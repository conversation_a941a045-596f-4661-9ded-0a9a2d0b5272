/// <reference types="cypress" />

const INITIAL_ONLY_FIELDS = [
    'branchId', 'borrowerFName', 'borrowerLName', 'borrowerEmail'
];

const SELECT_FIELDS = ['statusForeclosure'];

const valuesSet1 = {
    isBorUSCitizen: 'No',
    borOrigin: 'Brazil',
    borVisaStatus: 'Work Visa',
    isBorDecalredBankruptPastYears: 'Yes',
    personalBankruptcy: '1-3 months',
    bankruptcyTypes: ['Chapter 7', 'Chapter 13'],
    borDecalredBankruptExpln: 'Filed in 2022. Discharged.',
    isAnyBorOutstandingJudgements: 'Yes',
    borOutstandingJudgementsExpln: 'Credit card lawsuit settled 2023.',
    hasBorAnyActiveLawsuits: 'Yes',
    borActiveLawsuitsExpln: 'Pending civil litigation.',
    hasBorPropertyTaxLiens: 'Yes',
    borPropertyTaxLiensExpln: 'Lien satisfied, released 2021.',
    hasBorObligatedInForeclosure: 'Yes',
    statusForeclosure: '5',
    borObligatedInForeclosureExpln: 'Surrendered property in 2023.',
    isBorPresenltyDelinquent: 'Yes',
    borDelinquentExpln: 'Behind on student loan.',
    haveBorOtherFraudRelatedCrimes: 'Yes',
    borOtherFraudRelatedCrimesExpln: 'No fraud convictions.',
    borDesignatedBeneficiaryAgreement: 'Yes',
    borDesignatedBeneficiaryAgreementExpln: 'Civil union recognized.',
    completedPreForeclose: 'No',
    hasBorBeenForeclosed: 'No',
    previouslyHadShortSale: 'Yes',
    whenWasShortSale: '06/15/2022',
    explnCircumstancesOfShortSales: 'Short sale due to job loss.',
    borBackgroundExplanation: 'Moved for a new job; changed residence.',
    incomeTaxFiledDate: '04/15/2023',
    anyReturnsAudited: 'Yes',
    yearsAudited: '2021, 2022',
    haveYouDrawnWill: 'Yes',
    nameOfExecutorAndYearDrawn: 'Sam Lawyer, 2021',
    financialPlanPreparedForYou: 'No',
    lineOfCreditOrUnusedCredit: 'Yes',
    creditFacilityDetails: 'Chase Bank, $20k, John Banker',
    substantialInheritances: 'Yes',
    substantialInheritancesDetails: 'Some substantial inheritance',
};

const clearFieldsObj = {
    borOrigin: '', borVisaStatus: '', personalBankruptcy: '',
    bankruptcyTypes: [], borOutstandingJudgementsExpln: '',
    borDecalredBankruptExpln: '', borActiveLawsuitsExpln: '',
    borPropertyTaxLiensExpln: '', statusForeclosure: '',
    borObligatedInForeclosureExpln: '', borDelinquentExpln: '',
    borOtherFraudRelatedCrimesExpln: '', borDesignatedBeneficiaryAgreementExpln: '',
    borBackgroundExplanation: '', whenWasShortSale: '',
    explnCircumstancesOfShortSales: '', incomeTaxFiledDate: '',
    yearsAudited: '', nameOfExecutorAndYearDrawn: '',
    creditFacilityDetails: '', substantialInheritancesDetails: '',
};

const revisitLoanTab = (loanUrl, tab = 'CI') => {
    cy.visit(`${loanUrl}&tabOpt=${tab}`);
    cy.wait(4000);
};

function clearChosenMultiSelect(field) {
    cy.get(`#${field}_chosen .search-choice-close`).then($choices => {
        if ($choices.length > 0) {
            cy.wrap($choices).each($el => {
                cy.wrap($el).click({ force: true });
            });
        }
    });
    cy.get(`#${field}_chosen .search-choice`).should('not.exist');
    cy.get(`#${field}`).invoke('val').should('deep.equal', []);
}

function selectChosenSingle(selector, value) {
    cy.get(`${selector} + .chosen-container`).should('be.visible').click();
    cy.get('.chosen-drop .chosen-results li').contains(value).click();
}

function populateFields(values) {
    const radioFields = [
        'isBorUSCitizen', 'isBorDecalredBankruptPastYears', 'isAnyBorOutstandingJudgements',
        'hasBorAnyActiveLawsuits', 'hasBorPropertyTaxLiens', 'hasBorObligatedInForeclosure',
        'isBorPresenltyDelinquent', 'haveBorOtherFraudRelatedCrimes',
        'borDesignatedBeneficiaryAgreement', 'completedPreForeclose', 'hasBorBeenForeclosed',
        'previouslyHadShortSale', 'anyReturnsAudited', 'haveYouDrawnWill',
        'financialPlanPreparedForYou', 'lineOfCreditOrUnusedCredit', 'substantialInheritances'
    ];
    radioFields.forEach(field => {
        const val = values[field];
        if (val) cy.get(`input[type="radio"][name="${field}"][value="${val}"]`).check({ force: true });
    });

    Object.entries(values).forEach(([field, value]) => {
        if (INITIAL_ONLY_FIELDS.includes(field) || radioFields.includes(field)) return;
        if (field === 'bankruptcyTypes') {
            value.forEach(v => {
                cy.get(`#${field}`).parent().find('.chosen-container').click();
                cy.get('.chosen-drop .chosen-results li').contains(v).click();
            });
            return;
        }
        if (field === 'personalBankruptcy') {
            selectChosenSingle(`#${field}`, value);
            return;
        }
        if (SELECT_FIELDS.includes(field)) {
            cy.get(`#${field}`).select(value);
            return;
        }
        cy.get(`#${field}`).should('be.visible').clear().type(`${value}`);
    });
}

describe('Borrower Info Tab - background', () => {
    let loanUrl;

    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });
        cy.visit('/backoffice/dashboard');
        cy.createNewLoanBO('BorrowerInfoTabBackground Test');
        cy.location('href').then(href => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });
        cy.viewport(1920, 1080);
        cy.wait(250);
    });

	it('should perform full field persistence cycle on Borrower Info/CI Tab', () => {
		revisitLoanTab('CI');
		populateFields(valuesSet1);
		cy.get('#mailingAddress').invoke('val').then(val => {
		  cy.log('Mailing Address BEFORE Save:', val); // Check here!
		});
		cy.get('#saveBtn').click();
		cy.wait(4000);
		cy.get('#mailingAddress').invoke('val').then(val => {
		  cy.log('Mailing Address AFTER Save:', val); // Check here!
		});
	});


    it('should assert on borrower background - part 1', () => {
        revisitLoanTab(loanUrl,'CI');
        cy.wait(4000);
        const firstHalf = Object.entries(valuesSet1).slice(0, Math.ceil(Object.entries(valuesSet1).length / 2));
        firstHalf.forEach(([field, value]) => {
            cy.wait(10);
            if ([
                'isBorUSCitizen','isBorDecalredBankruptPastYears','isAnyBorOutstandingJudgements',
                'hasBorAnyActiveLawsuits','hasBorPropertyTaxLiens','hasBorObligatedInForeclosure',
                'isBorPresenltyDelinquent','haveBorOtherFraudRelatedCrimes','borDesignatedBeneficiaryAgreement',
                'completedPreForeclose','hasBorBeenForeclosed','previouslyHadShortSale','anyReturnsAudited',
                'haveYouDrawnWill','financialPlanPreparedForYou','lineOfCreditOrUnusedCredit','substantialInheritances'
            ].includes(field)) {
                cy.get(`input[type="radio"][name="${field}"][value="${value}"]`).should('be.checked');
            } else if (field === 'bankruptcyTypes') {
                value.forEach(v => {
                    cy.get(`#${field}_chosen .search-choice`).should('contain.text', v);
                });
                cy.get(`#${field}`).invoke('val').should('include.members', ['chp7', 'chp13']);
            } else if (field === 'personalBankruptcy') {
                cy.get(`#${field}`).should('contain.text', value);
            } else {
                cy.get(`#${field}`).should('have.value', value);
            }
        });
    });

    it('should assert on borrower background - part 2', () => {
        revisitLoanTab(loanUrl,'CI');
        const secondHalf = Object.entries(valuesSet1).slice(Math.ceil(Object.entries(valuesSet1).length / 2));
        secondHalf.forEach(([field, value]) => {
            cy.wait(10);
            if ([
                'isBorUSCitizen','isBorDecalredBankruptPastYears','isAnyBorOutstandingJudgements',
                'hasBorAnyActiveLawsuits','hasBorPropertyTaxLiens','hasBorObligatedInForeclosure',
                'isBorPresenltyDelinquent','haveBorOtherFraudRelatedCrimes','borDesignatedBeneficiaryAgreement',
                'completedPreForeclose','hasBorBeenForeclosed','previouslyHadShortSale','anyReturnsAudited',
                'haveYouDrawnWill','financialPlanPreparedForYou','lineOfCreditOrUnusedCredit','substantialInheritances'
            ].includes(field)) {
                cy.get(`input[type="radio"][name="${field}"][value="${value}"]`).should('be.checked');
            } else if (field === 'bankruptcyTypes') {
                value.forEach(v => {
                    cy.get(`#${field}_chosen .search-choice`).should('contain.text', v);
                });
                cy.get(`#${field}`).invoke('val').should('include.members', ['chp7', 'chp13']);
            } else if (field === 'personalBankruptcy') {
                cy.get(`#${field}`).should('contain.text', value);
            } else {
                cy.get(`#${field}`).should('have.value', value);
            }
        });
    });

    it('should flip all radio fields and save flipped state', () => {
        revisitLoanTab(loanUrl, 'CI');
        const radioFields = [
            'isBorUSCitizen','isBorDecalredBankruptPastYears','isAnyBorOutstandingJudgements',
            'hasBorAnyActiveLawsuits','hasBorPropertyTaxLiens','hasBorObligatedInForeclosure',
            'isBorPresenltyDelinquent','haveBorOtherFraudRelatedCrimes','borDesignatedBeneficiaryAgreement',
            'completedPreForeclose','hasBorBeenForeclosed','previouslyHadShortSale','anyReturnsAudited',
            'haveYouDrawnWill','financialPlanPreparedForYou','lineOfCreditOrUnusedCredit','substantialInheritances'
        ];
        const flippedValues = {};
        const flip = v => (v === 'Yes' ? 'No' : 'Yes');

        radioFields.forEach(field => {
            flippedValues[field] = flip(valuesSet1[field]);
        });

        populateFields(flippedValues);
        cy.get('#saveBtn').click();
        cy.wait(8000);
    });

    it('should assert radio fields were flipped correctly', () => {
        revisitLoanTab(loanUrl, 'CI');
        const radioFields = [
            'isBorUSCitizen','isBorDecalredBankruptPastYears','isAnyBorOutstandingJudgements',
            'hasBorAnyActiveLawsuits','hasBorPropertyTaxLiens','hasBorObligatedInForeclosure',
            'isBorPresenltyDelinquent','haveBorOtherFraudRelatedCrimes','borDesignatedBeneficiaryAgreement',
            'completedPreForeclose','hasBorBeenForeclosed','previouslyHadShortSale','anyReturnsAudited',
            'haveYouDrawnWill','financialPlanPreparedForYou','lineOfCreditOrUnusedCredit','substantialInheritances'
        ];
        const flip = v => (v === 'Yes' ? 'No' : 'Yes');

        radioFields.forEach(field => {
            const expected = flip(valuesSet1[field]);
            cy.get(`input[type="radio"][name="${field}"][value="${expected}"]`).should('be.checked');
        });
    });
});
