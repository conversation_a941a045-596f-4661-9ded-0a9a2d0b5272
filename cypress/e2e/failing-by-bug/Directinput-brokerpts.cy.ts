describe('Total Project Soft & Hard Cost - A/B Suite', () => {
    let loanUrl: string;
    function parseFormattedNumber(val) {
        return parseFloat(val.replace(/[^0-9.-]+/g, '')) || 0;
    }

    const phaseA = {
        costBasis: 5050000,
        maxAmtToPutDown: 210000,
        rehabCost: 123000,
        rehabFinancedPercent: 100,
        closingCost: 11500,
        costSpent: 88500,
        originationPointsValue: 12000,
        brokerPointsValue: 11000,
        bufferAndMessengerFee: 1000,
        travelNotaryFee: 1001,
        escrowFees: 1002,
        attorneyFee: 1003,
        closingCostFinancingFee: 1004,
        appraisalFee: 1005,
        applicationFee: 1006,
        processingFee: 1007,
        estdTitleClosingFee: 1008,
        brokerProcessingFee: 1009,
        drawsSetUpFee: 1010,
        drawsFee: 1011,
        valuationBPOFee: 1012,
        valuationAVMFee: 1013,
        valuationAVEFee: 1014,
        valuationCMAFee: 1015,
        creditReportFee: 1016,
        creditCheckFee: 1017,
        taxServiceFee: 1018,
        backgroundCheckFee: 1019,
        wireFee: 1020,
        documentPreparationFee: 1021,
        floodCertificateFee: 1022,
        servicingSetUpFee: 1023,
        inspectionFees: 1024,
        floodServiceFee: 1025,
        dueDiligence: 1026,
        projectFeasibility: 1027,
        thirdPartyFees: 1028,
        UccLienSearch: 1029,
        underwritingFees: 1030,
        otherFee: 1031,
        prePaidInterest: 1032,
        recordingFee: 1033,
        insurancePremium: 1034,
        propertyTax: 1035,
        wireTransferFeeToTitle: 1036,
        realEstateTaxes: 1037,
        pastDuePropertyTaxes: 1038,
        payOffLiensCreditors: 1039,
        wholeSaleAdminFee: 1040,
        wireTransferFeeToEscrow: 1041,
        employmentVerificationFee: 1042,
        survey: 1043,
        taxReturnOrderFee: 1044,
        cityCountyTaxStamps: 1045,
        taxImpoundsFee: 1046,
        constructionHoldbackFee: 1047,
    };

    const phaseB = {
        costBasis: 4710000,
        maxAmtToPutDown: 250000,
        rehabCost: 96000,
        rehabFinancedPercent: 90,
        closingCost: 14800,
        costSpent: 76500,
        originationPointsValue: 13000,
        brokerPointsValue: 12000,
        bufferAndMessengerFee: 1001,
        travelNotaryFee: 1002,
        escrowFees: 1003,
        attorneyFee: 1004,
        closingCostFinancingFee: 1005,
        appraisalFee: 1006,
        applicationFee: 1007,
        processingFee: 1008,
        estdTitleClosingFee: 1009,
        brokerProcessingFee: 1010,
        drawsSetUpFee: 1011,
        drawsFee: 1012,
        valuationBPOFee: 1013,
        valuationAVMFee: 1014,
        valuationAVEFee: 1015,
        valuationCMAFee: 1016,
        creditReportFee: 1017,
        creditCheckFee: 1018,
        taxServiceFee: 1019,
        backgroundCheckFee: 1020,
        wireFee: 1021,
        documentPreparationFee: 1022,
        floodCertificateFee: 1023,
        servicingSetUpFee: 1024,
        inspectionFees: 1025,
        floodServiceFee: 1026,
        dueDiligence: 1027,
        projectFeasibility: 1028,
        thirdPartyFees: 1029,
        UccLienSearch: 1030,
        underwritingFees: 1031,
        otherFee: 1032,
        prePaidInterest: 1033,
        recordingFee: 1034,
        insurancePremium: 1035,
        propertyTax: 1036,
        wireTransferFeeToTitle: 1037,
        realEstateTaxes: 1038,
        pastDuePropertyTaxes: 1039,
        payOffLiensCreditors: 1040,
        wholeSaleAdminFee: 1041,
        wireTransferFeeToEscrow: 1042,
        employmentVerificationFee: 1043,
        survey: 1044,
        taxReturnOrderFee: 1045,
        cityCountyTaxStamps: 1046,
        taxImpoundsFee: 1047,
        constructionHoldbackFee: 1048,
    };

    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.visit('/backoffice/dashboard');
        cy.createNewLoanBO('DirectInput-Brokerpts Test');

        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.viewport(1920, 1080);
        cy.wait(250);
    });

    const revisitLoanTab = (tab = 'HMLI') => {
        cy.visit(`${loanUrl}&tabOpt=${tab}`);
    };

    const specialFields = [
        'costBasis',
        'maxAmtToPutDown',
        'rehabCost',
        'rehabFinancedPercent',
        'closingCost',
        'costSpent'
    ];

    const fillForm = (data) => {
        cy.get('#typeOfHMLOLoanRequesting').select('Purchase');
        cy.get('#costBasis').clear().type(data.costBasis.toString());
        cy.get('#propertyNeedRehabYes').check({ force: true });
        cy.get('#maxAmtToPutDown').clear().type(data.maxAmtToPutDown.toString());
        cy.get('#downPaymentPercentage').click();
        cy.get('#rehabCost').clear().type(data.rehabCost.toString());
        cy.get('#rehabCostPercentageFinanced').clear().type(`${data.rehabFinancedPercent}`, { force: true });
        cy.get('td.doesPropertyNeedRehabDispDiv label').click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(1) label:nth-of-type(1) > span").click();
        cy.get("tr:nth-of-type(4) > td:nth-of-type(1) > div > div:nth-of-type(2) label:nth-of-type(1) > span").click();
        cy.get('#initialAdvance').click();
        cy.get('#closingCostFinanced').clear().type(data.closingCost.toString());
        cy.get('#costSpent').clear().type(data.costSpent.toString());
        cy.get('td.px-0 label').click();

        Object.keys(data).forEach((key) => {
            if (!specialFields.includes(key)) {
                cy.get(`#${key}`).click();
                cy.get(`#${key}`).clear().type(data[key].toString());
            }
        });
    };

    const assertSavedInputs = (data) => {
        assertCurrencyInput('#costBasis', data.costBasis);
        assertCurrencyInput('#rehabCost', data.rehabCost);
        assertCurrencyInput('#costSpent', data.costSpent);

        Object.keys(data).forEach((key) => {
            if (!specialFields.includes(key)) {
                assertCurrencyInput(`#${key}`, data[key]);
            }
        });
    };

    const assertCurrencyInput = (selector: string, expected: number) => {
        cy.get(selector).invoke('val').then(raw => {
            const actual = parseFormattedNumber(raw);
            cy.log(`${selector} expected: ${expected}, actual: ${actual}`);
            expect(actual).to.eq(expected);
        });
    };

    const assertFinancedPercentage = (data) => {
        const expected = data.costBasis + data.rehabCost + data.costSpent;
        cy.get('#totalProjectCost').invoke('text').then(text => {
            const actual = parseFormattedNumber(text);
            cy.log(`Expected: ${expected}`);
            cy.log(`Actual: ${actual}`);
            expect(actual).to.eq(expected);
        });
    };

    it('JS Test - Phase A Input + Calculation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseA);
        assertFinancedPercentage(phaseA);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase A Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseA);
        assertFinancedPercentage(phaseA);
    });

    it('JS Test - Phase B Input + Re-Calculation', () => {
        revisitLoanTab('HMLI');
        fillForm(phaseB);
        assertFinancedPercentage(phaseB);
        cy.get('#saveBtn').click();
        cy.wait(4000);
    });

    it('PHP Test - Phase B Validation', () => {
        revisitLoanTab('HMLI');
        assertSavedInputs(phaseB);
        assertFinancedPercentage(phaseB);
    });
});
