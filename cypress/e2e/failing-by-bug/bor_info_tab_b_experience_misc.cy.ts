/// <reference types="cypress" />

const SKIP_CLEAR_FIELDS = [
    // Add any fields you want to skip clearing/asserting here that are failing the test and need repairing in codebase
    'borNoOfProjectCurrently',
    'borNoOfOwnProp',
    'borPrimaryInvestmentStrategy',
];

function assertNoComma(selector, expected) {
    cy.get(selector).invoke('val').then(actual => {
        const actualNum = parseFloat((actual || '').replace(/,/g, '')) || 0;
        const expectedNum = parseFloat((expected || '').replace(/,/g, '')) || 0;
        expect(actualNum).to.eq(expectedNum);
    });
}


describe('Borrower Experience - Sell Section', () => {
    let loanUrl;
    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });
        cy.visit('/backoffice/dashboard');
        cy.createNewLoanBO('borrower sell test');
        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });
    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });
        cy.viewport(1920, 1080);
        cy.wait(250);
    });
    const revisitLoanTab = (tab = 'CI') => cy.visit(`${loanUrl}&tabOpt=${tab}`);

    it('should populate additional experience fields and save', () => {
        revisitLoanTab('CI');
        cy.wait(4000);

        cy.get("div:nth-of-type(11) div:nth-of-type(4) label:nth-of-type(1) > span").click();
        cy.get("#borNoOfProjectCurrently").clear().type("5");

        cy.get("div:nth-of-type(11) div:nth-of-type(5) label:nth-of-type(1) > span").click();
        cy.get("div.borOwnInvestmentPropertiesDiv > div > div > div").click();
        cy.get("#borNoOfOwnProp").clear().type("4");

        cy.get("div:nth-of-type(11) div:nth-of-type(6) label:nth-of-type(1) > span").click();
        cy.get("#borClubName").clear().type("3");

        cy.get("div:nth-of-type(11) div:nth-of-type(7) label:nth-of-type(1) > span").click();
        cy.get("#borProfLicence").select("Real Estate");
        cy.get("#borLicenseNo").clear().type("a1718625");

        cy.get("div:nth-of-type(11) div:nth-of-type(8) label:nth-of-type(1) > span").click();
        cy.get("#liquidAssets").clear().type("250000");

        cy.get("div.overallRealEstateInvesExp_disp > label").click();
        cy.get("div.borPrimaryInvestmentStrategy_disp input").click();
        cy.get('#borPrimaryInvestmentStrategy').select(['Residentialfixflip', 'Residentialbuyhold'], { force: true });
        cy.get("#overallRealEstateInvesExp").clear().type("to make money i sold lots of props");
        cy.get("#borPrimaryInvestmentStrategyExplain").clear().type("prim investment explanation");

        cy.get('#saveBtn').click();
        cy.wait(1500);
    });
    it('should assert values, then clear children and save', () => {
        revisitLoanTab('CI');
        cy.wait(2000);

        cy.get("div:nth-of-type(11) div:nth-of-type(4) label:nth-of-type(1) > input").should('be.checked');
        cy.get("#borNoOfProjectCurrently").should('have.value', "5");
        cy.get("div:nth-of-type(11) div:nth-of-type(5) label:nth-of-type(1) > input").should('be.checked');
        cy.get("#borNoOfOwnProp").should('have.value', "4");
        cy.get("div:nth-of-type(11) div:nth-of-type(6) label:nth-of-type(1) > input").should('be.checked');
        cy.get("#borClubName").should('have.value', "3");
        cy.get("div:nth-of-type(11) div:nth-of-type(7) label:nth-of-type(1) > input").should('be.checked');
        cy.get("#borProfLicence").should('have.value', "Real Estate");
        cy.get("#borLicenseNo").should('have.value', "a1718625");
        cy.get("div:nth-of-type(11) div:nth-of-type(8) label:nth-of-type(1) > input").should('be.checked');
        assertNoComma('#liquidAssets', '250000');
        cy.get('#borPrimaryInvestmentStrategy').invoke('val').should('deep.eq', ['Residentialfixflip', 'Residentialbuyhold']);
        cy.get("#overallRealEstateInvesExp").should('have.value', "to make money i sold lots of props");
        cy.get("#borPrimaryInvestmentStrategyExplain").should('have.value', "prim investment explanation");

        // Clear children except SKIP_CLEAR_FIELDS
        const fieldsToClear = [
            "#borNoOfProjectCurrently",
            "#borNoOfOwnProp",
            "#borClubName",
            "#borProfLicence",
            "#borLicenseNo",
            "#liquidAssets",
            "#borPrimaryInvestmentStrategy",
            "#overallRealEstateInvesExp",
            "#borPrimaryInvestmentStrategyExplain"
        ];
        fieldsToClear.forEach(selector => {
            const fieldName = selector.replace(/^#/, "");
            if (!SKIP_CLEAR_FIELDS.includes(fieldName)) {
                if (selector === "#borProfLicence") {
                    cy.get(selector).select("");
                } else if (selector === "#borPrimaryInvestmentStrategy") {
                    cy.window().then(win => {
                        const select = win.document.getElementById('borPrimaryInvestmentStrategy');
                        if (select) {
                            Array.from(select.options).forEach(option => option.selected = false);
                            select.dispatchEvent(new Event('change', { bubbles: true }));
                            if (win.$) win.$(select).trigger('chosen:updated');
                        }
                    });
                } else {
                    cy.get(selector).clear();
                }
            }
        });

        cy.get('#saveBtn').click();
        cy.wait(1200);
    });
    it('should assert children values are cleared/nulled', () => {
        revisitLoanTab('CI');
        cy.wait(1500);

        const fieldsToClear = [
            "#borNoOfProjectCurrently",
            "#borNoOfOwnProp",
            "#borClubName",
            "#borProfLicence",
            "#borLicenseNo",
            "#liquidAssets",
            "#borPrimaryInvestmentStrategy",
            "#overallRealEstateInvesExp",
            "#borPrimaryInvestmentStrategyExplain"
        ];
        fieldsToClear.forEach(selector => {
            const fieldName = selector.replace(/^#/, "");
            if (!SKIP_CLEAR_FIELDS.includes(fieldName)) {
                if (selector === "#borProfLicence") {
                    cy.get(selector).should('have.value', "");
                } else if (selector === "#borPrimaryInvestmentStrategy") {
                    cy.get(selector).invoke('val').should('deep.eq', []);
                    cy.get('#borPrimaryInvestmentStrategy_chosen .search-choice').should('not.exist');
                } else if (selector === "#liquidAssets") {
                    assertNoComma(selector, '');
                } else {
                    cy.get(selector).should('have.value', "");
                }
            }
        });
    });
    it('should toggle parent radios to No and assert', () => {
        revisitLoanTab('CI');
        cy.wait(1000);

        cy.get("div:nth-of-type(11) div:nth-of-type(4) label:nth-of-type(2) > span").click();
        cy.get("div:nth-of-type(11) div:nth-of-type(5) label:nth-of-type(2) > span").click();
        cy.get("div:nth-of-type(11) div:nth-of-type(6) label:nth-of-type(2) > span").click();
        cy.get("div:nth-of-type(11) div:nth-of-type(7) label:nth-of-type(2) > span").click();
        cy.get("div:nth-of-type(11) div:nth-of-type(8) label:nth-of-type(2) > span").click();

        cy.get('#saveBtn').click();
        cy.wait(1200);

        cy.get("div:nth-of-type(11) div:nth-of-type(4) label:nth-of-type(2) > input").should('be.checked');
        cy.get("div:nth-of-type(11) div:nth-of-type(5) label:nth-of-type(2) > input").should('be.checked');
        cy.get("div:nth-of-type(11) div:nth-of-type(6) label:nth-of-type(2) > input").should('be.checked');
        cy.get("div:nth-of-type(11) div:nth-of-type(7) label:nth-of-type(2) > input").should('be.checked');
        cy.get("div:nth-of-type(11) div:nth-of-type(8) label:nth-of-type(2) > input").should('be.checked');
    });

});
