describe('Email Templates Load', () => {
    let loanUrl: string;

    before(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.visit('/backoffice/dashboard');
        cy.createNewLoanBO('Email Template Loads Test');

        cy.location('href').then((href) => {
            loanUrl = href.replace(/&tabOpt=[^&]+/, '');
        });
    });

    beforeEach(() => {
        cy.session('backoffice-login', () => {
            cy.viewport(1920, 1080);
            cy.loginBackoffice();
        });

        cy.viewport(1920, 1080);
        cy.wait(250);
    });


    const revisitLoanTab = (tab = 'HMLI') => {
        cy.visit(`${loanUrl}&tabOpt=${tab}`);
    };


    it('Email Templates Loading Test', () => {
        revisitLoanTab('HMLI');
        cy.get("#sendEmail").click();
        cy.get("#sendEmailNewForm > div:nth-of-type(1) > div:nth-of-type(3) span").click();
        cy.get("li.highlighted").click();

        cy.get('#emailMessage_ifr', { timeout: 10000 })
            .should('exist')
            .then(($iframe) => {
                const $body = $iframe[0].contentDocument.body;

                // Retry until body is populated
                cy.wrap($body, { timeout: 10000 })
                    .should('not.be.empty')
                    .then(cy.wrap)
                    .invoke('text')
                    // .should('include', 'This is a test for the Emailtest.  This was sent from file Dave Eschmeyer ');
                    .should('include', 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX');
            });
    });
});
