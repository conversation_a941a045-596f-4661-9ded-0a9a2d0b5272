<?php

namespace models;


use models\types\strongType;

/**
 * Class Log
 */
class Log extends strongType
{
    private static ?LogFile $_log_file = null;

    /**
     *
     */
    private static function _init()
    {
        if (is_null(self::$_log_file)) {
            self::$_log_file = new LogFile();
        }
    }

    /**
     * @param $message
     * @param bool $echo
     * @param bool $write_to_file
     * @param string $prefix
     */
    public static function Insert($message, bool $echo = true, bool $write_to_file = true, string $prefix = '')
    {
        self::_init();
        if (!defined('GUID')) {
            return;
        }

        self::$_log_file->Insert($_SERVER['SCRIPT_FILENAME'], $message, $echo, $write_to_file, $prefix);
    }

    public static function Print($message)
    {
        self::Insert($message, true, false);
    }

    public static function File($message)
    {
        self::Insert($message);
    }
}