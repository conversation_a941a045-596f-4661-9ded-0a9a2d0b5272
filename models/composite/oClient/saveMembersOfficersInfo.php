<?php

namespace models\composite\oClient;

use models\Database2;
use models\cypher;
use models\FileStorage;
use models\lendingwise\db\tblMembersOfficers_db;
use models\lendingwise\tblBorrowerEntityDocs;
use models\lendingwise\tblBorrowerEntityMemberDocs;
use models\lendingwise\tblMembersOfficers;
use models\lendingwise\tblPCClientEntityMembers;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

/**
 *
 */
class saveMembersOfficersInfo extends strongType
{
    /**
     * @param $ip
     * @return int
     */
    public static function getReport_old($ip): int
    {
        $memberName = [];
        $appComma = '';
        $recordDate = Dates::Timestamp();
        $memberTitle = [];
        $memberOwnership = [];
        $memberAnnualSalary = [];
        $memberAddress = [];
        $memberPhone = [];
        $memberCell = [];
        $memberSSN = [];
        $memberDOB = [];
        $memberCreditScore = [];
        $memberEmail = [];
        $memberDriversLicenseState = $memberDriversLicense = [];
        $memberTIN = [];
        $cnt = 0;
        $CID = cypher::myDecryption(Arrays::getArrayValue('encCID', $ip));
        $CBEID = Arrays::getArrayValue('CBEID', $ip);

        if ($CID == 0) return $cnt;

        $qss = 0;

        if (array_key_exists('memberName', $_REQUEST)) $memberName = $_REQUEST['memberName'];
        if (array_key_exists('memberTitle', $_REQUEST)) $memberTitle = $_REQUEST['memberTitle'];
        if (array_key_exists('memberOwnership', $_REQUEST)) $memberOwnership = $_REQUEST['memberOwnership'];
        if (array_key_exists('memberAnnualSalary', $_REQUEST)) $memberAnnualSalary = $_REQUEST['memberAnnualSalary'];
        if (array_key_exists('memberAddress', $_REQUEST)) $memberAddress = $_REQUEST['memberAddress'];
        if (array_key_exists('memberPhone', $_REQUEST)) $memberPhone = $_REQUEST['memberPhone'];
        if (array_key_exists('memberCell', $_REQUEST)) $memberCell = $_REQUEST['memberCell'];
        if (array_key_exists('memberSSN', $_REQUEST)) $memberSSN = $_REQUEST['memberSSN'];
        if (array_key_exists('memberDOB', $_REQUEST)) $memberDOB = $_REQUEST['memberDOB'];
        if (array_key_exists('memberCreditScore', $_REQUEST)) $memberCreditScore = $_REQUEST['memberCreditScore'];
        if (array_key_exists('memberEmail', $_REQUEST)) $memberEmail = $_REQUEST['memberEmail'];
        if (array_key_exists('memberDriversLicense', $_REQUEST)) $memberDriversLicense = $_REQUEST['memberDriversLicense'];
        if (array_key_exists('memberDriversLicenseState', $_REQUEST)) $memberDriversLicenseState = $_REQUEST['memberDriversLicenseState'];
        if (array_key_exists('memberTin', $_REQUEST)) $memberTIN = $_REQUEST['memberTin'];

        $entityMembersDetails = $ip['members'] ?? [];

        $tblMembersOfficers = tblMembersOfficers::GetAll(['CBEID' => $CBEID]);
        if (count($tblMembersOfficers)) {
            foreach ($tblMembersOfficers as $tblMembersOfficer) {
                $tblMembersOfficer->delete();
            }
        }

        /*$qry = ' INSERT INTO tblMembersOfficers (CBEID
                , CID
                , memberType
                , memberName
                , memberTitle
                , memberOwnership
                , memberAnnualSalary
                , memberAddress
                , memberPhone
                , memberCell
                , memberSSN
                , memberDOB
                , memberCreditScore
                , memberEmail
                , recordDate
                , memberDriversLicenseState
                , memberDriversLicense
                , memberPersonalGuarantee
                , memberAuthorizedSigner
                , memberCitizenship
                , memberTin
                ) VALUES ';*/
        if (Arrays::getArrayValue('from', $ip) == 'file') {
            for ($mo = 0; $mo < 10; $mo++) {
                $memberType = Request::GetClean('memberType_' . $mo) ?? '';
                $memberPersonalGuarantee = Request::GetClean('memberPersonalGuarantee_' . $mo) ?? '';
                $memberAuthorizedSigner = Request::GetClean('memberAuthorizedSigner_' . $mo) ?? '';
                $memberCitizenship = Request::GetClean('memberCitizenship_' . $mo) ?? '';
                if ($_REQUEST['memberName'][$mo] != '') {
                    /*$qry .= $appComma . '(' . $CBEID . '
                    , ' . $CID . "
                    , '" . $memberType . "'
                    , '" . trim($_REQUEST['memberName'][$mo]) . "'
                    , '" . trim($_REQUEST['memberTitle'][$mo]) . "'
                    , '" . $_REQUEST['memberOwnership'][$mo] . "'
                    , '" . $_REQUEST['memberAnnualSalary'][$mo] . "'
                    , '" . trim($_REQUEST['memberAddress'][$mo]) . "'
                    , '" . Strings::cleanPhoneNo($_REQUEST['memberPhone'][$mo]) . "'
                    , '" . Strings::cleanPhoneNo($_REQUEST['memberCell'][$mo]) . "'
                    , '" . Strings::cleanPhoneNo($_REQUEST['memberSSN'][$mo]) . "'
                    , '" . Dates::formatDateWithRE($_REQUEST['memberDOB'][$mo], 'MDY', 'Y-m-d') . "'
                    , '" . trim($_REQUEST['memberCreditScore'][$mo]) . "'
                    , '" . trim($_REQUEST['memberEmail'][$mo]) . "'
                    , '" . $recordDate . "'
                    , '" . trim($_REQUEST['memberDriversLicenseState'][$mo]) . "'
                    , '" . trim($_REQUEST['memberDriversLicense'][$mo]) . "'
                    , '" . $memberPersonalGuarantee . "'
                    , '" . $memberAuthorizedSigner . "'
                    , '" . $memberCitizenship . "'
                    , '" . trim($_REQUEST['memberTin'][$mo]) . "'
                    )";
                    $appComma = ',';*/
                    $qss++;
                    //New ORM Format
                    $tblMembersOfficers = new tblMembersOfficers();
                    $tblMembersOfficers->CID = $CID;
                    $tblMembersOfficers->CBEID = $CBEID;
                    $tblMembersOfficers->memberType = $memberType;
                    $tblMembersOfficers->memberName = trim($_REQUEST['memberName'][$mo]);
                    $tblMembersOfficers->memberTitle = trim($_REQUEST['memberTitle'][$mo]);
                    $tblMembersOfficers->memberOwnership = intval($_REQUEST['memberOwnership'][$mo]);
                    $tblMembersOfficers->memberAnnualSalary = Strings::replaceCommaValues($_REQUEST['memberAnnualSalary'][$mo]);
                    $tblMembersOfficers->memberAddress = trim($_REQUEST['memberAddress'][$mo]);
                    $tblMembersOfficers->memberPhone = Strings::cleanPhoneNo($_REQUEST['memberPhone'][$mo]);
                    $tblMembersOfficers->memberCell = Strings::cleanPhoneNo($_REQUEST['memberCell'][$mo]);
                    $tblMembersOfficers->memberSSN = Strings::cleanPhoneNo($_REQUEST['memberSSN'][$mo]);
                    $tblMembersOfficers->memberDOB = Dates::formatDateWithRE($_REQUEST['memberDOB'][$mo], 'MDY', 'Y-m-d');
                    $tblMembersOfficers->memberCreditScore = trim($_REQUEST['memberCreditScore'][$mo]);
                    $tblMembersOfficers->memberEmail = trim($_REQUEST['memberEmail'][$mo]);
                    $tblMembersOfficers->recordDate = $recordDate;
                    $tblMembersOfficers->memberDriversLicenseState = trim($_REQUEST['memberDriversLicenseState'][$mo]);
                    $tblMembersOfficers->memberDriversLicense = trim($_REQUEST['memberDriversLicense'][$mo]);
                    $tblMembersOfficers->memberPersonalGuarantee = $memberPersonalGuarantee;
                    $tblMembersOfficers->memberAuthorizedSigner = $memberAuthorizedSigner;
                    $tblMembersOfficers->memberCitizenship = $memberCitizenship;
                    $tblMembersOfficers->memberTin = intval(trim($_REQUEST['memberTin'][$mo]));
                    $tblMembersOfficers->Save();
                }
            }
//            $cnt2 = 0;
//            if ($qss > 0) $cnt2 = Database2::getInstance()->insert($qry);
            return $qss;
        } else {
            for ($mo = 0; $mo < count($memberName); $mo++) {
                if (trim($memberName[$mo]) != '') {
                    $memberType = Request::GetClean('memberType_' . $mo) ?? '';
                    $memberPersonalGuarantee = Request::GetClean('memberPersonalGuarantee_' . $mo) ?? '';
                    $memberAuthorizedSigner = Request::GetClean('memberAuthorizedSigner_' . $mo) ?? '';
                    $memberCitizenship = Request::GetClean('memberCitizenship_' . $mo) ?? '';
                    $tblMembersOfficers = new tblMembersOfficers();
                    $tblMembersOfficers->CID = $CID;
                    $tblMembersOfficers->CBEID = $CBEID;
                    $tblMembersOfficers->memberType = $memberType;
                    $tblMembersOfficers->memberName = trim($memberName[$mo]);
                    $tblMembersOfficers->memberTitle = trim($memberTitle[$mo]);
                    $tblMembersOfficers->memberOwnership = intval($memberOwnership[$mo]);
                    $tblMembersOfficers->memberAnnualSalary = Strings::replaceCommaValues($memberAnnualSalary[$mo]);
                    $tblMembersOfficers->memberAddress = trim($memberAddress[$mo]);
                    $tblMembersOfficers->memberPhone = Strings::cleanPhoneNo($memberPhone[$mo]);
                    $tblMembersOfficers->memberCell = Strings::cleanPhoneNo($memberCell[$mo]);
                    $tblMembersOfficers->memberSSN = Strings::cleanPhoneNo($memberSSN[$mo]);
                    $tblMembersOfficers->memberDOB = Dates::formatDateWithRE($memberDOB[$mo], 'MDY', 'Y-m-d');
                    $tblMembersOfficers->memberCreditScore = trim($memberCreditScore[$mo]);
                    $tblMembersOfficers->memberEmail = trim($memberEmail[$mo]);
                    $tblMembersOfficers->recordDate = $recordDate;
                    $tblMembersOfficers->memberDriversLicenseState = trim($memberDriversLicenseState[$mo]);
                    $tblMembersOfficers->memberDriversLicense = trim($memberDriversLicense[$mo]);
                    $tblMembersOfficers->memberPersonalGuarantee = $memberPersonalGuarantee;
                    $tblMembersOfficers->memberAuthorizedSigner = $memberAuthorizedSigner;
                    $tblMembersOfficers->memberCitizenship = $memberCitizenship;
                    $tblMembersOfficers->memberTin = intval(trim($memberTIN[$mo]));
                    $tblMembersOfficers->Save();
                    if ($tblMembersOfficers->MOID && $memberType === 'Entity') {
                        //Save Borrower Profile Entity Member Details
                        self::saveClientEntityMembers($tblMembersOfficers->MOID, $CID, $CBEID, $entityMembersDetails[$mo]['entityMember']);
                    }
                }
            }
        }
        return $tblMembersOfficers->CID;
    }

    /*public static function saveClientEntityMembers($MOID, $CID, $CBEID, $entityMembersDetails)
    {
        //Delete old data
        $tblPCClientEntityMembers = tblPCClientEntityMembers::GetAll(['MOID' => $MOID]);
        if (count($tblPCClientEntityMembers)) {
            foreach ($tblPCClientEntityMembers as $tblPCClientEntityMember) {
                $tblPCClientEntityMember->delete();
            }
        }
        foreach ($entityMembersDetails as $entityMembersDetail) {
            $entityMember = array_filter($entityMembersDetail); // remove empty post values
            if ($entityMember) {
                $tblPCClientEntityMembers = new tblPCClientEntityMembers();
                $tblPCClientEntityMembers->MOID = $MOID;
                $tblPCClientEntityMembers->CID = $CID;
                $tblPCClientEntityMembers->CBEID = $CBEID;
                $tblPCClientEntityMembers->clientEntityMemberName = $entityMember['entityMemberName'] ?? $tblPCClientEntityMembers->clientEntityMemberName;
                $tblPCClientEntityMembers->clientEntityMemberTitle = $entityMember['entityMemberTitle'] ?? $tblPCClientEntityMembers->clientEntityMemberTitle;
                $tblPCClientEntityMembers->clientEntityMemberOwnership = intval($entityMember['entityMemberOwnership']) ?? $tblPCClientEntityMembers->clientEntityMemberOwnership;
                $tblPCClientEntityMembers->clientEntityMemberPhone = Strings::cleanPhoneNo($entityMember['entityMemberPhone']) ?? $tblPCClientEntityMembers->clientEntityMemberPhone;
                $tblPCClientEntityMembers->clientEntityMemberCell = Strings::cleanPhoneNo($entityMember['entityMemberCell']) ?? $tblPCClientEntityMembers->clientEntityMemberCell;
                $tblPCClientEntityMembers->clientEntityMemberSSN = Strings::cleanPhoneNo($entityMember['entityMemberSSN']) ?? $tblPCClientEntityMembers->clientEntityMemberSSN;
                $tblPCClientEntityMembers->clientEntityMemberDOB = Dates::formatDateWithRE($entityMember['entityMemberDOB'], 'MDY', 'Y-m-d') ?? $tblPCClientEntityMembers->clientEntityMemberDOB;
                $tblPCClientEntityMembers->clientEntityMemberEmail = $entityMember['entityMemberEmail'] ?? $tblPCClientEntityMembers->clientEntityMemberEmail;
                $tblPCClientEntityMembers->clientEntityMemberCitizenship = $entityMember['entityMemberCitizenship'] ?? $tblPCClientEntityMembers->clientEntityMemberCitizenship;
                $tblPCClientEntityMembers->clientEntityMemberPersonalGuarantee = $entityMember['entityMemberPersonalGuarantee'] ?? $tblPCClientEntityMembers->clientEntityMemberPersonalGuarantee;
                $tblPCClientEntityMembers->clientEntityMemberAuthorizedSigner = $entityMember['entityMemberAuthorizedSigner'] ?? $tblPCClientEntityMembers->clientEntityMemberAuthorizedSigner;
                $tblPCClientEntityMembers->Save();
            }
        }
    }*/

    public static function getReport($ip): ?int
    {

        $appComma = '';
        $recordDate = Dates::Timestamp();
        $memberName = [];
        $memberTitle = [];
        $memberOwnership = [];
        $memberAnnualSalary = [];
        $memberAddress = [];
        $memberPhone = [];
        $memberCell = [];
        $memberSSN = [];
        $memberDOB = [];
        $memberCreditScore = [];
        $memberEmail = [];
        $memberDriversLicense = [];
        $memberDriversLicenseState = [];
        $memberTin = [];
        $memberPersonalGuarantee = [];
        $memberAuthorizedSigner = [];
        $memberCitizenship = [];
        $memberMaritalStatus = [];
        $memberMarriageDate = [];
        $memberDivorceDate = [];
        $memberMaidenName = [];
        $memberSpouseName = [];
        $memberCreditScoreDate = [];
        $memberRentOrOwn = [];
        $memberMonthlyRentOrMortgage = [];
        $memberDateMovedAddress = [];
        $memberRealEstateValue = [];
        $memberRetirementAccountBalance = [];
        $memberCashSavingsStocksBalance = [];
        $memberCreditCardBalance = [];
        $memberMortgageBalance = [];
        $memberAutoLoanBalance = [];

        $cnt = 0;

        $CID = cypher::myDecryption(Arrays::getArrayValue('encCID', $ip));
        $CBEID = Arrays::getArrayValue('CBEID', $ip);

        if ($CID == 0) return $cnt;

        $qss = 0;

        if (array_key_exists('memberName', $_REQUEST)) $memberName = $_REQUEST['memberName'];
        if (array_key_exists('memberTitle', $_REQUEST)) $memberTitle = $_REQUEST['memberTitle'];
        if (array_key_exists('memberOwnership', $_REQUEST)) $memberOwnership = $_REQUEST['memberOwnership'];
        if (array_key_exists('memberAnnualSalary', $_REQUEST)) $memberAnnualSalary = $_REQUEST['memberAnnualSalary'];
        if (array_key_exists('memberAddress', $_REQUEST)) $memberAddress = $_REQUEST['memberAddress'];
        if (array_key_exists('memberPhone', $_REQUEST)) $memberPhone = $_REQUEST['memberPhone'];
        if (array_key_exists('memberCell', $_REQUEST)) $memberCell = $_REQUEST['memberCell'];
        if (array_key_exists('memberSSN', $_REQUEST)) $memberSSN = $_REQUEST['memberSSN'];
        if (array_key_exists('memberDOB', $_REQUEST)) $memberDOB = $_REQUEST['memberDOB'];
        if (array_key_exists('memberCreditScore', $_REQUEST)) $memberCreditScore = $_REQUEST['memberCreditScore'];
        if (array_key_exists('memberEmail', $_REQUEST)) $memberEmail = $_REQUEST['memberEmail'];
        if (array_key_exists('memberDriversLicense', $_REQUEST)) $memberDriversLicense = $_REQUEST['memberDriversLicense'];
        if (array_key_exists('memberDriversLicenseState', $_REQUEST)) $memberDriversLicenseState = $_REQUEST['memberDriversLicenseState'];
        if (array_key_exists('memberTin', $_REQUEST)) $memberTin = $_REQUEST['memberTin'];
        //radio buttons will have different format of data post
        if (array_key_exists('memberMarriageDate', $_REQUEST)) $memberMarriageDate = $_REQUEST['memberMarriageDate'];
        if (array_key_exists('memberDivorceDate', $_REQUEST)) $memberDivorceDate = $_REQUEST['memberDivorceDate'];
        if (array_key_exists('memberMaidenName', $_REQUEST)) $memberMaidenName = $_REQUEST['memberMaidenName'];
        if (array_key_exists('memberSpouseName', $_REQUEST)) $memberSpouseName = $_REQUEST['memberSpouseName'];
        if (array_key_exists('memberCreditScoreDate', $_REQUEST)) $memberCreditScoreDate = $_REQUEST['memberCreditScoreDate'];
        if (array_key_exists('memberRentOrOwn', $_REQUEST)) $memberRentOrOwn = $_REQUEST['memberRentOrOwn'];
        if (array_key_exists('memberMonthlyRentOrMortgage', $_REQUEST)) $memberMonthlyRentOrMortgage = $_REQUEST['memberMonthlyRentOrMortgage'];
        if (array_key_exists('memberDateMovedAddress', $_REQUEST)) $memberDateMovedAddress = $_REQUEST['memberDateMovedAddress'];
        if (array_key_exists('memberRealEstateValue', $_REQUEST)) $memberRealEstateValue = $_REQUEST['memberRealEstateValue'];
        if (array_key_exists('memberRetirementAccountBalance', $_REQUEST)) $memberRetirementAccountBalance = $_REQUEST['memberRetirementAccountBalance'];
        if (array_key_exists('memberCashSavingsStocksBalance', $_REQUEST)) $memberCashSavingsStocksBalance = $_REQUEST['memberCashSavingsStocksBalance'];
        if (array_key_exists('memberCreditCardBalance', $_REQUEST)) $memberCreditCardBalance = $_REQUEST['memberCreditCardBalance'];
        if (array_key_exists('memberMortgageBalance', $_REQUEST)) $memberMortgageBalance = $_REQUEST['memberMortgageBalance'];
        if (array_key_exists('memberAutoLoanBalance', $_REQUEST)) $memberAutoLoanBalance = $_REQUEST['memberAutoLoanBalance'];

        $qry = ' DELETE FROM tblMembersOfficers WHERE CBEID IN(' . $CBEID . '); ';
        Database2::getInstance()->update($qry);

        $qry = ' INSERT INTO tblMembersOfficers (CBEID
                    , CID
                    , memberName
                    , memberTitle
                    , memberOwnership
                    , memberAnnualSalary
                    , memberAddress
                    , memberPhone
                    , memberCell
                    , memberSSN
                    , memberDOB
                    , memberCreditScore
                    , memberEmail
                    , recordDate
                    , memberDriversLicense
                    , memberDriversLicenseState
                    , memberTin
                    , memberPersonalGuarantee
                    , memberAuthorizedSigner
                    , memberCitizenship
                    , memberMaritalStatus
                    , memberMarriageDate
                    , memberDivorceDate
                    , memberMaidenName
                    , memberSpouseName
                    , memberCreditScoreDate
                    , memberRentOrOwn
                    , memberMonthlyRentOrMortgage
                    , memberDateMovedAddress
                    , memberRealEstateValue
                    , memberRetirementAccountBalance
                    , memberCashSavingsStocksBalance
                    , memberCreditCardBalance
                    , memberMortgageBalance
                    , memberAutoLoanBalance
                    , memberTotalNetWorth
            ) VALUES ';
        if (Arrays::getArrayValue('from', $ip) == 'file') {
            for ($mo = 0; $mo < 10; $mo++) {
                if ($_REQUEST['memberName'][$mo] != '') {

                    $memberDOB = isset($_REQUEST['memberDOB'][$mo])
                        ? Dates::formatDateWithRE($_REQUEST['memberDOB'][$mo], 'MDY', 'Y-m-d') : null;
                    $memberMarriageDate = isset($_REQUEST['memberMarriageDate'][$mo])
                        ? Dates::formatDateWithRE($_REQUEST['memberMarriageDate'][$mo], 'MDY', 'Y-m-d') : null;
                    $memberDivorceDate = isset($_REQUEST['memberDivorceDate'][$mo])
                        ? Dates::formatDateWithRE($_REQUEST['memberDivorceDate'][$mo], 'MDY', 'Y-m-d') : null;
                    $memberCreditScoreDate = isset($_REQUEST['memberCreditScoreDate'][$mo])
                        ? Dates::formatDateWithRE($_REQUEST['memberCreditScoreDate'][$mo], 'MDY', 'Y-m-d') : null;
                    $memberDateMovedAddress = isset($_REQUEST['memberDateMovedAddress'][$mo])
                        ? Dates::formatDateWithRE($_REQUEST['memberDateMovedAddress'][$mo], 'MDY', 'Y-m-d') : null;


                    $memberRentOrOwn = $_REQUEST['memberRentOrOwn'][$mo] ?? null;
                    $memberMonthlyRentOrMortgage = Strings::replaceCommaValues($_REQUEST['memberMonthlyRentOrMortgage'][$mo]) ?? null;
                    $memberRealEstateValue = Strings::replaceCommaValues($_REQUEST['memberRealEstateValue'][$mo]) ?? null;
                    $memberRetirementAccountBalance = Strings::replaceCommaValues($_REQUEST['memberRetirementAccountBalance'][$mo]) ?? null;
                    $memberCashSavingsStocksBalance = Strings::replaceCommaValues($_REQUEST['memberCashSavingsStocksBalance'][$mo]) ?? null;
                    $memberCreditCardBalance = Strings::replaceCommaValues($_REQUEST['memberCreditCardBalance'][$mo]) ?? null;
                    $memberMortgageBalance = Strings::replaceCommaValues($_REQUEST['memberMortgageBalance'][$mo]) ?? null;
                    $memberAutoLoanBalance = Strings::replaceCommaValues($_REQUEST['memberAutoLoanBalance'][$mo]) ?? null;
                    $memberTotalNetWorth = ($memberCashSavingsStocksBalance + $memberRealEstateValue + $memberRetirementAccountBalance)
                        - ($memberCreditCardBalance + $memberMortgageBalance + $memberAutoLoanBalance);
                    $qry .= $appComma . '(' . $CBEID . ', ' . $CID . ", '"
                        . trim($_REQUEST['memberName'][$mo]) . "', '"
                        . trim($_REQUEST['memberTitle'][$mo]) . "', '"
                        . $_REQUEST['memberOwnership'][$mo] . "', '"
                        . Strings::replaceCommaValues($_REQUEST['memberAnnualSalary'][$mo]) . "', '"
                        . trim($_REQUEST['memberAddress'][$mo]) . "', '"
                        . Strings::cleanPhoneNo($_REQUEST['memberPhone'][$mo]) . "', '"
                        . Strings::cleanPhoneNo($_REQUEST['memberCell'][$mo]) . "', '"
                        . Strings::cleanPhoneNo($_REQUEST['memberSSN'][$mo]) . "', '"
                        . $memberDOB . "','"
                        . trim($_REQUEST['memberCreditScore'][$mo]) . "', '"
                        . trim($_REQUEST['memberEmail'][$mo]) . "', '"
                        . $recordDate . "' , '"
                        . trim($_REQUEST['memberDriversLicense'][$mo]) . "' , '"
                        . trim($_REQUEST['memberDriversLicenseState'][$mo]) . "' , '"
                        . intval($_REQUEST['memberTin'][$mo]) . "' , '"
                        . trim($_REQUEST['memberPersonalGuarantee'][$mo]) . "' , '"
                        . trim($_REQUEST['memberAuthorizedSigner'][$mo]) . "' , '"
                        . trim($_REQUEST['memberCitizenship'][$mo]) . "' , '"
                        . trim($_REQUEST['memberMaritalStatus'][$mo]) . "' , '"
                        . $memberMarriageDate . "' , '"
                        . $memberDivorceDate . "' , '"
                        . trim($_REQUEST['memberMaidenName'][$mo]) . "' , '"
                        . trim($_REQUEST['memberSpouseName'][$mo]) . "' , '"
                        . $memberCreditScoreDate . "','"
                        . $memberRentOrOwn . "', '"
                        . $memberMonthlyRentOrMortgage . "', '"
                        . $memberDateMovedAddress . "','"
                        . $memberRealEstateValue . "', '"
                        . $memberRetirementAccountBalance . "', '"
                        . $memberCashSavingsStocksBalance . "', '"
                        . $memberCreditCardBalance . "', '"
                        . $memberMortgageBalance . "', '"
                        . $memberAutoLoanBalance . "', '"
                        . $memberTotalNetWorth
                        . "')";
                    $appComma = ',';
                    $qss++;
                }
            }

        } else {
            for ($mo = 0; $mo < count($memberName); $mo++) {
                if (trim($memberName[$mo]) != '') {
                    $memberTotalNetWorth = (Strings::replaceCommaValues($memberCashSavingsStocksBalance[$mo])
                        + Strings::replaceCommaValues($memberRealEstateValue[$mo])
                        + Strings::replaceCommaValues($memberRetirementAccountBalance[$mo]))
                        - (Strings::replaceCommaValues($memberCreditCardBalance[$mo])
                        + Strings::replaceCommaValues($memberMortgageBalance[$mo])
                        + Strings::replaceCommaValues($memberAutoLoanBalance[$mo]));

                    $memberDOB = isset($memberDOB[$mo])
                        ? Dates::formatDateWithRE($memberDOB[$mo], 'MDY', 'Y-m-d') : null;
                    $memberMarriageDate = isset($memberMarriageDate[$mo])
                        ? Dates::formatDateWithRE($memberMarriageDate[$mo], 'MDY', 'Y-m-d') : null;
                    $memberDivorceDate = isset($memberDivorceDate[$mo])
                        ? Dates::formatDateWithRE($memberDivorceDate[$mo], 'MDY', 'Y-m-d') : null;
                    $memberDateMovedAddress = isset($memberDateMovedAddress[$mo])
                        ? Dates::formatDateWithRE($memberDateMovedAddress[$mo], 'MDY', 'Y-m-d') : null;
                    $memberCreditScoreDate = isset($memberCreditScoreDate[$mo])
                        ? Dates::formatDateWithRE($memberCreditScoreDate[$mo], 'MDY', 'Y-m-d') : null;
                    $qry .= $appComma . '(' . $CBEID . ', ' . $CID . ", '"
                        . trim($memberName[$mo]) . "', '"
                        . trim($memberTitle[$mo]) . "', '"
                        . trim($memberOwnership[$mo]) . "', '"
                        . Strings::replaceCommaValues($memberAnnualSalary[$mo]) . "', '"
                        . trim($memberAddress[$mo]) . "', '"
                        . Strings::cleanPhoneNo($memberPhone[$mo]) . "', '"
                        . Strings::cleanPhoneNo($memberCell[$mo]) . "', '"
                        . Strings::cleanPhoneNo($memberSSN[$mo]) . "', '"
                        . $memberDOB . "', '"
                        . trim($memberCreditScore[$mo]) . "', '"
                        . trim($memberEmail[$mo]) . "', '"
                        . $recordDate . "', '"
                        . trim($memberDriversLicense[$mo]) . "', '"
                        . trim($memberDriversLicenseState[$mo]) . "' , '"
                        . intval($memberTin[$mo]) . "', '"
                        . trim($_REQUEST['memberPersonalGuarantee_' . $mo]) . "', '"
                        . trim($_REQUEST['memberAuthorizedSigner_' . $mo]) . "', '"
                        . trim($_REQUEST['memberCitizenship_' . $mo]) . "', '"
                        . trim($_REQUEST['memberMaritalStatus_' . $mo]) . "', '"
                        . $memberMarriageDate . "', '"
                        . $memberDivorceDate . "', '"
                        . trim($memberMaidenName[$mo]) . "', '"
                        . trim($memberSpouseName[$mo]) . "', '"
                        . $memberCreditScoreDate . "', '"
                        . trim($memberRentOrOwn[$mo]) . "', '"
                        . Strings::replaceCommaValues($memberMonthlyRentOrMortgage[$mo]) . "', '"
                        . $memberDateMovedAddress . "', '"
                        . Strings::replaceCommaValues($memberRealEstateValue[$mo]) . "', '"
                        . Strings::replaceCommaValues($memberRetirementAccountBalance[$mo]) . "', '"
                        . Strings::replaceCommaValues($memberCashSavingsStocksBalance[$mo]) . "', '"
                        . Strings::replaceCommaValues($memberCreditCardBalance[$mo]) . "', '"
                        . Strings::replaceCommaValues($memberMortgageBalance[$mo]) . "', '"
                        . Strings::replaceCommaValues($memberAutoLoanBalance[$mo]) . "', '"
                        . $memberTotalNetWorth
                        . "' )";
                    $appComma = ',';
                    $qss++;
                }
            }
        }

        $cnt2 = 0;
        if ($qss > 0) $cnt2 = Database2::getInstance()->insert($qry);
        return $cnt2;
    }


    public static function processMemberData($CID, $CBEID, $members)
    {
        foreach ($members as $entityNumber => $member) {
            $memberData = $member['entityMember'];
            foreach ($memberData as $memberIndex => $parent) {
               $MOID = self::saveMembers($CID, $CBEID, $parent);
               if($MOID) {
                   if(isset($_REQUEST['docs']['member'][$entityNumber])) {
                       $eachMemberDocs = $_REQUEST['docs']['member'][$entityNumber][$memberIndex];
                       foreach ($eachMemberDocs as $requiredDocID => $docs) {
                           foreach ($docs as $uploadedDocSerialNumber => $eachDoc){
                        /*       $displayName = $eachDoc['displayName'];
                               $expiryDate = $eachDoc['expiryDate'];
                               $docId = $eachDoc['id'];*/

                             // echo $_FILES['docs']['size']['member'][$entityNumber][$memberIndex][$requiredDocID][$uploadedDocSerialNumber]['file'];

                               $item = tblBorrowerEntityMemberDocs::Get(['id' => $eachDoc['id']]) ?? new tblBorrowerEntityMemberDocs();
                               $item->memberId = $MOID;
                               $item->requiredDocId = $requiredDocID ?: null;
                               $item->expiryDate = $eachDoc['expiryDate'] ? Dates::Datestamp($eachDoc['expiryDate']) : null;
                               $item->displayName = $eachDoc['displayName'] ?: null;

                               if ($_FILES['docs']['name']['member'][$entityNumber][$memberIndex][$requiredDocID][$uploadedDocSerialNumber]['file']) {
                              // if ($_FILES['docs']['name'][$docType][$requiredDocID][$docIndex]['file']) {
                                   $fileName = $_FILES['docs']['name']['member'][$entityNumber][$memberIndex][$requiredDocID][$uploadedDocSerialNumber]['file'];
                                       //$_FILES['docs']['name'][$entityNumber][$requiredDocID][$docIndex]['file'];
                                   $fileType = $_FILES['docs']['type']['member'][$entityNumber][$memberIndex][$requiredDocID][$uploadedDocSerialNumber]['file'];
                                       //$_FILES['docs']['type'][$docType][$requiredDocID][$docIndex]['file'];
                                   $fileTmpName = $_FILES['docs']['tmp_name']['member'][$entityNumber][$memberIndex][$requiredDocID][$uploadedDocSerialNumber]['file'];
                                       //$_FILES['docs']['tmp_name'][$docType][$requiredDocID][$docIndex]['file'];
                                   $fileError = $_FILES['docs']['error']['member'][$entityNumber][$memberIndex][$requiredDocID][$uploadedDocSerialNumber]['file'];
                                       //$_FILES['docs']['error'][$docType][$requiredDocID][$docIndex]['file'];
                                   $fileSize = $_FILES['docs']['size']['member'][$entityNumber][$memberIndex][$requiredDocID][$uploadedDocSerialNumber]['file'];
                                   //$_FILES['docs']['size'][$docType][$requiredDocID][$docIndex]['file'];
                                   if(!$eachDoc['displayName']) {
                                       $item->displayName = $fileName;
                                   }
                                   $fileName = trim('/' . $item->memberId . '/member/' . $item->requiredDocId . '/' . $fileName);
                                   if ($fileTmpName
                                       && $fileName
                                       && $fileSize) {
                                       $tblFileStorage = FileStorage::moveFile($fileTmpName, $fileName);
                                       $item->fileStorageId = $tblFileStorage->id;
                                   }
                               }
                               if ($item->fileStorageId) {
                                   $item->save();
                               }
                           }
                       }
                   }
               }
            }
        }
    }

    public static function saveMembers($CID, $CBEID, $memberData): ?int
    {
        $MOID = $memberData['memberId'] ?? 0;
        $tblMembersOfficers = tblMembersOfficers::Get([
            'MOID' => $MOID,
            'CID' => $CID,
            'CBEID' => $CBEID
        ]) ?? new tblMembersOfficers();

        $tblMembersOfficers->parent_id = $memberData['parent_id'] ?: null;
        $tblMembersOfficers->CID = $CID;
        $tblMembersOfficers->CBEID = intval($CBEID);
        $tblMembersOfficers->memberType = $memberData['memberType'];
        $tblMembersOfficers->memberCategory = $memberData['memberCategory'];
        $tblMembersOfficers->memberName = trim($memberData['memberName']);
        $tblMembersOfficers->memberTitle = trim($memberData['memberTitle']);
        $tblMembersOfficers->memberOwnership = intval($memberData['memberOwnership']);
        $tblMembersOfficers->memberAnnualSalary = Strings::replaceCommaValues($memberData['memberAnnualSalary']);
        $tblMembersOfficers->memberAddress = trim($memberData['memberAddress']);
        $tblMembersOfficers->memberPhone = Strings::cleanPhoneNo($memberData['memberPhone']);
        $tblMembersOfficers->memberCell = Strings::cleanPhoneNo($memberData['memberCell']);
        $tblMembersOfficers->memberSSN = Strings::cleanPhoneNo($memberData['memberSSN']);
        $tblMembersOfficers->memberDOB = Dates::formatDateWithRE($memberData['memberDOB'], 'MDY', 'Y-m-d');
        $tblMembersOfficers->memberCreditScore = trim($memberData['memberCreditScore']);
        $tblMembersOfficers->memberCreditScoreDate = Dates::formatDateWithRE($memberData['memberCreditScoreDate'], 'MDY', 'Y-m-d');
        $tblMembersOfficers->memberEmail = trim($memberData['memberEmail']);
        $tblMembersOfficers->recordDate = Dates::Timestamp();
        $tblMembersOfficers->memberDriversLicenseState = trim($memberData['memberDriversLicenseState']);
        $tblMembersOfficers->memberDriversLicense = trim($memberData['memberDriversLicense']);
        $tblMembersOfficers->memberPersonalGuarantee = $memberData['memberPersonalGuarantee'];
        $tblMembersOfficers->memberAuthorizedSigner = $memberData['memberAuthorizedSigner'];
        $tblMembersOfficers->memberCitizenship = $memberData['memberCitizenship'];
        $tblMembersOfficers->memberTin = intval(Strings::numberOnly($memberData['memberTin'])) ?: null;
        $tblMembersOfficers->memberMaritalStatus = $memberData['memberMaritalStatus'];
        $tblMembersOfficers->memberMarriageDate = Dates::formatDateWithRE($memberData['memberMarriageDate'], 'MDY', 'Y-m-d');
        $tblMembersOfficers->memberDivorceDate = Dates::formatDateWithRE($memberData['memberDivorceDate'], 'MDY', 'Y-m-d');
        $tblMembersOfficers->memberMaidenName = $memberData['memberMaidenName'];
        $tblMembersOfficers->memberSpouseName = $memberData['memberSpouseName'];
        $tblMembersOfficers->memberRentOrOwn = $memberData['memberRentOrOwn'];
        $tblMembersOfficers->memberMonthlyRentOrMortgage = Strings::replaceCommaValues($memberData['memberMonthlyRentOrMortgage']);
        $tblMembersOfficers->memberDateMovedAddress = Dates::formatDateWithRE($memberData['memberDateMovedAddress'], 'MDY', 'Y-m-d');
        $tblMembersOfficers->memberRealEstateValue = Strings::replaceCommaValues($memberData['memberRealEstateValue']);
        $tblMembersOfficers->memberRetirementAccountBalance = Strings::replaceCommaValues($memberData['memberRetirementAccountBalance']);
        $tblMembersOfficers->memberCashSavingsStocksBalance = Strings::replaceCommaValues($memberData['memberCashSavingsStocksBalance']);
        $tblMembersOfficers->memberCreditCardBalance = Strings::replaceCommaValues($memberData['memberCreditCardBalance']);
        $tblMembersOfficers->memberMortgageBalance = Strings::replaceCommaValues($memberData['memberMortgageBalance']);
        $tblMembersOfficers->memberAutoLoanBalance = Strings::replaceCommaValues($memberData['memberAutoLoanBalance']);
        $tblMembersOfficers->memberTotalNetWorth = ($tblMembersOfficers->memberCashSavingsStocksBalance
            + $tblMembersOfficers->memberRealEstateValue
            + $tblMembersOfficers->memberRetirementAccountBalance)
            - ($tblMembersOfficers->memberCreditCardBalance
            + $tblMembersOfficers->memberMortgageBalance
            + $tblMembersOfficers->memberAutoLoanBalance);
        $tblMembersOfficers->Save();

        return $tblMembersOfficers->MOID;
    }

    public static function saveNestedEntityMembers($params)
    {
        $CID = $params['CID'];
        $CBEID = $params['CBEID'];
        $members = $params['members'];
        self::processMemberData($CID, $CBEID, $members);
    }

    public static function saveMemberDocs($memberId, $memberIndex, $entityId) {


    }

    /**
     * @param $CID
     * @param $CBEID
     * @return void|null
     */
    public static function deleteEntityMembers($CID, $CBEID)
    {
        if (!$CID || !$CBEID) {
            return null;
        }
        $table = tblMembersOfficers::GetAll([
            tblMembersOfficers_db::COLUMN_CID   => $CID,
            tblMembersOfficers_db::COLUMN_CBEID => $CBEID,
        ]);
        foreach ($table as $row) {
            $row->Delete();
        }
    }
}
