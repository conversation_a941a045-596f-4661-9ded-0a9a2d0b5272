<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\lendingwise\tblDrawRequestLineItems_h;

class BorrowerDrawLineItemHistory extends strongType
{
    /**
     * @var int|null The ID of the history record.
     */
    public ?int $id = null;

    /**
     * @var int|null The ID of the draw request history record this line item belongs to.
     */
    public ?int $recordId = null;

    /**
     * @var int|null The ID of the original line item.
     */
    public ?int $lineItemId = null;

    /**
     * @var float The completed amount of the line item.
     */
    public ?float $completedAmount = 0.00;

    /**
     * @var float The completed percentage of the line item.
     */
    public ?float $completedPercent = 0.00;

    /**
     * @var float The requested amount of the line item.
     */
    public ?float $requestedAmount = 0.00;

    /**
     * @var float The disbursed amount of the line item.
     */
    public ?float $disbursedAmount = 0.00;

    /**
     * @var string|null The creation date.
     */
    public ?string $createdAt = null;

    /**
     * @var tblDrawRequestLineItems_h|null The database table object for the line item history.
     */
    public ?tblDrawRequestLineItems_h $lineItemHistory = null;

    /**
     * BorrowerDrawLineItemHistory constructor.
     * @param tblDrawRequestLineItems_h|null $lineItemHistory The database line item history object to initialize from.
     */
    public function __construct(?tblDrawRequestLineItems_h $lineItemHistory = null) {
        if ($lineItemHistory == null) $lineItemHistory = new tblDrawRequestLineItems_h();
        $this->setProperties($lineItemHistory);
    }

    /**
     * Saves the current line item history object to the database.
     * @return array The result of the save operation.
     */
    public function save(array $lineItemData): array {
        $this->setFromArray($lineItemData);
        $saved = $this->lineItemHistory->save();
        $this->id = $this->lineItemHistory->id;
        return $saved;
    }

    /**
     * Sets properties from the database object.
     * @param tblDrawRequestLineItems_h $lineItemHistory The database object.
     * @return void
     */
    private function setProperties(tblDrawRequestLineItems_h $lineItemHistory): void {
        $this->lineItemHistory = $lineItemHistory;
        $this->id = $lineItemHistory->id;
        $this->recordId = $lineItemHistory->recordId;
        $this->lineItemId = $lineItemHistory->lineItemId;
        $this->completedAmount = $lineItemHistory->completedAmount;
        $this->completedPercent = $lineItemHistory->completedPercent;
        $this->requestedAmount = $lineItemHistory->requestedAmount;
        $this->disbursedAmount = $lineItemHistory->disbursedAmount;
        $this->createdAt = $lineItemHistory->createdAt;
    }

    /**
     * Sets properties from an array.
     * @param array $data The data array.
     * @return void
     */
    private function setFromArray(array $data): void {
        if (isset($data['recordId'])) $this->lineItemHistory->recordId = $data['recordId'];
        if (isset($data['lineItemId'])) $this->lineItemHistory->lineItemId = $data['lineItemId'];
        if (isset($data['completedAmount'])) $this->lineItemHistory->completedAmount = $data['completedAmount'];
        if (isset($data['completedPercent'])) $this->lineItemHistory->completedPercent = $data['completedPercent'];
        if (isset($data['requestedAmount'])) $this->lineItemHistory->requestedAmount = $data['requestedAmount'];
        if (isset($data['disbursedAmount'])) $this->lineItemHistory->disbursedAmount = $data['disbursedAmount'];
    }

    /**
     * Gets the database object.
     * @return tblDrawRequestLineItems_h The database object.
     */
    public function getDbObject(): tblDrawRequestLineItems_h {
        return $this->lineItemHistory;
    }

    /**
     * Converts the line item history object to an associative array.
     * @return array An associative array representation of the line item history.
     */
    public function toArray(): array {
        return [
            "id" => $this->id,
            "recordId" => $this->recordId,
            "lineItemId" => $this->lineItemId,
            "completedAmount" => $this->completedAmount,
            "completedPercent" => $this->completedPercent,
            "requestedAmount" => $this->requestedAmount,
            "disbursedAmount" => $this->disbursedAmount,
            "createdAt" => $this->createdAt
        ];
    }
}
