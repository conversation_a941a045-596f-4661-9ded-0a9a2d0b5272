<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\lendingwise\tblDrawRequestLineItems;
use models\composite\oDrawManagement\DrawRequest;

class BorrowerDrawLineItem extends strongType
{
    /**
     * @var int|null The ID of the line item.
     */
    public ?int $id = null;

    /**
     * @var int|null The ID of the draw request this line item belongs to.
     */
    public ?int $drawId = null;

    /**
     * @var int|null The ID of the category this line item belongs to.
     */
    public ?int $categoryId = null;

    /**
     * @var string|null The name of the line item.
     */
    public ?string $name = null;

    /**
     * @var string|null The description of the line item.
     */
    public ?string $description = null;

    /**
     * @var int The display order of the line item.
     */
    public ?int $order = 1;

    /**
     * @var float The cost of the line item.
     */
    public ?float $cost = 0.00;

    /**
     * @var float The completed amount of the line item.
     */
    public ?float $completedAmount = 0.00;

    /**
     * @var float The completed percentage of the line item.
     */
    public ?float $completedPercent = 0.00;

    /**
     * @var float The requested amount of the line item.
     */
    public ?float $requestedAmount = 0.00;

    /**
     * @var float The disbursed amount of the line item.
     */
    public ?float $disbursedAmount = 0.00;

    /**
     * @var string|null Notes for the line item.
     */
    public ?string $notes = null;

    /**
     * @var string|null Lender notes for the line item.
     */
    public ?string $lenderNotes = null;

    /**
     * @var string|null The reason for rejecting the line item.
     */
    public ?string $rejectReason = null;

    /**
     * @var string|null The creation date.
     */
    public ?string $createdAt = null;

    /**
     * @var string|null The last update date.
     */
    public ?string $updatedAt = null;

    /**
     * @var tblDrawRequestLineItems|null The database table object for the line item.
     */
    public ?tblDrawRequestLineItems $lineItem = null;

    /**
     * BorrowerDrawLineItem constructor.
     * @param tblDrawRequestLineItems|null $lineItem The database line item object to initialize from.
     */
    public function __construct(?tblDrawRequestLineItems $lineItem = null) {
        if ($lineItem == null) $lineItem = new tblDrawRequestLineItems();
        $this->setProperties($lineItem);
    }

    /**
     * Saves the current line item object to the database.
     * @return array The result of the save operation.
     */
    public function save(array $lineItemData): array {
        $this->setFromArray($lineItemData);
        $saved = $this->lineItem->save();
        $this->id = $this->lineItem->id;
        return $saved;
    }

    /**
     * Sets the properties of the line item from an associative array.
     * @param array $lineItemData Associative array containing line item data.
     * @return void
     */
    private function setFromArray(array $lineItemData): void {
        $this->lineItem->id = $lineItemData['id'] ?? null;
        $this->lineItem->drawId = $lineItemData['drawId'];
        $this->lineItem->categoryId = $lineItemData['categoryId'];
        $this->lineItem->name = $lineItemData['name'];
        $this->lineItem->description = $lineItemData['description'] ?? $this->lineItem->description;
        $this->lineItem->order = $lineItemData['order'] ?? $this->lineItem->order;
        $this->lineItem->cost = $lineItemData['cost'] ?? $this->lineItem->cost;
        $this->lineItem->completedAmount = $lineItemData['completedAmount'] ?? $this->lineItem->completedAmount;
        $this->lineItem->completedPercent = $lineItemData['completedPercent'] ?? $this->lineItem->completedPercent;
        $this->lineItem->requestedAmount = $lineItemData['requestedAmount'] ?? $this->lineItem->requestedAmount;
        $this->lineItem->disbursedAmount = $lineItemData['disbursedAmount'] ?? $this->lineItem->disbursedAmount;
        $this->lineItem->notes = $lineItemData['notes'] ?? $this->lineItem->notes;
        $this->lineItem->lenderNotes = $lineItemData['lenderNotes'] ?? $this->lineItem->lenderNotes;
        $this->lineItem->rejectReason = $lineItemData['rejectReason'] ?? $this->lineItem->rejectReason;
        $this->setProperties($this->lineItem);
    }

    public function getDbObject(): tblDrawRequestLineItems {
        return $this->lineItem;
    }

    /**
     * Delete the lineitem from DB
     *
     * @return void
     */
    public function delete(): void {
        if ($this->lineItem instanceof tblDrawRequestLineItems) {
            $this->lineItem->delete();
        }
    }

    /**
     * Converts the line item object to an associative array.
     * @return array An associative array representation of the line item.
     */
    public function toArray(): array {
        return [
            "id" => $this->id,
            "drawId" => $this->drawId,
            "categoryId" => $this->categoryId,
            "name" => $this->name,
            "description" => $this->description,
            "order" => $this->order,
            "cost" => $this->cost,
            "completedAmount" => $this->completedAmount,
            "completedPercent" => $this->completedPercent,
            "requestedAmount" => $this->requestedAmount,
            "disbursedAmount" => $this->disbursedAmount,
            "notes" => $this->notes,
            "lenderNotes" => $this->lenderNotes,
            "rejectReason" => $this->rejectReason
        ];
    }

    /**
     * Sets class properties from a tblDrawRequestLineItems object
     *
     * @param tblDrawRequestLineItems $lineItem
     * @return void
     */
    private function setProperties(tblDrawRequestLineItems $lineItem): void {
        $this->id = $lineItem->id;
        $this->drawId = $lineItem->drawId;
        $this->categoryId = $lineItem->categoryId;
        $this->name = $lineItem->name;
        $this->description = $lineItem->description ?? '';
        $this->order = $lineItem->order;
        $this->cost = $lineItem->cost;
        $this->completedAmount = $lineItem->completedAmount ?? 0.00;
        $this->completedPercent = $lineItem->completedPercent ?? 0.00;
        $this->requestedAmount = $lineItem->requestedAmount ?? 0.00;
        $this->disbursedAmount = $lineItem->disbursedAmount ?? 0.00;
        $this->notes = $lineItem->notes ?? '';
        $this->lenderNotes = $lineItem->lenderNotes ?? '';
        $this->rejectReason = $lineItem->rejectReason ?? '';
        $this->createdAt = $lineItem->createdAt;
        $this->updatedAt = $lineItem->updatedAt;
        $this->lineItem = $lineItem;
    }
}
