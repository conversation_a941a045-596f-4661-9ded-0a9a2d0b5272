<?php

namespace models\composite\oFile\getFileInfo;

use models\cypher;
use models\Database2;
use models\composite\oThirdPartyServices\getThirdPartyServicesDetails;

class PCInfo
{
    public static function getReport(int $processingCompanyId)
    {
        $qry = ' select * from tblProcessingCompany where PCID = :PCID ';
        $rs = Database2::getInstance()->queryData($qry, [
            'PCID' => $processingCompanyId,
        ]);
        $PCData = $rs[0] ?? [];
        if (!empty($PCData)) {
            $cardDetails = getThirdPartyServicesDetails::getReport($processingCompanyId);
            $cardDetails = $cardDetails[array_key_first($cardDetails)];

            $PCData['creditCardType'] = cypher::myDecryption($cardDetails['creditCardType']);
            $PCData['cardHolderName'] = cypher::myDecryption($cardDetails['cardHolderName']);
            $PCData['creditCardNumber'] = cypher::myDecryption($cardDetails['creditCardNumber']);
            $PCData['expirationMonth'] = cypher::myDecryption($cardDetails['expirationMonth']);
            $PCData['expirationYear'] = cypher::myDecryption($cardDetails['expirationYear']);
            $PCData['cardNumberOnBack'] = cypher::myDecryption($cardDetails['cardNumberOnBack']);
            $PCData['billingAddress1'] = $cardDetails['billingAddress1'];
            $PCData['billingAddress2'] = $cardDetails['billingAddress2'];
            $PCData['billingCity'] = $cardDetails['billingCity'];
            $PCData['billingState'] = $cardDetails['billingState'];
            $PCData['billingZip'] = $cardDetails['billingZip'];
        }

        return $PCData;
    }

    public static function getCaptchaKey($processingCompanyId, $keyType)
    {
        $CaptchaKey = '';
        $PCDetails = self::getReport($processingCompanyId);
        if ($PCDetails['captcha'] == 2) {
            if ($keyType == 'secretKey') {
                $CaptchaKey =  GOOGLE_CAPTCHA_SECRET_KEY_GROUP_2;
            } else {
                $CaptchaKey = GOOGLE_CAPTCHA_SITE_KEY_GROUP_2;
            }

        } else {
            if ($keyType == 'secretKey') {
                $CaptchaKey = GOOGLE_CAPTCHA_SECRET_KEY;
            } else {
                $CaptchaKey = GOOGLE_CAPTCHA_SITE_KEY;
            }
        }
        return $CaptchaKey;
    }
}