<?php

namespace models\pops\exportClientFiles;

use models\APIHelper;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\GpropertyTypeNumbArray;
use models\Database2;
use models\lendingwise\db\tblAdminUsers_db;
use models\lendingwise\db\tblBorrowerAuthorizationStatus_db;
use models\lendingwise\db\tblBorrowerExperienceTrackRecord_db;
use models\lendingwise\db\tblBranch_db;
use models\lendingwise\db\tblBuildingAnalysisNeed_db;
use models\lendingwise\db\tblBuildingAnalysisOutstanding_db;
use models\lendingwise\db\tblFile_db;
use models\lendingwise\db\tblFileCalculatedValues_db;
use models\lendingwise\db\tblFileHMLO_db;
use models\lendingwise\db\tblPropertiesAccess_db;
use models\lendingwise\db\tblPropertiesCharacteristics_db;
use models\lendingwise\db\tblPropertiesDetails_db;
use models\lendingwise\db\tblPropertyAppraisalStatuses_db;
use models\lendingwise\db\tblQAInfo_db;
use models\lendingwise\db\tblRefinanceMortgage_db;
use models\lendingwise\db\tblVOMPayoffStatus_db;
use models\lendingwise\db\tblWelcomeCallStatus_db;
use models\lendingwise\tblBorrowerAuthorizationStatus;
use models\lendingwise\tblBorrowerExperienceTrackRecord;
use models\lendingwise\tblBuildingAnalysisNeed;
use models\lendingwise\tblBuildingAnalysisOutstanding;
use models\lendingwise\tblCustomField;
use models\lendingwise\tblFile;
use models\lendingwise\tblFileCalculatedValues;
use models\lendingwise\tblFileHMLO;
use models\lendingwise\tblPropertiesAccess;
use models\lendingwise\tblPropertiesCharacteristics;
use models\lendingwise\tblPropertiesDetails;
use models\lendingwise\tblPropertyAppraisalStatuses;
use models\lendingwise\tblQAInfo;
use models\lendingwise\tblRefinanceMortgage;
use models\lendingwise\tblVOMPayoffStatus;
use models\lendingwise\tblWelcomeCallStatus;
use models\lendingwise_log\ChangeLog;
use models\Log;
use models\types\simpleExcel;
use models\types\simpleExcel_Column;
use models\types\strongType;
use stdClass;

class DataExporter extends strongType
{
    // Pipeline Report needs this to be false, should be true for most reports
    public static ?bool $enforceStrongType = false;

    public static array $manyToOneHeaders = [];
    public static array $headers = [];

    public static ?int $columnOrderDummy = 800;
    public static ?array $selectedHeaders = null;
    public static ?array $exportColumnByOrder = null;
    public static ?array $LMRIds = null;

    public static ?int $PCID = null;

    public static bool $debug = false;

    public static function checkColumns()
    {
        $columns = [];
        foreach (static::$headers as $header => $settings) {
            if ($settings['table']) {
                if (!isset($columns[$settings['column']])) {
                    $columns[$settings['column']] = $settings['table'];
                }
                if ($columns[$settings['column']] !== $settings['table']) {
                    Debug($header, $settings);
                }
            }
        }
    }

    public static function Init(
        array $selectedHeaders,
        array $exportColumnByOrder,
        array $LMRIds,
        int   $PCID
    )
    {
        self::$selectedHeaders = [];
        static::$exportColumnByOrder = $exportColumnByOrder;

        if (!in_array('File ID', $selectedHeaders)) {
            self::addHeader(0, 'File ID');
        }

        foreach ($selectedHeaders as $header) {
            self::addHeader(self::getColumnOrder($header), $header);
        }


//        static::$selectedHeaders = $selectedHeaders;
        static::$LMRIds = $LMRIds;
        static::$PCID = $PCID;
    }

    public static function getData(int $limit = null): simpleExcel
    {
        if(!sizeof(self::$LMRIds)) {
            return new simpleExcel();
        }

        $table_columns = [];
        $report = [];

        $params = [];
        foreach (static::$LMRIds as $i => $LMRId) {
            if($limit && $i > $limit) {
                continue;
            }
            $params['LMRId' . $i] = $LMRId;
        }
        $SQL_LMRId = Database2::GetPlaceholders($limit ?: sizeof(static::$LMRIds), ':LMRId', true);

        foreach (static::$selectedHeaders as $i => $header) {
            if (static::$debug) {
                Log::Insert($header);
            }

            if (preg_match('/Custom_(\d+)/', $header, $match)) {
                $customId = $match[1]; // This will be 1, 2, 3, etc.
                //$customField = tblCustomField::getCached($customId);
                // self::addHeader(self::getColumnOrder($header), 'Custom Field: '.$customField->Label);
                static::_eachCustomField($report, $SQL_LMRId, $params,$header,$customId);
                continue;
            }

            if (in_array($header, static::$manyToOneHeaders)) {
                $settings = new exportClientFilesDataDB(static::$headers[$header]);

                if ($settings->function) {
                    $func = $settings->function;
                    static::$func($report, $SQL_LMRId, $params);
                }
                continue;
            }

            if (!isset(static::$headers[$header])) {
                if ($header === 'Will there be a 2nd Term to this loan?') {
                    // column removed from DB, not removed from pipeline export
                    unset(static::$selectedHeaders[$i]);
                    continue;
                }

                $header = explode(' ', $header);
                if (is_numeric($header[sizeof($header) - 1])) {
                    $cnt = intval($header[sizeof($header) - 1]);
                    if ($cnt > 1) {
                        continue;
                    }

                    unset($header[sizeof($header) - 1]);
                    $header = implode(' ', $header);
                    switch ($header) {
                        case 'Types of Required Insurance':
                        case 'Name of Carrier':
                        case 'Policy Name':
                        case 'Policy Number':
                        case 'Annual Premium':
                        case 'Policy Expiry Date':
                        case 'Appraiser Name':
                        case 'Appraiser Phone Number':
                        case 'Appraiser Email':
                        case 'Appraiser Company':
                        case 'Appraised As Is Value':
                        case 'Appraisal Rehabbed Value':
                        case 'Appraisal Monthly Rent':
                        case 'Appraisal Date Obtained':
                        case 'Appraisal Order Date':
                        case 'AVM Monthly Rent':
                        case 'AVM As Is Value':
                        case 'BPO Value':
                        case 'BPO Rehabbed Value':
                        case 'BPO Monthly Rent':
                        case 'BPO Date Obtained':
                            continue 2;

                    }
                }
                Debug([
                    'error'  => 'header not found',
                    'header' => implode(' ', $header),
                ]);
                continue;
            }

            $settings = new exportClientFilesDataDB(static::$headers[$header]);
            if (!$settings->table && !$settings->function) {
                if ($header === 'Estimated Title Insurance Fees') {
                    continue; // doesn't exist
                }
                Debug([
                    'header'   => $header,
                    'settings' => $settings,
                ]);
                continue;
            }

            if ($settings->table) {
                if (!$settings->column) {
                    Debug([
                        'header'   => $header,
                        'settings' => $settings,
                    ]);
                    continue;
                }
                $table_columns[$settings->table][] = $settings->column;
            }

            if ($settings->function) {
                $func = $settings->function;
                static::$func($report, $SQL_LMRId, $params);
            }
        }

        foreach ($table_columns as $table => $columns) {
            if (self::$debug) {
                Log::Insert('table: ' . $table);
            }
            $columns = array_unique($columns);

            switch ($table) {
                case 'tblContactInsuranceRep':
                case 'tblContactAttorney':
                case 'tblContactTitleRep':
                    $primaryColumn = 'fileID';
                    break;

                case tblBranch_db::TABLE:
                case tblAdminUsers_db::TABLE:
                case 'tblAgentBrokerNumber':
                case 'tblPCPrimeStatus':
                case 'tblAgentSecondaryBrokerNumber':
                case 'tblPCFileSubstatus':
                case 'tblProcessingCompany':
                    $primaryColumn = 'LMRId';
                    break;


                case tblFile_db::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, $table);
                    if (!$primaryColumn) {
                        continue 2;
                    }
                    self::_CheckColumns(tblFile::class, $columns);
                    break;

                case tblQAInfo_db::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, $table);
                    self::_CheckColumns(tblQAInfo::class, $columns);
                    break;

                case tblFileCalculatedValues_db::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, $table);
                    self::_CheckColumns(tblFileCalculatedValues::class, $columns);
                    break;

                case tblPropertiesDetails_db::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblProperties');
                    self::_CheckColumns(tblPropertiesDetails::class, $columns);
                    break;

                case tblPropertiesCharacteristics_db::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblProperties');
                    self::_CheckColumns(tblPropertiesCharacteristics::class, $columns);
                    break;

                case tblPropertiesAccess_db::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblProperties');
                    self::_CheckColumns(tblPropertiesAccess::class, $columns);
                    break;

                case tblBuildingAnalysisOutstanding_db::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblFileHMLONewLoanInfo');
                    self::_CheckColumns(tblBuildingAnalysisOutstanding::class, $columns);
                    break;

                case tblBuildingAnalysisNeed_db::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblFileHMLONewLoanInfo');
                    self::_CheckColumns(tblBuildingAnalysisNeed::class, $columns);
                    break;

                case tblBorrowerAuthorizationStatus_DB::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblFileHMLO');
                    self::_CheckColumns(tblBorrowerAuthorizationStatus::class, $columns);
                    break;

                case tblVOMPayoffStatus_DB::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblRefinanceMortgage');
                    self::_CheckColumns(tblVOMPayoffStatus::class, $columns);
                    break;

                case tblBorrowerExperienceTrackRecord_DB::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblFileHMLOExperience');
                    self::_CheckColumns(tblBorrowerExperienceTrackRecord::class, $columns);
                    break;
                case tblWelcomeCallStatus_DB::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblFileAdminInfo');
                    self::_CheckColumns(tblWelcomeCallStatus::class, $columns);
                    break;

                case tblPropertyAppraisalStatuses_DB::TABLE:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, 'tblProperties');
                    self::_CheckColumns(tblPropertyAppraisalStatuses::class, $columns);
                    break;
                default:
                    $primaryColumn = ChangeLog::getPrimaryColumnForTable(tblFile::class, $table);
            }

            if (!$primaryColumn) {
                Debug($table);
                continue;
            }


            switch ($table) {
                case 'tblLMRCommission':
                    foreach ($columns as $i => $column) {
                        if ($column === 'totalPaid') {
                            unset($columns[$i]);
                        }
                    }

                    $sql = APIHelper::getSQL(__DIR__ . '/sql/tblLMRCommission.sql');
                    $sql = str_ireplace('\'--LMRIDs--\'', $SQL_LMRId, $sql);
                    break;

                case 'tblPCFileSubstatus':
                    foreach ($columns as $i => $column) {
                        if ($column === 'substatus') {
                            unset($columns[$i]);
                        }
                    }

                    $sql = APIHelper::getSQL(__DIR__ . '/sql/tblPCFileSubstatus.sql');
                    $sql = str_ireplace('\'--LMRIDs--\'', $SQL_LMRId, $sql);
                    $sql = str_ireplace('\'--COLUMNS--\'', 't120.' . implode(', t120.', $columns), $sql);
                    break;

                case 'tblPCPrimeStatus':
                    foreach ($columns as $i => $column) {
                        if ($column === 'DaysInCurrentStatus') {
                            unset($columns[$i]);
                        }
                        if ($column === 'DaysInPreviousStatus') {
                            unset($columns[$i]);
                        }
                    }
                    if (!sizeof($columns)) {
                        $columns[] = 'PSID';
                    }

                    $sql = APIHelper::getSQL(__DIR__ . '/sql/tblPCPrimeStatus.sql');
                    $sql = str_ireplace('\'--LMRIDs--\'', $SQL_LMRId, $sql);
                    $sql = str_ireplace('\'--COLUMNS--\'', 't120.' . implode(', t120.', $columns), $sql);
                    break;

                case 'tblProcessingCompany':
                    $sql = '
                        SELECT 
                            t1.LMRId
                            , t2.' . implode(', t2.', $columns) . '
                        FROM tblFile t1 
                        JOIN tblProcessingCompany t2 ON t1.FPCID = t2.PCID 
                        WHERE  
                        t1.LMRId IN (' . $SQL_LMRId . ')
                    ';
                    break;

                case 'tblAgentBrokerNumber':
                    $sql = '
                        SELECT 
                            t1.LMRId
                            , t2.' . implode(', t2.', $columns) . '
                        FROM tblFile t1 
                        JOIN tblAgent t2 ON t1.brokerNumber = t2.userNumber 
                        WHERE  
                        t1.LMRId IN (' . $SQL_LMRId . ')
                    ';
                    break;

                case 'tblAgentSecondaryBrokerNumber':
                    $sql = '
                        SELECT 
                            t1.LMRId
                            , t2.' . implode(', t2.', $columns) . '
                        FROM tblFile t1 
                        JOIN tblAgent t2 ON t1.secondaryBrokerNumber = t2.userNumber 
                        WHERE  
                        t1.LMRId IN (' . $SQL_LMRId . ')
                    ';
                    break;

                case 'tblAdminUsers':
                    $cols = [];
                    foreach ($columns as $col) {
                        if (stristr($col, ' AS ') !== false) {
                            $cols[] = $col;
                        } else {
                            $cols[] = 't2.' . $col;
                        }
                    }

                    $sql = '
                        SELECT 
                            t1.fileID AS LMRId
                            , ' . implode(', ', $cols) . '
                        FROM tblFileUsers t1 
                        JOIN tblAdminUsers t2 ON t1.UID = t2.AID 
                        WHERE  
                        t1.URole = \'Employee\'
                        AND t1.fileID IN (' . $SQL_LMRId . ')
                        AND t2.activeStatus = 1 
                    ';
                    break;

                case 'tblBranch':
                    $sql = '
                        SELECT 
                            t1.LMRId
                            , t2.' . implode(', t2.', $columns) . '
                        FROM tblFile t1 
                        JOIN tblBranch t2 ON t1.FBRID = t2.executiveId 
                        WHERE  
                        t1.LMRId IN (' . $SQL_LMRId . ')
                    ';
                    break;

                case 'tblContactInsuranceRep':
                    $sql = '
                        SELECT 
                            fileID
                            , t2.' . implode(', t2.', $columns) . '
                        FROM tblFileContacts t1 
                        JOIN tblContacts t2 ON t1.CID = t2.CID 
                        WHERE  
                        t1.fileID IN (' . $SQL_LMRId . ')
                        and activeStatus = 1             
                        AND cRole = \'Insurance Rep\'       
                    ';
                    break;

                case 'tblContactAttorney':
                    $sql = '
                        SELECT 
                            fileID
                            , ' . implode(', ', $columns) . '
                        FROM tblFileContacts t1 
                        JOIN tblContacts t2 ON t1.CID = t2.CID 
                        WHERE  
                        t1.fileID IN (' . $SQL_LMRId . ')
                        and activeStatus = 1             
                        AND cRole = \'Attorney\'       
                    ';
                    break;

                case 'tblContactTitleRep':
                    $sql = '
                        SELECT 
                            fileID
                            , ' . implode(', ', $columns) . '
                        FROM tblFileContacts t1 
                        JOIN tblContacts t2 ON t1.CID = t2.CID 
                        WHERE  
                        t1.fileID IN (' . $SQL_LMRId . ')
                        and activeStatus = 1             
                        AND cRole = \'Title Rep\'       
                    ';
                    break;

                case 'tblProperties':
                    $sql = '
                   SELECT
                   ' . $primaryColumn . '
                   , ' . implode(', ', $columns) . '
                   FROM ' . $table . '
                   WHERE ' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                        AND isPrimary = 1
                    ';
                    break;

                case 'tblPropertiesDetails':
                case 'tblPropertiesAccess':
                case 'tblPropertiesCharacteristics':
                    $sql = '
                   SELECT
                   ' . $primaryColumn . '
                   ,' . implode(', ', $columns) . '
                   FROM ' . $table . '
                   JOIN tblProperties t100 ON t100.propertyId = ' . $table . '.propertyId
                   WHERE t100.' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                        AND t100.isPrimary = 1
                    ';
                    break;
               case 'tblBuildingAnalysisOutstanding':
                    $sql = '
                   SELECT
                   ' . $primaryColumn . '
                   ,' . implode(', ', $columns) . '
                   FROM ' . $table . '
                   JOIN tblFileHMLONewLoanInfo t100 ON t100.buildingAnalysisOutstanding = ' . $table . '.id
                   WHERE t100.' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                    ';
                    break;
              case 'tblBuildingAnalysisNeed':
                        $sql = '
                       SELECT
                       ' . $primaryColumn . '
                       ,' . implode(', ', $columns) . '
                       FROM ' . $table . '
                       JOIN tblFileHMLONewLoanInfo t100 ON t100.buildingAnalysisNeed = ' . $table . '.id
                       WHERE t100.' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                        ';
                        break;
              case 'tblBorrowerAuthorizationStatus':
                        $sql = '
                       SELECT
                       ' . $primaryColumn . '
                       ,' . implode(', ', $columns) . '
                       FROM ' . $table . '
                       JOIN tblFileHMLO t100 ON t100.authorizationStatus = ' . $table . '.id
                       WHERE t100.' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                        ';
                        break;
              case 'tblBorrowerExperienceTrackRecord':
                        $sql = '
                       SELECT
                       ' . $primaryColumn . '
                       ,' . implode(', ', $columns) . '
                       FROM ' . $table . '
                       JOIN tblFileHMLOExperience t100 ON t100.trackRecord = ' . $table . '.id
                       WHERE t100.' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                        ';
                        break;
                case 'tblVOMPayoffStatus':
                        $sql = '
                       SELECT
                       ' . $primaryColumn . '
                       ,' . implode(', ', $columns) . '
                       FROM ' . $table . '
                       JOIN tblRefinanceMortgage t100 ON t100.payoffStatus = ' . $table . '.id or t100.VOMStatus = ' . $table . '.id
                       WHERE t100.' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                        ';
                        break;
                case 'tblWelcomeCallStatus':
                        $sql = '
                       SELECT
                       ' . $primaryColumn . '
                       ,' . implode(', ', $columns) . '
                       FROM ' . $table . '
                       JOIN tblFileAdminInfo t100 ON t100.welcomeCallStatus = ' . $table . '.id 
                       WHERE t100.' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                        ';
                        break;
                case 'tblPropertyAppraisalStatuses':
                    $sql = '
                   SELECT
                   ' . $primaryColumn . '
                   ,' . implode(', ', $columns) . '
                   FROM ' . $table . '
                    JOIN tblPropertiesAppraiserDetails t200 on t200.propertyAppraisalStatus =  ' . $table . '.id
                    JOIN tblProperties t300 ON t300.propertyId = t200.propertyId
                   WHERE t300.' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                        AND t300.isPrimary = 1
                    ';
                    break;


                default:
                    $sql = '
                   SELECT
                   ' . $primaryColumn . '
                   ,' . implode(', ', $columns) . '
                   FROM ' . $table . '
                   WHERE ' . $primaryColumn . ' IN (' . $SQL_LMRId . ')
                   ';
                    break;
            }
            $res = Database2::getInstance()->queryData($sql, $params);
            foreach ($res as $row) {
                if ($primaryColumn != 'LMRId') {
                    $row['LMRId'] = $row[$primaryColumn];
                    unset($row[$primaryColumn]);
                }

                if (!isset($report[$row['LMRId']])) {
                    $report[$row['LMRId']] = new stdClass();
                }

                foreach ($row as $k => $v) {
                    $report[$row['LMRId']]->$k = $v;
                }
            }
        }

        $se = new simpleExcel();
        $se->Title = 'Pipeline Export';
        $se->Columns = [];
        $se->Report = [];

        static::$exportColumnByOrder = array_unique(static::$exportColumnByOrder);

        foreach (static::$exportColumnByOrder as $header) {
            if (!in_array($header, static::$selectedHeaders)) {
                continue;
            }

            $settings = static::$headers[$header] ?? null;

            // required for "many to one" columns
            if (!$settings || !$settings['column']) {
                $check = explode(' ', $header);
                $number = $check[sizeof($check) - 1];
                if (is_numeric($number)) {
                    $se->Columns[$header] = new simpleExcel_Column($header, $header);
                    continue;
                }

                if (stristr($header, 'DocStatus:') !== false) {
                    $se->Columns[$header] = new simpleExcel_Column($header, $header);
                    continue;
                }

                if (stristr($header, 'WorkFlow:') !== false) {
                    $se->Columns[$header] = new simpleExcel_Column($header, $header);
                    continue;
                }

                if (stristr($header, 'Custom Field:') !== false) {
                    $se->Columns[$header] = new simpleExcel_Column($header, $header);
                    continue;
                }

                if (stristr($header, 'HUD:') !== false) {
                    $se->Columns[$header] = new simpleExcel_Column($header, $header);
                    continue;
                }

                if (stristr($header, 'Checklist Status Count:') !== false) {
                    $se->Columns[$header] = new simpleExcel_Column($header, $header);
                    continue;
                }

                if (stristr($header, 'Checklist Status:') !== false) {
                    $se->Columns[$header] = new simpleExcel_Column($header, $header);
                    continue;
                }

                continue;
            }

            if (glCustomJobForProcessingCompany::isPC_CV3(self::$PCID) && $header === 'Lender Internal Notes') {
                $header = 'Funding Comments';
            }

            $se->Columns[$header] = new simpleExcel_Column($header, simpleExcel_Column::getProperty($header));
        }

        foreach ($report as $item) {

            $t = new stdClass();

            foreach (static::$selectedHeaders as $header) {
                if (in_array($header, static::$manyToOneHeaders)) {
                    continue;
                }

                $property = simpleExcel_Column::getProperty($header);

                $settings = static::$headers[$header] ?? null;

                if (!$settings || !$settings['column']) {
                    $check = explode(' ', $header);
                    $number = $check[sizeof($check) - 1];
                    if (is_numeric($number)) {
                        unset($check[sizeof($check) - 1]);
                        $column = implode(' ', $check);
                        $settings = new exportClientFilesDataDB(static::$headers[$column]);
                        if ($settings->column) {
                            $column = $settings->column;
                            $val = $item->{'_' .$column}[$number] ?? null;
                            $val = Database2::strongTypeValue($val, $settings->format ?? null);
                            $t->{$header} = $val;
                            continue;
                        }
                    }

                    if (stristr($header, 'WorkFlow:') !== false) {
                        $column = 'Workflow';
                        $settings = new exportClientFilesDataDB(static::$headers[$column]);
                        if ($settings->column) {
                            $column = $settings->column;
                            $val = $item->{'_' . $column}[$header] ?? null;
                            $val = Database2::strongTypeValue($val, $settings->format ?? null);
                            $t->{$header} = $val;
                            continue;
                        }
                    }
                    if (stristr($header, 'DocStatus:') !== false) {
                        $column = 'Required Doc Status';
                        $settings = new exportClientFilesDataDB(static::$headers[$column]);
                        if ($settings->column) {
                            $column = $settings->column;
                            $val = $item->{$column}[$header] ?? null;
                            $val = Database2::strongTypeValue($val, $settings->format ?? null);
                            $t->{$header} = $val;
                            continue;
                        }
                    }

                    if (stristr($header, 'Custom Field:') !== false) {
                        $column = 'All Custom Fields';
                        $settings = new exportClientFilesDataDB(static::$headers[$column]);
                        if ($settings->column) {
                            $column = $settings->column;
                            $val = $item->{'_' . $column}[$header] ?? null;
                            $val = Database2::strongTypeValue($val, $settings->format ?? null);
                            $t->{$header} = $val;
                            continue;
                        }
                    }

                    if (stristr($header, 'HUD:') !== false) {
                        $column = str_replace('HUD:', 'HUD', $header);
                        $settings = new exportClientFilesDataDB(static::$headers[$column]);
                        if ($settings->column) {
                            $column = $settings->column;
                            $val = $item->{'_' . $column}[$header] ?? null;
                            $val = Database2::strongTypeValue($val, $settings->format ?? null);
                            $t->{$header} = $val;
                            continue;
                        }
                    }

                    if (stristr($header, 'Checklist Status Count:') !== false) {
                        $column = 'Checklist Status Count';
                        $settings = new exportClientFilesDataDB(static::$headers[$column]);
                        if ($settings->column) {
                            $column = $settings->column;
                            $val = $item->{'_' . $column}[$header] ?? null;
                            $val = Database2::strongTypeValue($val, $settings->format ?? null);
                            $t->{$header} = $val;
                            continue;
                        }
                    }

                    if (stristr($header, 'Checklist Status:') !== false) {
                        $column = 'Checklist Status';
                        $settings = new exportClientFilesDataDB(static::$headers[$column]);
                        if ($settings->column) {
                            $column = $settings->column;
                            $val = $item->{'_' . $column}[$header] ?? null;
                            $val = Database2::strongTypeValue($val, $settings->format ?? null);
                            $t->{$header} = $val;
                            continue;
                        }
                    }

                    if ($header === 'Estimated Title Insurance Fees') {
                        continue; // doesn't exist
                    }

                    if ($header === 'Will there be a 2nd Term to this loan?') {
                        continue; // doesn't exist
                    }

                    Debug($header . ' - Not Defined');
                    continue;
                }

                $column = $settings['column'];
                if (stristr($column, ' AS ')) {
                    $column = explode(' AS ', $column)[1];
                }

                $val = property_exists($item, $column) ? $item->{$column} : null;

                if (is_array($val)) {
                    Debug($val, $header, $column);
                    continue;
                }

                $val = Database2::strongTypeValue($val, $settings['format'] ?? null);
                $t->{$property} = $val;

                if (glCustomJobForProcessingCompany::isPC_CV3(self::$PCID) && $header === 'Lender Internal Notes') {
                    $t->{'Funding_Comments'} = $val;
                }
            }

            if(static::$enforceStrongType) {
                $se->Report[] = new static(json_decode(json_encode($t), true));
            } else {
                $se->Report[] = $t;
            }
        }
        return $se;
    }

    public static function addHeader(float $order, string $header)
    {
        if (!in_array($header, self::$selectedHeaders)) {
            self::$selectedHeaders[] = $header;

//            if(isset(self::$exportColumnByOrder[$order])) {
//                Log::Insert($header . ' - ' . $order);
//            }

            if (!in_array($header, self::$exportColumnByOrder)) {
                while (isset(self::$exportColumnByOrder[$order])) {
//                Log::Insert($header . ' - ' . $order);
                    $order += 1;
                }
                self::$exportColumnByOrder[$order] = $header;
            }
        }
    }

    public static function unsetHeader(string $header)
    {
        if (in_array($header, self::$selectedHeaders)) {
            unset(self::$selectedHeaders[array_search($header, self::$selectedHeaders)]);
            self::$selectedHeaders = array_values(self::$selectedHeaders);
        }

        if (in_array($header, self::$exportColumnByOrder)) {
            unset(self::$exportColumnByOrder[array_search($header, self::$exportColumnByOrder)]);
            ksort(self::$exportColumnByOrder);
        }
    }

    public static function getColumnOrder(string $headerKey, int $offset = 0): float
    {
        if ($headerKey == 'File ID') {
            return 0;
        }
        if (in_array($headerKey, self::$exportColumnByOrder)) {
            $columnOrder = floatval(array_search($headerKey, self::$exportColumnByOrder));
            $columnOrder += $offset;
        } else {
            self::$columnOrderDummy++;
            $columnOrder = self::$columnOrderDummy;
        }

        return floatval($columnOrder);
    }

    private static function _CheckColumns($class, array &$columns)
    {
        foreach ($columns as $i => $column) {
            $_column = $column;
            $_as = $_column;
            if (stristr($column, ' AS ') !== false) {
                continue;
//                $parts = explode(' AS ', $column);
//                $_column = $parts[0];
//                $_as = $parts[1];
            }
            if (!property_exists($class, $_column)) {
                Debug($class, $_column);
                $columns[$i] = 'null AS ' . $_as;
            }
        }
    }

    protected static function _PropertyType(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_PropertyType');
        }

        $data = _PropertyType::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->propertyType = GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$row->propertyType];

        }
    }
}
