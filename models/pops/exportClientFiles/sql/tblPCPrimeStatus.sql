SELECT t110.LMRId
     , DATEDIFF(NOW(), t100.recordDate) AS DaysInCurrentStatus
     , '--COLUMNS--'
FROM tblFileResponse t110
         JOIN tblPCPrimeStatus t120 ON t120.PSID = t110.primeStatusId
         LEFT JOIN
     (SELECT LMRID,
             rID,
             statusID,
             recordDate,
             ROW_NUMBER() OVER (PARTITION BY LMRId ORDER BY rID DESC) as rn
      FROM tblRecordFileStatus
      WHERE LMRId IN ('--LMRIDs--')
        AND statusType = 'Pri') AS t100
     ON t100.LMRId = t110.LMRId
         AND t100.statusID = t110.primeStatusId
         AND t100.rn = 1
WHERE t110.LMRId IN ('--LMRIDs--');