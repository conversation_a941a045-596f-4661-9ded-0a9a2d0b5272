<?php

namespace models\pops\exportClientFiles;

use models\constants\docStatusArray;
use models\constants\gl\glHeaderArray;
use models\constants\purposeOfLoanArray;
use models\Controllers\LMRequest\Property;
use models\constants\gl\glPCHMLOExportData;
use models\cypher;
use models\Database2;
use models\lendingwise\db\tblAdverseAction_db;
use models\lendingwise\db\tblAssetsInfo_db;
use models\lendingwise\db\tblBorrowerAuthorizationStatus_db;
use models\lendingwise\db\tblBorrowerExperienceTrackRecord_db;
use models\lendingwise\db\tblBranch_db;
use models\lendingwise\db\tblBuildingAnalysisNeed_db;
use models\lendingwise\db\tblBuildingAnalysisOutstanding_db;
use models\lendingwise\db\tblFile2_db;
use models\lendingwise\db\tblFile_db;
use models\lendingwise\db\tblFileAdminInfo_db;
use models\lendingwise\db\tblFileCalculatedValues_db;
use models\lendingwise\db\tblFileHMLO_db;
use models\lendingwise\db\tblFileHMLOBackGround_db;
use models\lendingwise\db\tblFileHMLOBusinessEntity_db;
use models\lendingwise\db\tblFileHMLOExperience_db;
use models\lendingwise\db\tblFileHMLONewLoanInfo_db;
use models\lendingwise\db\tblFileHMLOPropInfo_db;
use models\lendingwise\db\tblFileLOExplanation_db;
use models\lendingwise\db\tblFilePropertyInfo_db;
use models\lendingwise\db\tblFileResponse_db;
use models\lendingwise\db\tblFileUpdatedDate_db;
use models\lendingwise\db\tblIncomeInfo_db;
use models\lendingwise\db\tblLMRCommission_db;
use models\lendingwise\db\tblLoanPropertySummary_db;
use models\lendingwise\db\tblLoanSetting_db;
use models\lendingwise\db\tblLoanSettingTerms_db;
use models\lendingwise\db\tblPCPrimeStatus_db;
use models\lendingwise\db\tblProperties_db;
use models\lendingwise\db\tblPropertiesAccess_db;
use models\lendingwise\db\tblPropertiesAppraiserDetails_db;
use models\lendingwise\db\tblPropertiesCharacteristics_db;
use models\lendingwise\db\tblPropertiesDetails_db;
use models\lendingwise\db\tblPropertiesFloodCertificates_db;
use models\lendingwise\db\tblPropertyAppraisalStatuses_db;
use models\lendingwise\db\tblQAInfo_db;
use models\lendingwise\db\tblRefinanceMortgage_db;
use models\lendingwise\db\tblRestInfo_db;
use models\lendingwise\db\tblShortSale2_db;
use models\lendingwise\db\tblShortSale_db;
use models\lendingwise\db\tblVOMPayoffStatus_db;
use models\lendingwise\db\tblWelcomeCallStatus_db;
use models\lendingwise\tblCustomField;
use models\lendingwise\tblCustomFieldType;
use models\Log;
use models\PageVariables;
use models\standard\Dates;
use models\standard\Strings;
use pages\backoffice\pipeline\classes\FieldsCustom;

class exportClientFilesDataV2 extends DataExporter
{
    public ?string $LMRId = null;
    public ?string $LoanProgram = null;

    // many to one
    public ?array $_DrawAmount = null;
    public ?array $_DrawDate = null;
    public ?array $_appraiserName = null;
    public ?array $_appraiserPhone = null;
    public ?array $_appraiserEmail = null;
    public ?array $_appraiserCompanyName = null;
    public ?array $_appraisedValue = null;
    public ?array $_rehabValue = null;
    public ?array $_monthlyRent = null;
    public ?array $_appraisalDateObtained = null;
    public ?array $_appraisalOrderDate = null;
    public ?array $_policyType = null;
    public ?array $_policyCarrier = null;
    public ?array $_policyName = null;
    public ?array $_policyNumber = null;
    public ?array $_policyAnnualPremium = null;
    public ?array $_policyExpDate = null;
    public ?array $_policyEffDate = null;
    public ?array $_insuranceDateReceived = null;
    public ?array $_insuranceDateOrdered = null;


    public ?string $AssignedEmployees = null;

    public ?array $_workflowStepDays = null;

    public ?array $_customFields = null;

    public ?string $CurrentLoanBalance = null;

    // draws
    public ?array $DrawAmount = null;
    public ?array $DrawDate = null;

    // insurance
    public ?array $policyAnnualPremium = null;
    public ?array $policyType = null;
    public ?array $policyCarrier = null;
    public ?array $policyName = null;
    public ?array $policyNumber = null;
    public ?array $policyExpDate = null;
    public ?array $policyEffDate = null;
    public ?array $insuranceDateReceived = null;
    public ?array $insuranceDateOrdered = null;

    // refinance current mortgage
    public ?array $_originalPurchaseDate = null;
    public ?array $_refinanceCurrentLender = null;
    public ?array $_originalPurchasePrice = null;
    public ?array $_costOfImprovementsMade = null;
    public ?array $_refinanceMonthlyPayment = null;
    public ?array $_refinanceCurrentRate = null;
    public ?array $_refinanceCurrentLoanBalance = null;
    public ?array $_subjectOriginalBalance = null;
    public ?array $_originalPrepayPenalty = null;
    public ?array $_originalPrepayPercentage = null;
    public ?array $_originalTaxesIncluded = null;
    public ?array $_originalMaturityDate = null;
    public ?array $_originalPayOffAmount = null;
    public ?array $_originalLienNumber = null;
    public ?array $_refinanceCurrentLenderFullAddress = null;
    public ?array $_refinanceCurrentLenderEmail = null;
    public ?array $_goodThroughDate = null;

    //Entity Members
    public ?array $_memberType = null;
    public ?array $_memberCategory = null;
    public ?array $_memberName = null;
    public ?array $_memberTitle = null;
    public ?array $_memberOwnership = null;
    public ?array $_memberAnnualSalary = null;
    public ?array $_memberAddress = null;
    public ?array $_memberPhone = null;
    public ?array $_memberCell = null;
    public ?array $_memberSSN = null;
    public ?array $_memberDOB = null;
    public ?array $_memberCreditScore = null;
    public ?array $_memberEmail = null;
    public ?array $_memberDriversLicense = null;
    public ?array $_memberDriversLicenseState = null;
    public ?array $_memberTin = null;
    public ?array $_memberPersonalGuarantee = null;
    public ?array $_memberAuthorizedSigner = null;
    public ?array $_memberCitizenship = null;
    public ?array $_memberMaritalStatus = null;
    public ?array $_memberMarriageDate = null;
    public ?array $_memberDivorceDate = null;
    public ?array $_memberMaidenName = null;
    public ?array $_memberSpouseName = null;


    // appraisals
    public ?array $appraiserName = null;
    public ?array $appraiserPhone = null;
    public ?array $appraiserEmail = null;
    public ?array $appraiserCompanyName = null;
    public ?array $appraisedValue = null;
    public ?array $rehabValue = null;
    public ?array $monthlyRent = null;
    public ?array $appraisalDateObtained = null;
    public ?array $appraisalOrderDate = null;

    //Property Appraisal
    public ?array $_propertyAppraisalAsIsValue = null;
    public ?array $_propertyAppraisalRehabbedValue = null;
    public ?array $_propertyAppraisalMonthlyRent = null;
    public ?array $_propertyAppraisalJobTypes = null;
    public ?array $_propertyAppraisalDateObtained = null;
    public ?array $_propertyAppraisalOrderDate = null;
    public ?array $_propertyAppraisalComments = null;
    public ?array $_propertyAppraisalEffectiveDate = null;
    public ?array $_primaryAppraisalEcoaDeliveryDate = null;
    public ?array $_propertyAppraisalInspectionDate = null;

    // workflow

    public ?array $workflowStepDays = null;
    public ?array $requiredDocStatus = null;

    // previous status

    public ?string $previousStatus = null;
    public ?string $previousStatusDays = null;

    // custom fields
    public ?array $customFields = null;

    // Total PITIA
    public ?float $totalgrossincome = null;
    public ?float $totalnetincome = null;
    public ?float $disposableincome = null;
    public ?float $creditcards = null;
    public ?float $creditcardbalance = null;
    public ?float $borrowerstudentloantuitions = null;
    public ?float $coborrowerstudentloantuitions = null;
    public ?float $borrowertotalstudentloanbalance = null;
    public ?float $coborrowertotalstudentloanbalance = null;
    public ?float $borrowergrossemployeeincome = null;
    public ?float $borrowertotalgrossincome = null;
    public ?float $borrowertotalnetincome = null;
    public ?float $coborrowergrossemployeeincome = null;
    public ?float $coborrowertotalgrossincome = null;
    public ?float $coborrowertotalnetincome = null;
    public ?float $borrowergrosssocialsecurityincome = null;
    public ?float $coborrowergrosssocialsecurityincome = null;
    public ?float $primTotalHouseHoldExpenses = null;
    public ?float $coTotalHouseHoldExpenses = null;
    public ?float $TotalPITIA = null;
    public ?float $RehabCostFinanced = null;

    // Checklist Status
    public ?array $_DocumentStatus = null;
    public ?array $_DocumentCount = null;

    // Lender Revenue
    public ?float $LenderRevenue = null;
    public ?float $NetMonthlyPaymentPITI = null;

    // No Pending Tasks
    public ?int $noOfPendingTask = null;

    // Client Type
    public ?string $ClientType = null;
    public ?string $InternalLoanPrograms = null;

    // Bank Attorney
    public ?string $BankAttorneyName = null;
    public ?string $BankAttorneyPhone = null;
    public ?string $BankAttorneyCell = null;
    public ?string $BankAttorneyFax = null;
    public ?string $BankAttorneyEmail = null;
    public ?string $BankAttorneyCompany = null;

    // CMA
    public ?array $cmaEstimatedValue = null;
    public ?array $cmaHighPrice = null;
    public ?array $cmaSuggListPrice = null;
    public ?array $cmaQuickResalePrice = null;
    public ?array $cmaSalePrice = null;
    public ?array $cmaMonthsOnMLS = null;
    public ?array $cmaAverageListingPrice = null;
    public ?array $cmaDesc = null;
    public ?int $PSID = null;
    public ?string $titleReportDate = null;
    public ?string $totalPropertiesLoanAmount = null;
    public ?string $totalPropertiesPITIA = null;
    public ?string $createdDateWithTimestamp = null;
    public ?string $applicantConfirmed = null;

    public ?string $propertyType = null;
    public ?string $borrowertotalexpenses = null;

    public ?string $EmployeeName = null;
    public ?string $EmployeeCell = null;
    public ?string $EmployeePhone = null;
    public ?string $EmployeeEmail = null;
    public ?string $EmployeeFax = null;
    public ?string $substatus = null;

    public ?string $_cmaEstimatedValue = null;
    public ?string $initialTerminYears = null;
    public ?string $initialInterestRateMargin = null;
    public ?string $initialInterestRateIndex = null;
    public ?string $initialAmortization = null;
    public ?string $initialTermAdjustmentPeriod = null;
    public ?string $secondTerminYears = null;
    public ?string $secondInterestRateMargin = null;
    public ?string $secondInterestRateIndex = null;
    public ?string $secondAmortization = null;
    public ?string $secondTermAdjustmentPeriod = null;
    public ?string $PrePaymentPenalty = null;

    public ?array $_HUDValues = null;
    public ?string $isThisBalloonPayment = null;
    public ?string $purposeOfLoan = null;
    public ?string $propertyFloodZone = null;

    public static array $manyToOneHeaders = [
        'Workflow', // multi
        'Listing History', // multi
        'Required Doc Status', // multi
        'Draw Amount', // draws
        'Draw Date', // draws

        'Appraised As Is Value', // appraisal
        'Appraiser Rehabbed Value', // appraisal
        'Appraiser Monthly Rent', // appraisal
        'Appraisal Date', // appraisal
        'Appraiser Order Date', // appraisal
        'Appraiser Name', // appraisal
        'Appraiser Phone Number', // appraisal
        'Appraiser Email', // appraisal
        'Appraiser Company', // appraisal
        'Appraised Value', // appraisal

        // 'AVM Monthly Rent', // tblShortSale2
        // 'AVM As Is Value', // tblShortSale
        // 'BPO Value', // tblShortSale
        // 'BPO Rehabbed Value', // tblShortSale
        // 'BPO Monthly Rent', // tblShortSale
        // 'BPO Date Obtained', // tblShortSale
        //  'Insurance Rep Cell', // not a way to set this in the loan management section

        'Name of Carrier', // _Insurance
        'Types of Required Insurance', // _Insurance
        'Policy Name', // _Insurance
        'Insurance Policy Number', // _Insurance
        'Policy Expiration Date', // _Insurance
        'Policy Effective Date', // _Insurance
        'Insurance Date Received', // _Insurance
        'Insurance Date Ordered', // _Insurance
        // 'Rehabbed value', // verify - tblShortSale2
        //        'Recommended Offer', // verify - tblShortSale2
        // 'Rehab Budget', // verify - tblShortSale2
        // 'Estimated Title Insurance Fees', // verify - doesn't seem to be stored in the database
        // 'Days In Previous Status', -- tblPCPrimeStatus
    ];

    public static array $headers = [

        'File ID' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LMRID,
            'format' => Database2::DATATYPE_NUMBER,
        ],

        'MERS ID' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_MERSID,
            'format' => Database2::DATATYPE_NUMBER,
        ],

        'Assigned Employees' => [
            'table'    => null,
            'column'   => 'AssignedEmployees', // 'AssignedEmployees',
            'function' => '_assignedEmployees',
            'params'   => [],
            'format'   => null,
        ],

        'Borrower First Name' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERNAME,
            'format' => Database2::DATATYPE_STRING
        ],

        'Borrower Last Name' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERLNAME,
            'format' => Database2::DATATYPE_STRING
        ],

        'Borrower Phone Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => 'phoneNumber AS BorrowerPhoneNumber', // 'phoneNumber AS BorrowerPhoneNumber',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Borrower Cell Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_CELLNUMBER,
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Borrower Fax Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_FAX,
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'SMS Service Provider' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_SERVICEPROVIDER,
            'format' => Database2::DATATYPE_SERVICE_PROVIDER
        ],

        'Borrower Email' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWEREMAIL,
            'format' => Database2::DATATYPE_EMAIL
        ],

        'Borrower Secondary Email' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERSECONDARYEMAIL,
            'format' => Database2::DATATYPE_EMAIL
        ],

        'Client SSN Last 4' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_SSNNUMBER,
            'format' => Database2::DATATYPE_SSN4
        ],

        'Full SSN' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_SSNNUMBER,
            'format' => Database2::DATATYPE_SSN
        ],

        'Borrower Date Of Birth' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERDOB,
            'format' => Database2::DATATYPE_DATE
        ],

        'Business Entity Name' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENTITYNAME,
            'format' => Database2::DATATYPE_STRING,
        ],

        'Net Worth' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_TOTALNETWORTH,
            'format' => Database2::DATATYPE_MONEY_STR
        ],

        'Credit Score range' => [
            'table'    => tblFileHMLO_db::TABLE,
            'column'   => tblFileHMLO_db::COLUMN_BORCREDITSCORERANGE,
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_STRING,
        ],

        'Experian' => [
            'table'    => tblFileHMLO_db::TABLE,
            'column'   => tblFileHMLO_db::COLUMN_BOREXPERIANSCORE, // 'borExperianScore',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_STRING,
        ],

        'Equifax'                                      => [
            'table'    => tblFileHMLO_db::TABLE,
            'column'   => tblFileHMLO_db::COLUMN_BOREQUIFAXSCORE, // 'borEquifaxScore',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_STRING,
        ],
        'Transunion'                                   => [
            'table'    => tblFileHMLO_db::TABLE,
            'column'   => tblFileHMLO_db::COLUMN_BORTRANSUNIONSCORE, // 'borTransunionScore',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_STRING,
        ],
        'Mid Fico Score'                               => [
            'table'    => tblFileHMLO_db::TABLE,
            'column'   => tblFileHMLO_db::COLUMN_MIDFICOSCORE, // 'midFicoScore',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_STRING,
        ],
        'No of properties completed in last 36 months' => [
            'table'    => tblFileHMLOExperience_db::TABLE,
            'column'   => tblFileHMLOExperience_db::COLUMN_BORNOOFREPROPERTIESCOMPLETED, // 'borNoOfREPropertiesCompleted',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_NUMBER,
        ],
        'No of rehab/Construction done'                => [
            'table'    => tblFileHMLOExperience_db::TABLE,
            'column'   => tblFileHMLOExperience_db::COLUMN_BORREHABPROPCOMPLETED, // 'borRehabPropCompleted',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_NUMBER,
        ],

        'Years of rehab/construction Exp' => [
            'table'    => tblFileHMLOExperience_db::TABLE,
            'column'   => tblFileHMLOExperience_db::COLUMN_BORNOOFYEARREHABEXPERIENCE, // 'borNoOfYearRehabExperience',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_NUMBER,
        ],

        'No of investment properties' => [
            'table'    => tblFileHMLOExperience_db::TABLE,
            'column'   => tblFileHMLOExperience_db::COLUMN_BORNOOFOWNPROP, // 'borNoOfOwnProp',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_NUMBER,
        ],

        'US Citizen' => [
            'table'    => tblFileHMLOBackGround_db::TABLE,
            'column'   => tblFileHMLOBackGround_db::COLUMN_ISBORUSCITIZEN, // 'isBorUSCitizen',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_STRING,
        ],

        'Total Cash In Bank Accounts' => [
            'table'    => tblAssetsInfo_db::TABLE,
            'column'   => tblAssetsInfo_db::COLUMN_ASSETTOTALCASHBANKACC, // 'assetTotalCashBankAcc',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_MONEY_STR,
        ],

        'Liquid Assets' => [
            'table'    => tblFileHMLOExperience_db::TABLE,
            'column'   => tblFileHMLOExperience_db::COLUMN_LIQUIDASSETS, // 'liquidAssets',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_MONEY_STR,
        ],

        'Co-borrower First Name' => [
            'table'    => tblFile_db::TABLE,
            'column'   => tblFile_db::COLUMN_COBORROWERFNAME, // 'coBorrowerFName',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_STRING,
        ],

        'Co-borrower Middle Name' => [
            'table'    => tblFile2_db::TABLE,
            'column'   => tblFile2_db::COLUMN_COBORROWERMNAME, // 'coBorrowerFName',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_STRING,
        ],

        'Co-borrower Last Name' => [
            'table'    => tblFile_db::TABLE,
            'column'   => tblFile_db::COLUMN_COBORROWERLNAME, // 'coBorrowerLName',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_STRING,
        ],

        'Co-borrower Phone Number' => [
            'table'    => tblFile_db::TABLE,
            'column'   => tblFile_db::COLUMN_COBPHONENUMBER, // 'coBPhoneNumber',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_PHONE_NUMBER,
        ],

        'Co-borrower Cell Number' => [
            'table'    => tblFile_db::TABLE,
            'column'   => tblFile_db::COLUMN_COBCELLNUMBER, // 'coBCellNumber',
            'function' => null,
            'params'   => [],
            'format'   => Database2::DATATYPE_PHONE_NUMBER,
        ],

        'Co-borrower Fax Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_COBFAX, // 'coBFax',
            'format' => Database2::DATATYPE_PHONE_NUMBER,
        ],

        'Co-borrower SMS Service Provider' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_COBSERVICEPROVIDER, // 'coBServiceProvider',
            'format' => Database2::DATATYPE_SERVICE_PROVIDER,
        ],

        'Co-borrower Email' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_COBORROWEREMAIL, // 'coBorrowerEmail',
            'format' => Database2::DATATYPE_EMAIL,
        ],

        'Co-Borrower SSN Last 4' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_COBSSNNUMBER, // 'coBSsnNumber',
            'format' => Database2::DATATYPE_SSN4,
        ],

        'Co-Borrower Full SSN' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_COBSSNNUMBER, // 'coBSsnNumber',
            'format' => Database2::DATATYPE_SSN,
        ],

        'Co-Borrower DOB' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_COBORROWERDOB, // 'coBorrowerDOB',
            'format' => Database2::DATATYPE_DATE,
        ],

        'Borrower Address' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_PRESENTADDRESS, // 'presentAddress',
            'format' => Database2::DATATYPE_STRING,
        ],

        'Borrower City' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_PRESENTCITY, // 'presentCity',
            'format' => Database2::DATATYPE_STRING,
        ],

        'Borrower State' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_PRESENTSTATE, // 'presentState',
            'format' => Database2::DATATYPE_STATE,
        ],

        'Borrower Zip' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_PRESENTZIP, // 'presentZip',
            'format' => Database2::DATATYPE_ZIP,
        ],

        'Mailing Address' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MAILINGADDRESS, // 'mailingAddress',
            'format' => Database2::DATATYPE_STRING,
        ],

        'Mailing City' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MAILINGCITY, // 'mailingCity',
            'format' => Database2::DATATYPE_STRING,
        ],

        'Mailing State' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MAILINGSTATE, // 'mailingState',
            'format' => Database2::DATATYPE_STATE,
        ],

        'Mailing Zip' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MAILINGZIP, // 'mailingZip',
            'format' => Database2::DATATYPE_ZIP,
        ],

        'Borrower Furnish this information' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_PUBLISHBINFO, // 'PublishBInfo',
            'format' => Database2::DATATYPE_FURNISH_THIS_INFO_STR,
        ],

        'Borrower Ethnicity' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BETHNICITY, // 'BEthnicity',
            'format' => Database2::DATATYPE_ETHNICITY,
        ],

        'Borrower Ethnicity Sub' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BFIETHNICITY, // 'bFiEthnicitySub',
            'format' => Database2::DATATYPE_ETHNICITY_SUB,
        ],

        'Borrower Race' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BRACE, // 'BRace',
            'format' => Database2::DATATYPE_RACE,
        ],

        'Borrower Race Sub' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BFIRACESUB, // 'bFiRaceSub',
            'format' => Database2::DATATYPE_RACE_SUB,
        ],

        'Borrower Ethnicity Print Origin' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BFIETHNICITYSUBOTHER, // 'bFiEthnicitySubOther',
            'format' => null
        ],

        'Borrower Asian Print Race' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BFIRACEASIANOTHER, // 'bFiRaceAsianOther',
            'format' => null
        ],

        'Borrower Pacific Print Race' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BFIRACEPACIFICOTHER, // 'bFiRacePacificOther',
            'format' => null
        ],

        'Co-Borrower Ethnicity Print Origin' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBETHNICITYSUBOTHER, // 'CBEthnicitySubOther',
            'format' => null
        ],

        'Co-Borrower Asian Print Race' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBRACEASIANOTHER, // 'CBRaceAsianOther',
            'format' => null
        ],

        'Co-Borrower Pacific Print Race' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBRACEPACIFICOTHER, // 'CBRacePacificOther',
            'format' => null
        ],

        'Borrower Sex' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BGENDER, // 'BGender',
            'format' => Database2::DATATYPE_GENDER,
        ],

        'Borrower Veteran' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BVETERAN, // 'BVeteran',
            'format' => Database2::DATATYPE_VETERAN,
        ],

        'Co-Borrower Furnish this information' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_PUBLISHCBINFO, // 'PublishCBInfo',
            'format' => Database2::DATATYPE_FURNISH_THIS_INFO_STR,
        ],

        'Co-Borrower Ethnicity' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBETHNICITY, // 'CBEthnicity',
            'format' => Database2::DATATYPE_ETHNICITY,
        ],

        'Co-Borrower Ethnicity Sub' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBETHNICITYSUB, // 'CBEthnicitySub',
            'format' => Database2::DATATYPE_ETHNICITY_SUB,
        ],

        'Co-Borrower Race' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBRACE, // 'CBRace',
            'format' => Database2::DATATYPE_RACE,
        ],

        'Co-Borrower Race Sub' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBRACESUB, // 'CBRaceSub',
            'format' => Database2::DATATYPE_RACE_SUB,
        ],

        'Co-Borrower Sex' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBGENDER, // 'CBGender',
            'format' => Database2::DATATYPE_GENDER,
        ],

        'Co-Borrower Veteran' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBVETERAN, // 'CBVeteran',
            'format' => Database2::DATATYPE_VETERAN,
        ],

        'Entity Name' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENTITYNAME, // 'entityName',
            'format' => null
        ],

        'DBA Name' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_TRADENAME, // 'tradeName',
            'format' => null
        ],

        'EIN' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENINO, // 'ENINo',
            'format' => null
        ],

        'Entity Type' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENTITYTYPE, // 'entityType',
            'format' => null
        ],

        'Entity Address' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENTITYADDRESS, // 'entityAddress',
            'format' => null
        ],

        'Entity City' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENTITYCITY, // 'entityCity',
            'format' => null
        ],

        'Entity State' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENTITYSTATE, // 'entityState',
            'format' => Database2::DATATYPE_STATE,
        ],

        'Entity Zip' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENTITYZIP, // 'entityZip',
            'format' => Database2::DATATYPE_ZIP,
        ],

        'Of Employees' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_NOOFEMPLOYEES, // 'noOfEmployees',
            'format' => Database2::DATATYPE_NUMBER,
        ],

        'Property Ownership' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_ENTITYPROPERTYOWNERSHIP, // 'entityPropertyOwnerShip',
            'format' => null
        ],

        'Value Of Property' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_VALUEOFPROPERTY, // 'valueOfProperty',
            'format' => Database2::DATATYPE_MONEY_STR,
        ],

        'Naics Code' => [
            'table'  => tblFileHMLOBusinessEntity_db::TABLE,
            'column' => tblFileHMLOBusinessEntity_db::COLUMN_NAICSCODE, // 'naicsCode',
            'format' => null
        ],

        'Originator' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_HMLOLENDER, // 'HMLOLender',
            'format' => null
        ],

        'Payment Frequency' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PAYMENTFREQUENCY, // 'paymentFrequency',
            'format' => Database2::DATATYPE_PAYMENT_FREQUENCY,
        ],

        'LOI Sent Date' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_LOISENTDATE,
            'format' => Database2::DATATYPE_DATE,
        ],
        'Loan Terms Expire Date' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_LOANTERMEXPIREDATE, // 'loanTermExpireDate',
            'format' => Database2::DATATYPE_DATE,
        ],

        'Actual Closing Date' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CLOSINGDATE, // 'closingDate',
            'format' => Database2::DATATYPE_DATE,
        ],

        'Target Closing Date' => [
            'table'  => tblFileHMLO_db::TABLE,
            'column' => tblFileHMLO_db::COLUMN_TARGETCLOSINGDATE, // 'targetClosingDate',
            'format' => Database2::DATATYPE_DATE,
        ],

        'Loan Program' => [
            'table'    => null,
            'column'   => 'LoanProgram', // 'LoanProgram',
            'function' => '_LoanPrograms',
            'format'   => null
        ],

        'Internal Loan Program' => [
            'table'    => null,
            'column'   => 'InternalLoanPrograms', // 'InternalLoanPrograms',
            'function' => '_InternalLoanPrograms',
            'params'   => [],
            'format'   => null
        ],

        'Transaction Type' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_TYPEOFHMLOLOANREQUESTING, // 'typeOfHMLOLoanRequesting',
            'format' => null
        ],

        'Borrower Occupancy' => [
            'table'  => tblFilePropertyInfo_db::TABLE,
            'column' => tblFilePropertyInfo_db::COLUMN_ISHOUSEPROPERTY, // 'isHouseProperty',
            'format' => null
        ],

        'Mortgage payment' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1PAYMENT, // 'lien1Payment',
            'format' => null
        ],

        'Escrowed Taxes' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_TAXES1, // 'taxes1',
            'format' => null
        ],

        'Escrowed Insurance' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_ANNUALPREMIUM, // 'annualPremium',
            'format' => null
        ],

        'Monthly Payment' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1PAYMENT, // 'lien1Payment',
            'format' => null
        ],

        'No of properties collateralized' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_NOOFPROPERTIESACQUIRING, // 'noOfPropertiesAcquiring',
            'format' => null
        ],

        'Acquisition Price Financed' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_INITIALLOANAMOUNT, // 'initialLoanAmount',
            'format' => null
        ],

        'Acquisition Down Payment' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_MAXAMTTOPUTDOWN, // 'maxAmtToPutDown',
            'format' => null
        ],

        'Rehab/Construction Cost' => [
            'table'  => tblFileHMLO_db::TABLE,
            'column' => tblFileHMLO_db::COLUMN_REHABCOST, // 'rehabCost',
            'format' => null
        ],

        'Rehab Cost Financed' => [
            'table'    => null,
            'column'   => 'RehabCostFinanced', // 'RehabCostFinanced',
            'function' => '_RehabCostFinanced',
            'params'   => [],
            'format'   => null
        ],

        'Initial Draw Amount' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_INITIALADVANCE, // 'initialAdvance',
            'format' => null
        ],

        'As is Value' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_HOMEVALUE, // 'homeValue',
            'format' => null
        ],

        'After Repair Value' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_ASSESSEDVALUE, // 'assessedValue',
            'format' => null
        ],

        'Closing Cost Financed' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_CLOSINGCOSTFINANCED, // 'closingCostFinanced',
            'format' => null
        ],

        'Total Project Cost' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_NEWTOTALPROJECTCOST, // 'newTotalProjectCost',
            'format' => null
        ],

        'Simple ARV %' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_SIMPLEARV, // 'SimpleARV',
            'format' => null
        ],

        'Full ARV %' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_FULLARV, // 'FullARV',
            'format' => null
        ],

        'Acquisition LTV' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_ACQUISITIONLTV, // 'AcquisitionLTV',
            'format' => null
        ],

        'Market LTV' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_MARKETLTV, // 'MarketLTV',
            'format' => null
        ],

        'Loan To Cost' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_LOANTOCOST, // 'LoanToCost',
            'format' => null
        ],

        '% of Rehab Cost Financed' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_REHABCOSTPERCENTAGEFINANCED, // 'rehabCostPercentageFinanced',
            'format' => null
        ],

        'Cash to Close' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_TOTALCASHTOCLOSE, // 'TotalCashToClose',
            'format' => null
        ],

        'Required Reserves' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_TOTALREQUIREDRESERVES, // 'totalRequiredReserves',
            'format' => null
        ],

        'Estate held in' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_HMLOESTATEHELDIN, // 'HMLOEstateHeldIn',
            'format' => null
        ],

        'Lien Position' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_LIENPOSITION, // 'lienPosition',
            'format' => null
        ],

        'Rehab Required' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_PROPERTYNEEDREHAB, // 'propertyNeedRehab',
            'format' => null
        ],

        'Total Rehab Cost'       => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_TOTALREHABCOST, // 'TotalRehabCost',
            'format' => null
        ],
        'Applicant Confirmation' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_APPLICANTCONFIRMED, // 'applicantConfirmed',
            'format' => tblCustomFieldType::applicantConfirmed
        ],

        'Property Construction Level' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_PROPERTYCONSTRUCTIONLEVEL, // 'propertyConstructionLevel',
            'format' => null
        ],

        'Exit Strategy' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_EXITSTRATEGY, // 'exitStrategy',
            'format' => null
        ],

        'Exit Strategy Explanation' => [
            'table'  => tblFileLOExplanation_db::TABLE,
            'column' => tblFileLOExplanation_db::COLUMN_BORCOMMENT, // 'borComment',
            'format' => null
        ],

        'Initial Loan Amount' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_INITIALLOANAMOUNT, // 'InitialLoanAmount',
            'format' => null
        ],

        'Total Loan Amount' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_TOTALLOANAMOUNT, // 'TotalLoanAmount',
            'format' => null
        ],

        'Interest Rate' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1RATE, // 'lien1Rate',
            'format' => null
        ],

        'Cost of Capital' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_COSTOFCAPITAL, // 'costOfCapital',
            'format' => null
        ],

        'Yield Spread' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_YIELDSPREAD, // 'yieldSpread',
            'format' => null
        ],

        'Purchase Price' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_COSTBASIS, // 'costBasis',
            'format' => null
        ],

        'Loan Term' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_LOANTERM, // 'loanTerm',
            'format' => null
        ],

        'Loan Term (Numerical value only)' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_LOANTERM, // 'loanTerm',
            'format' => tblCustomFieldType::loanTermNumeric
        ],

        'Loan Term (Text value only)' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_LOANTERM, // 'loanTerm',
            'format' => tblCustomFieldType::loanTermText
        ],

        'Current Loan Balance' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_CURRENTLOANBALANCE, // 'currentLoanBalance',
            'format' => null
        ],

        'Current Escrow Balance' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_CURRENTESCROWBALANCE, // 'CurrentEscrowBalance',
            'format' => null
        ],

        'Pre-paid Interest Reserve' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PREPAIDINTERESTRESERVE, // 'prepaidInterestReserve',
            'format' => null
        ],

        'Total Draws Funded' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_TOTALDRAWSFUNDED, // 'TotalDrawsFunded',
            'format' => null
        ],

        'Draw Amount' => [
            'table'    => null,
            'column'   => 'DrawAmount', // 'DrawAmount',
            'function' => '_Draws',
        ],

        'Draw Date' => [
            'table'    => null,
            'column'   => 'DrawDate', // 'DrawDate',
            'function' => '_Draws',
        ],

        'Amortization' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1TERMS, // 'amortizationType',
            'format' => null
        ],

        'Amortization Type' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_AMORTIZATIONTYPE, // 'amortizationType',
            'format' => null
        ],

        'Pre-Payment Penalty Options' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PREPAYMENTSELECTVAL, // 'prePaymentSelectVal',
            'format' => null
        ],
        'Pre-Payment Penalty'         => [
            'table'    => null,
            'column'   => 'PrePaymentPenalty',
            'function' => '_PrePaymentPenalty',
            'format'   => null
        ],

        'Servicing Number' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_SERVICINGNUMBER, // 'servicingNumber',
            'format' => null
        ],

        'Desired Closing Date' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_DESIREDCLOSINGDATE, // 'desiredClosingDate',
            'format' => Database2::DATATYPE_DATE
        ],

        'Days Until Closing' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => 'DATEDIFF(NOW(), closingDate) AS DaysUntilClosing', // 'DATEDIFF(NOW(), closingDate) AS DaysUntilClosing',
            'format' => null
        ],

        'Exit Fee Points' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_EXITFEEPOINTS, // 'exitFeePoints',
            'format' => null
        ],

        'Exit Fee Amount' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_EXITFEEAMOUNT, // 'exitFeeAmount',
            'format' => null
        ],

        'Projected Foreclosure Date' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_FORECLOSUREDATE, // 'foreclosureDate',
            'format' => null
        ],

        'Pay Off Amount' => [
            'table'    => null,
            'column'   => 'PayOffAmount', // 'PayOffAmount',
            'function' => '_PayOffAmount',
            'format'   => null
        ],

        'Foreclosure Date' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_FORECLOSUREDATE, // 'foreclosureDate',
            'format' => null
        ],

        'Accrual Type' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_ACCRUALTYPE, // 'accrualType',
            'format' => null
        ],

        'Payment Based' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => '(CASE WHEN isLoanPaymentAmt="SMP" THEN "Set Manual Payment" 
            WHEN isLoanPaymentAmt="ILA" THEN "Current Loan Balance"
                               ELSE "Total Loan Amount" END) AS isLoanPaymentAmt',
            'format' => null
        ],

        'Aggregate DSCR' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_AGGREGATEDSCR, // 'aggregateDSCR',
            'format' => null
        ],

        'Total Cash Out' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_TOTALCASHOUTAMT, // 'totalCashOutAmt',
            'format' => null
        ],

        'Extension Option' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_EXTENSIONOPTION, // 'extensionOption',
            'format' => null
        ],

        'Extension Option Points' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_EXTENSIONOPTIONPERCENTAGE, // 'extensionOptionPercentage',
            'format' => null
        ],

        'Extension Option Term' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_EXTENSIONOPTION, // 'extensionOption',
            'format' => null
        ],

        'Extension Option Rate' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_EXTENSIONRATEPERCENTAGE, // 'extensionRatePercentage',
            'format' => null
        ],

        'Rehab Budget' => [
            'table'  => tblShortSale2_db::TABLE,
            'column' => tblShortSale2_db::COLUMN_INTASSREHABBUDGET, // 'intAssRehabBudget',
            'format' => Database2::DATATYPE_MONEY_STR,
        ],

        'Property Address' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYADDRESS, // 'propertyAddress',
            'format' => null
        ],

        'Property City' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYCITY, // 'propertyCity',
            'format' => null
        ],

        'Property State' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYSTATE, // 'propertyState',
            'format' => null
        ],

        'Property Zip' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYZIPCODE, // 'propertyZipCode',
            'format' => null
        ],

        'Property County' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYCOUNTY, // 'propertyCounty',
            'format' => null
        ],

        'Property Location' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYLOCATION, // 'propertyLocation',
            'format' => null
        ],

        'Present Occupancy' => [
            'table'  => tblPropertiesDetails_db::TABLE,
            'column' => tblPropertiesDetails_db::COLUMN_PROPERTYPRESENTOCCUPANCY, // 'propertyPresentOccupancy',
            'format' => null
        ],

        'Property Condition' => [
            'table'  => tblPropertiesDetails_db::TABLE,
            'column' => tblPropertiesDetails_db::COLUMN_PROPERTYCONDITION, // 'propertyCondition',
            'format' => null
        ],

        'Property Type' => [
            'table'    => null,
            'column'   => 'propertyType', // 'propertyType',
            'function' => '_PropertyType',
            'format'   => null
        ],

        'Occupancy Notes' => [
            'table'  => tblPropertiesDetails_db::TABLE,
            'column' => tblPropertiesDetails_db::COLUMN_PROPERTYOCCUPANCYNOTES, // 'propertyOccupancyNotes',
            'format' => null
        ],

        'Condition Notes' => [
            'table'  => tblPropertiesDetails_db::TABLE,
            'column' => tblPropertiesDetails_db::COLUMN_PROPERTYCONDITIONNOTES, // 'propertyConditionNotes',
            'format' => null
        ],

        'Features' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYFEATURES, // 'propertyFeatures',
            'format' => null
        ],

        'Year Built' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYYEARBUILT, // 'propertyYearBuilt',
            'format' => null
        ],

        'Lot Size' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYACRES, // 'propertyAcres',
            'format' => null
        ],

        'Total Sq Ft' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYSQFT, // 'propertySqFt',
            'format' => null
        ],

        'Bedrooms' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYNUMBEROFBEDROOMS, // 'propertyNumberOfBedRooms',
            'format' => null
        ],

        'Bathrooms' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYNUMBEROFBATHROOMS, // 'propertyNumberOfBathRooms',
            'format' => null
        ],

        'Tax Year' => [
            'table'  => tblPropertiesDetails_db::TABLE,
            'column' => tblPropertiesDetails_db::COLUMN_PROPERTYTAXYEAR, // 'propertyTaxYear',
            'format' => null
        ],

        'Legal Description' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYLEGALDESCRIPTION, // 'propertyLegalDescription',
            'format' => null
        ],

        'URL link to property' => [
            'table'  => tblPropertiesDetails_db::TABLE,
            'column' => 'CONCAT(propertyURLLink1, \'::\', propertyURLLink2) AS propertyURLLink',
            'format' => null
        ],

        'Property Access Contact Name' => [
            'table'  => tblPropertiesAccess_db::TABLE,
            'column' => tblPropertiesAccess_db::COLUMN_PROPERTYACCESSNAME, // 'propertyAccessName',
            'format' => null
        ],

        'Property Access Contact Phone' => [
            'table'  => tblPropertiesAccess_db::TABLE,
            'column' => tblPropertiesAccess_db::COLUMN_PROPERTYACCESSPHONE, // 'propertyAccessPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Lock Box Info' => [
            'table'  => tblPropertiesAccess_db::TABLE,
            'column' => tblPropertiesAccess_db::COLUMN_PROPERTYACCESSLOCKBOXINFO, // 'propertyAccessLockBoxInfo',
            'format' => null
        ],

        'Annual Property Tax' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_TAXES1, // 'taxes1',
            'format' => null
        ],

        'Parcel No' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYPARCELNUMBER, // 'propertyParcelNumber',
            'format' => null
        ],

        'Current # of ADU(s)' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_CURRENTADU, // 'propertyParcelNumber',
            'format' => null
        ],
        'Future # of ADU(s)'  => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_FUTUREADU, // 'propertyParcelNumber',
            'format' => null
        ],

        '# of Units' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYNUMBEROFUNITS, // 'propertyNumberOfUnits',
            'format' => null
        ],

        '# Half Baths' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYNUMBEROFHALFBATHROOMS, // 'propertyNumberOfHalfBathRooms',
            'format' => null
        ],

        'Cash-Flow HOA Fees' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_SPCF_HOAFEES, // 'spcf_hoafees',
            'format' => null
        ],

        'Gross Potential Income' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_ACTUALRENTSINPLACE, // 'actualRentsInPlace',
            'format' => null
        ],

        'Debt Service Ratio' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_DEBTSERVICERATIO, // 'debtServiceRatio',
            'format' => null
        ],

        'Debt Service Ratio PITIA' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_DEBTSERVICERATIOPITIA, // 'debtServiceRatioPITIA',
            'format' => null
        ],

        'HOA Monthly Fees' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_HOAFEES1, // 'HOAFees1',
            'format' => null
        ],

        'Municipality' => [
            'table'  => tblProperties_db::TABLE,
            'column' => tblProperties_db::COLUMN_PROPERTYMUNICIPALITY, // 'propertyMunicipality',
            'format' => null
        ],

        'Is this loan being cross collateralized?' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_ISBLANKETLOAN, // 'isBlanketLoan',
            'format' => null
        ],

        'Property 1 Estimates value' => [
            'table'  => tblPropertiesDetails_db::TABLE,
            'column' => tblPropertiesDetails_db::COLUMN_PROPERTYESTIMATEDVALUE, // 'propertyEstimatedValue',
            'format' => null
        ],

        'Property 1 Zillow value' => [
            'table'  => tblPropertiesDetails_db::TABLE,
            'column' => tblPropertiesDetails_db::COLUMN_PROPERTYZILLOWVALUE, // 'propertyZillowValue',
            'format' => null
        ],

        'zillow Value' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_ZILLOWVALUE, // 'zillowValue',
            'format' => null
        ],

        'zillow Rent Value' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_ZILLOWRENTVALUE, // 'zillowRentValue',
            'format' => null
        ],

        'Appraiser Name' => [
            'table'    => null,
            'column'   => 'appraiserName', // 'appraiserName',
            'function' => '_Appraisal',
            'format'   => null
        ],

        'Appraiser Phone Number' => [
            'table'    => null,
            'column'   => 'appraiserPhone', // 'appraiserPhone',
            'function' => '_Appraisal',
            'format'   => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Appraiser Email' => [
            'table'    => null,
            'column'   => 'appraiserEmail', // 'appraiserEmail',
            'function' => '_Appraisal',
            'format'   => null
        ],

        'Appraiser Company'     => [
            'table'    => null,
            'column'   => 'appraiserCompanyName', // 'appraiserCompanyName',
            'function' => '_Appraisal',
            'format'   => null
        ],
        'Appraised Value' => [
            'table'    => null,
            'column'   => 'appraisedValue', // 'appraisedValue',
            'function' => '_Appraisal',
            'format'   => null
        ],

        'Appraisal Rehabbed Value' => [
            'table'    => null,
            'column'   => 'rehabValue', // 'rehabValue',
            'function' => '_Appraisal',
            'format'   => null
        ],

        'Appraisal Monthly Rent' => [
            'table'    => null,
            'column'   => 'monthlyRent', // 'monthlyRent',
            'function' => '_Appraisal',
            'format'   => null
        ],
        'Appraisal Date'         => [
            'table'    => '',
            'column'   => 'appraisalDateObtained',
            'function' => '_Appraisal',
            'format'   => null
        ],

        'Appraisal Order Date' => [
            'table'    => null,
            'column'   => 'appraisalOrderDate', // 'appraisalOrderDate',
            'function' => '_Appraisal',
            'format'   => null
        ],

        'AVM As Is Value' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_AVM1VALUE, // 'AVM1Value',
            'format' => null
        ],

        'AVM Monthly Rent' => [
            'table'  => tblShortSale2_db::TABLE,
            'column' => tblShortSale2_db::COLUMN_AVM1MONTHLYRENT, // 'AVM1MonthlyRent',
            'format' => null
        ],

        'BPO Value' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BPO1VALUE, // 'BPO1Value',
            'format' => null
        ],

        'BPO Rehabbed Value' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_REHABVALUE3, // 'rehabValue3',
            'format' => null
        ],

        'BPO Monthly Rent' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_TWELVEMONTHRENT, // 'twelveMonthRent',
            'format' => null
        ],

        'BPO Date Obtained' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_DATEOBTAINED3, // 'dateObtained3',
            'format' => null
        ],

        'Property Value As Is' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_HOMEVALUE, // 'homeValue',
            'format' => null
        ],

        'Assessed Monthly Rent' => [
            'table'  => tblShortSale2_db::TABLE,
            'column' => tblShortSale2_db::COLUMN_ASSESSEDRENTVALUE, // 'assessedRentValue',
            'format' => null
        ],

        'Valuation Notes' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_APPRAISALNOTES, // 'appraisalNotes',
            'format' => null
        ],

        'Required Valuation Methods' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_REQVALUATIONMETHOD, // 'reqValuationMethod',
            'format' => null
        ],

        'Requested Return Date' => [
            'table'  => tblShortSale2_db::TABLE,
            'column' => tblShortSale2_db::COLUMN_APPRAISALREQRETURNDATE, // 'appraisalReqReturnDate',
            'format' => null
        ],

        'Is there an accepted purchase agreement?' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_ACCEPTEDPURCHASE, // 'acceptedPurchase',
            'format' => null
        ],
        'Purchase Agreement Expiration Date' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PAEXPIRATIONDATE, // 'purchaseAgreementExpirationDate',
            'format' => Database2::DATATYPE_DATE
        ],
        'Loan Guarantee' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_LOANGUARANTEETYPE, // 'loanGuarantee',
            'format' => null
        ],
        'Is there a wholesale fee involved with this purchase?' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_INVOLVEDPURCHASE,
            'format' => null,
        ],
        'How much is the wholesale fee?' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_WHOLESALEFEE,
            'format' => null,
        ],
        'Loan/Exit Plan' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_EXITSTRATEGY, // 'exitStrategy',
            'format' => null
        ],
        'Loan/Exit Plan Explanation' => [
            'table'  => tblFileLOExplanation_db::TABLE,
            'column' => tblFileLOExplanation_db::COLUMN_BORCOMMENT,
            'format' => null,
        ],
        'Are you adding square footage?' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_HAVEBORSQUAREFOOTAGE,
            'format' => null,
        ],
        'How many additional square feet are you adding?' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_BORNOOFSQUAREFEET,
            'format' => null,
        ],
        'Improvements to be Completed' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_REHABTOBEMADE,
            'format' => null,
        ],
        'Renovation Timeframe (months)' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_REHABTIME,
            'format' => null,
        ],
        'Will there be a 2nd lien or borrowed money for down payment?' => [
            'table'  => tblFileHMLOBackGround_db::TABLE,
            'column' => tblFileHMLOBackGround_db::COLUMN_ISBORBORROWEDDOWNPAYMENT,
            'format' => null,
        ],
        '(If Yes, Provide explanation.)' => [
            'table'  => tblFileHMLOBackGround_db::TABLE,
            'column' => tblFileHMLOBackGround_db::COLUMN_BORBORROWEDDOWNPAYMENTEXPLN,
            'format' => null,
        ],
        'Jr. Lien Holder Name' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_SECONDARYHOLDERNAME,
            'format' => null,
        ],
        'Jr. Lien Amount' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_SECONDARYFINANCINGAMOUNT,
            'format' => null,
        ],
        'Purpose(s) of loan' => [
            'table'    => null,
            'column'   => 'purposeOfLoan',
            'function' => '_PurposeOfLoan',
            'format'   => null,
        ],
        'Security Instrument' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_SECURITYINSTRUMENT,
            'format' => null,
        ],
        'Estate will be held in' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_HMLOESTATEHELDIN,
            'format' => null,
        ],
        'Will borrower personally guarantee this loan?' => [
            'table'  => tblFileHMLOBackGround_db::TABLE,
            'column' => tblFileHMLOBackGround_db::COLUMN_ISBORPERSONALLYGUARANTEELOAN,
            'format' => null,
        ],
        'Display Notes' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_DISPLAYNOTES,
            'format' => null
        ],
        'Use of Funds' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_USEOFFUNDS,
            'format' => null
        ],
        'Is this Loan subject to a Balloon Payment?' => [
            'table'  => null,
            'column' => 'isThisBalloonPayment',
            'function' => '_BalloonPayment',
            'format' => null,
        ],

        'Title Ordered Date' => [
            'table'  => tblFilePropertyInfo_db::TABLE,
            'column' => tblFilePropertyInfo_db::COLUMN_TITLEORDEREDDATE, // 'titleOrderedDate',
            'format' => null
        ],

        'Name on Title' => [
            'table'  => tblFilePropertyInfo_db::TABLE,
            'column' => tblFilePropertyInfo_db::COLUMN_TITLESELLER, // 'titleSeller',
            'format' => null
        ],

        'Title Rep First Name' => [
            'table'  => 'tblContactTitleRep',
            'column' => 'contactName AS TitleRepFirstName', // 'contactName AS TitleRepFirstName',
            'format' => null
        ],

        'Title Rep Last Name' => [
            'table'  => 'tblContactTitleRep',
            'column' => 'contactLName AS TitleRepLastName', // 'contactLName AS TitleRepLastName',
            'format' => null
        ],

        'Title Company' => [
            'table'  => 'tblContactTitleRep',
            'column' => 'companyName AS TitleCompany', // 'companyName AS TitleCompany',
            'format' => null
        ],

        'Title Email' => [
            'table'  => 'tblContactTitleRep',
            'column' => 'email AS TitleEmail', // 'email AS TitleEmail',
            'format' => null
        ],

        'Title Phone' => [
            'table'  => 'tblContactTitleRep',
            'column' => 'phone AS TitlePhone', // 'phone AS TitlePhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Attorney Name' => [
            'table'  => 'tblContactAttorney',
            'column' => 'contactName AS AttorneyName', // 'contactName AS AttorneyName',
            'format' => null
        ],

        'Attorney Firm Name' => [
            'table'  => 'tblContactAttorney',
            'column' => 'companyName', // 'companyName',
            'format' => null
        ],

        'Attorney Email' => [
            'table'  => 'tblContactAttorney',
            'column' => 'email AS AttorneyEmail', // 'email AS AttorneyEmail',
            'format' => null
        ],

        'Attorney Phone' => [
            'table'  => 'tblContactAttorney',
            'column' => 'phone AS AttorneyPhone', // 'phone AS AttorneyPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER,
        ],

        'Name of Company' => [
            'table'  => 'tblContactInsuranceRep',
            'column' => 'companyName AS InsuranceNameOfCompany', // 'companyName AS InsuranceNameOfCompany',
            'format' => null
        ],

        'Insurance Rep First Name' => [
            'table'  => 'tblContactInsuranceRep',
            'column' => 'contactName AS InsuranceRepFirstName', // 'contactName AS InsuranceRepFirstName',
            'format' => null
        ],

        'Insurance Rep Last Name' => [
            'table'  => 'tblContactInsuranceRep',
            'column' => 'contactLName', // 'contactLName',
            'format' => null
        ],

        'Insurance Rep Email' => [
            'table'  => 'tblContactInsuranceRep',
            'column' => 'email AS InsuranceRepEmail', // 'email AS InsuranceRepEmail',
            'format' => null
        ],

        'Insurance Rep Phone' => [
            'table'  => 'tblContactInsuranceRep',
            'column' => 'phone AS InsuranceRepPhone', // 'phone AS InsuranceRepPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Insurance Rep Cell' => [
            'table'  => 'tblContactInsuranceRep',
            'column' => 'cell', // 'cell',
            'format' => null
        ],

        'Types of Required Insurance' => [
            'table'    => null,
            'column'   => 'policyType', // 'policyType',
            'function' => '_Insurance',
            'format'   => Database2::DATATYPE_INSURANCE_TYPE,
        ],

        'Name of Carrier' => [
            'table'    => null,
            'column'   => 'policyCarrier', // 'policyCarrier',
            'function' => '_Insurance',
            'format'   => null
        ],

        'Policy Name' => [
            'table'    => null,
            'column'   => 'policyName', // 'policyName',
            'function' => '_Insurance',
            'format'   => null
        ],

        'Policy Expiration Date' => [
            'table'    => null,
            'column'   => 'policyExpDate', // 'policyExpDate',
            'function' => '_Insurance',
            'format'   => null
        ],

        'Policy Effective Date'                            => [
            'table'    => null,
            'column'   => 'policyEffDate', // 'policyEffDate',
            'function' => '_Insurance',
            'format'   => null
        ],
        'Insurance Date Received'                          => [
            'table'    => null,
            'column'   => 'insuranceDateReceived', // 'policyEffDate',
            'function' => '_Insurance',
            'format'   => null
        ],
        'Insurance Date Ordered'                           => [
            'table'    => null,
            'column'   => 'insuranceDateOrdered', // 'policyEffDate',
            'function' => '_Insurance',
            'format'   => null
        ],
        //Refinance Fields
        'Original Lien Number'                             => [
            'table'    => null,
            'column'   => 'originalLienNumber',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Original Pay Off Amount'                          => [
            'table'    => null,
            'column'   => 'originalPayOffAmount',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Original Purchase Date'                           => [
            'table'    => null,
            'column'   => 'originalPurchaseDate',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Refinance Current Lender'                         => [
            'table'    => null,
            'column'   => 'refinanceCurrentLender',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Current Lender Full Address'                      => [
            'table'    => null,
            'column'   => 'refinanceCurrentLenderFullAddress',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Current Lender Email'                             => [
            'table'    => null,
            'column'   => 'refinanceCurrentLenderEmail',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Original Purchase Price'                          => [
            'table'    => null,
            'column'   => 'originalPurchasePrice',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Maturity Date of Loan'                            => [
            'table'    => null,
            'column'   => 'originalMaturityDate',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Prepayment Penalty $'                             => [
            'table'    => null,
            'column'   => 'originalPrepayPenalty',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Prepayment Penalty %'                             => [
            'table'    => null,
            'column'   => 'originalPrepayPercentage',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Cost of Improvements Made'                        => [
            'table'    => null,
            'column'   => 'costOfImprovementsMade',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Are taxes and insurance included in the payment?' => [
            'table'    => null,
            'column'   => 'originalTaxesIncluded',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Refinance Monthly Payment'                        => [
            'table'    => null,
            'column'   => 'refinanceMonthlyPayment',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Refinance Current Rate'                           => [
            'table'    => null,
            'column'   => 'refinanceCurrentRate',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Refinance Current Loan Balance'                   => [
            'table'    => null,
            'column'   => 'refinanceCurrentLoanBalance',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Good Through Date'                   => [
            'table'    => null,
            'column'   => 'goodThroughDate',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],
        'Original Balance'                                 => [
            'table'    => null,
            'column'   => 'subjectOriginalBalance',
            'function' => '_RefinanceCurrentMortgage',
            'format'   => null
        ],

        'Policy Number' => [
            'table'    => null,
            'column'   => 'policyNumber', // 'policyNumber',
            'function' => '_Insurance',
            'format'   => null
        ],

        'Annual Premium' => [
            'table'    => null,
            'column'   => 'policyAnnualPremium', // 'policyAnnualPremium',
            'function' => '_Insurance',
            'format'   => null
        ],

        'Policy Expiry Date' => [
            'table'    => null,
            'column'   => 'policyExpDate', // 'policyExpDate',
            'function' => '_Insurance',
            'format'   => null
        ],

        'Borrower Occupation' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_OCCUPATION1, // 'occupation1',
            'format' => null
        ],

        'Borrower Employment Type' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_EMPLOYEDINFO1, // 'employedInfo1',
            'format' => null
        ],

        'Borrower Employer Name' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_EMPLOYER1, // 'employer1',
            'format' => null
        ],

        'Borrower Base Employment Income' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_GROSSINCOME1, // 'grossIncome1',
            'format' => null
        ],

        'Borrower Net Income' => [
            'table'    => null,
            'column'   => 'borrowertotalnetincome', // 'borrowertotalnetincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Borrower This Loan'                 => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1PAYMENT, // 'lien1Payment',
            'format' => null
        ],
        'Borrower Total Expenses'            => [
            'table'    => null,
            'column'   => 'borrowertotalexpenses', // 'BorrowerTotalExpenses',
            'function' => '_TotalPITIA',
            'format'   => null
        ],
        'Co-Borrower Occupation'             => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_OCCUPATION2, // 'occupation2',
            'format' => null
        ],
        'Co-Borrower Employment Type'        => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_EMPLOYEDINFO1, // 'employedInfo2',
            'format' => null
        ],
        'Co-Borrower Employer Name'          => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_EMPLOYER2, // 'employer2',
            'format' => null
        ],
        'Co-Borrower Base Employment Income' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_GROSSINCOME2, // 'grossIncome2',
            'format' => null
        ],

        'Co-Borrower Net Income' => [
            'table'    => null,
            'column'   => 'coborrowertotalnetincome', // 'coborrowertotalnetincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Co-Borrower Total Expenses' => [
            'table'    => null,
            'column'   => 'coTotalHouseHoldExpenses', // 'coTotalHouseHoldExpenses',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Branch Name' => [
            'table'  => tblBranch_db::TABLE,
            'column' => tblBranch_db::COLUMN_LMREXECUTIVE, // 'LMRExecutive',
            'format' => null
        ],

        'Branch Company' => [
            'table'  => tblBranch_db::TABLE,
            'column' => 'company AS BranchCompany', // 'company AS BranchCompany',
            'format' => null
        ],

        'Branch Phone' => [
            'table'  => tblBranch_db::TABLE,
            'column' => tblBranch_db::COLUMN_TOLLFREE, // 'tollFree',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Branch Cell' => [
            'table'  => tblBranch_db::TABLE,
            'column' => 'cellNumber AS BranchCell', // 'cellNumber AS BranchCell',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Branch Fax' => [
            'table'  => tblBranch_db::TABLE,
            'column' => 'fax AS BranchFax', // 'fax AS BranchFax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Branch Email' => [
            'table'  => tblBranch_db::TABLE,
            'column' => tblBranch_db::COLUMN_EXECUTIVEEMAIL, // 'executiveEmail',
            'format' => null
        ],

        'Employee Name' => [
            'table'    => null,
            'function' => '_LastAssignedEmployee',
            'column'   => 'EmployeeName', // 'EmployeeName',
            'format'   => null
        ],

        'Employee Phone' => [
            'table'    => null,
            'function' => '_LastAssignedEmployee',
            'column'   => 'EmployeePhone', // 'EmployeePhone',
            'format'   => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Employee Cell' => [
            'table'    => null,
            'function' => '_LastAssignedEmployee',
            'column'   => 'cellNumber AS EmployeeCell', // 'cellNumber AS EmployeeCell',
            'format'   => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Employee Fax' => [
            'table'    => null,
            'function' => '_LastAssignedEmployee',
            'column'   => 'fax AS EmployeeFax', // 'fax AS EmployeeFax',
            'format'   => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Employee Email' => [
            'table'    => null,
            'function' => '_LastAssignedEmployee',
            'column'   => 'email AS EmployeeEmail', // 'email AS EmployeeEmail',
            'format'   => null
        ],

        'Agent First Name' => [
            'table'  => 'tblAgentBrokerNumber',
            'column' => 'firstName AS AgentFirstName', // 'firstName AS AgentFirstName',
            'format' => null
        ],

        'Agent Last Name' => [
            'table'  => 'tblAgentBrokerNumber',
            'column' => 'lastName AS AgentLastName', // 'lastName AS AgentLastName',
            'format' => null
        ],

        'Agent Company' => [
            'table'  => 'tblAgentBrokerNumber',
            'column' => 'company AS AgentCompany', // 'company AS AgentCompany',
            'format' => null
        ],

        'Agent Phone' => [
            'table'  => 'tblAgentBrokerNumber',
            'column' => 'phoneNumber AS AgentPhone', // 'phoneNumber AS AgentPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Agent Cell' => [
            'table'  => 'tblAgentBrokerNumber',
            'column' => 'cellNumber AS AgentCell', // 'cellNumber AS AgentCell',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Agent Fax' => [
            'table'  => 'tblAgentBrokerNumber',
            'column' => 'fax AS AgentFax', // 'fax AS AgentFax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Agent Email' => [
            'table'  => 'tblAgentBrokerNumber',
            'column' => 'email AS AgentEmail', // 'email AS AgentEmail',
            'format' => null
        ],

        'LO First Name' => [
            'table'  => 'tblAgentSecondaryBrokerNumber',
            'column' => 'firstName AS LOFirstName', // 'firstName AS LOFirstName',
            'format' => null
        ],

        'LO Last Name' => [
            'table'  => 'tblAgentSecondaryBrokerNumber',
            'column' => 'lastName AS LOLastName', // 'lastName AS LOLastName',
            'format' => null
        ],

        'LO Company' => [
            'table'  => 'tblAgentSecondaryBrokerNumber',
            'column' => 'company AS LOCompany', // 'company AS LOCompany',
            'format' => null
        ],

        'LO Phone' => [
            'table'  => 'tblAgentSecondaryBrokerNumber',
            'column' => 'phoneNumber AS LOPhone', // 'phoneNumber AS LOPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'LO Cell' => [
            'table'  => 'tblAgentSecondaryBrokerNumber',
            'column' => 'cellNumber AS LOCell', // 'cellNumber AS LOCell',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'LO Fax' => [
            'table'  => 'tblAgentSecondaryBrokerNumber',
            'column' => 'fax AS LOFax', // 'fax AS LOFax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'LO Email' => [
            'table'  => 'tblAgentSecondaryBrokerNumber',
            'column' => 'email AS LOEmail', // 'email AS LOEmail',
            'format' => null
        ],

        'File Created Date'           => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_RECORDDATE, // 'recordDate',
            'format' => null
        ],
        'Created Date With Timestamp' => [
            'table'    => null,
            'column'   => 'createdDateWithTimestamp',
            'function' => '_CreatedDateWithTimestamp',
            'format'   => null
        ],
        'Last Updated date'           => [
            'table'  => tblFileUpdatedDate_db::TABLE,
            'column' => tblFileUpdatedDate_db::COLUMN_LASTUPDATEDDATE, // 'lastUpdatedDate',
            'format' => null
        ],

        'Last Updated Date/Time' => [
            'table'  => tblFileUpdatedDate_db::TABLE,
            'column' => tblFileUpdatedDate_db::COLUMN_LASTUPDATEDDATE, // 'lastUpdatedDate',
            'format' => null
        ],

        'Received Date' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_RECEIVEDDATE, // 'receivedDate',
            'format' => null
        ],

        'Status' => [
            'table'  => tblPCPrimeStatus_db::TABLE,
            'column' => tblPCPrimeStatus_db::COLUMN_PRIMARYSTATUS, // 'primaryStatus',
            'format' => null
        ],

        'Sub Status' => [
            'table'    => null,
            'column'   => 'substatus', // 'substatus',
            'function' => '_FileSubStatus',
            'format'   => null
        ],

        'Days in Current Status' => [
            'table'  => tblPCPrimeStatus_db::TABLE,
            'column' => 'DaysInCurrentStatus', // 'DaysInCurrentStatus',
            'format' => null
        ],

        'Days In Current Status' => [
            'table'  => tblPCPrimeStatus_db::TABLE,
            'column' => 'DaysInCurrentStatus', // 'DaysInCurrentStatus',
            'format' => null
        ],

        'Days In Previous Status' => [
            'table'    => null,
            'column'   => 'DaysInPreviousStatus', // 'DaysInPreviousStatus',
            'function' => '_DaysInPreviousStatus',
            'format'   => null
        ],

        'Loan Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LOANNUMBER, // 'loanNumber',
            'format' => null
        ],

        'Closing Date' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CLOSINGDATE, // 'closingDate',
            'format' => Database2::DATATYPE_DATE
        ],

        'Date of First payment due' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_TRIALPAYMENTDATE1, // 'trialPaymentDate1',
            'format' => null
        ],

        'Type of Purchase' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_TYPEOFSALE, // 'typeOfSale',
            'format' => null
        ],

        'Rehabbed value' => [
            'table'  => tblShortSale2_db::TABLE,
            'column' => tblShortSale2_db::COLUMN_INTASSREHABBEDVALUE, // 'intAssRehabbedValue',
            'format' => null
        ],

        'Recommended Offer' => [
            'table'  => tblShortSale2_db::TABLE,
            'column' => tblShortSale2_db::COLUMN_INTASSRECOMMENDEDOFFER, // 'intAssRecommendedOffer',
            'format' => null
        ],

        'Maturity Date' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_MATURITYDATE, // 'maturityDate',
            'format' => null
        ],

        'Pay Off Date' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_PAYOFFDATE, // 'payOffDate',
            'format' => null
        ],

        'Lead Source' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_LEADSOURCE, // 'leadSource',
            'format' => null
        ],

        'Referring Party' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_REFERRINGPARTY, // 'referringParty',
            'format' => null
        ],

        'Date Of Loan Sale Agreement' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_LOANSALEDATE, // 'loanSaleDate',
            'format' => null
        ],

        'Funding Date' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_FUNDINGDATE, // 'fundingDate',
            'format' => null
        ],

        'Servicing Status' => [
            'table'  => tblFileHMLOPropInfo_db::TABLE,
            'column' => tblFileHMLOPropInfo_db::COLUMN_SERVICINGSUBSTATUS, // 'servicingSubStatus',
            'format' => null
        ],

        'Workflow'            => [
            'table'    => null,
            'column'   => 'workflowStepDays', // 'workflowStepDays',
            'function' => '_Workflow',
            'format'   => null
        ],
        'Required Doc Status' => [
            'table'    => null,
            'column'   => 'requiredDocStatus',
            'function' => '_RequiredDocStatus',
            'format'   => null
        ],

        /*'Will borrower personally guarantee this loan?' => [
            'table'  => tblFileHMLOBackGround_db::TABLE,
            'column' => tblFileHMLOBackGround_db::COLUMN_ISBORPERSONALLYGUARANTEELOAN, // 'isBorPersonallyGuaranteeLoan',
            'format' => null
        ],*/

        'Sale date' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_SALESDATE, // 'salesDate',
            'format' => null
        ],

        'Origination Points Rate' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_ORIGINATIONPOINTSRATE, // 'originationPointsRate',
            'format' => null
        ],

        'Origination Points Value' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_ORIGINATIONPOINTSVALUE, // 'originationPointsValue',
            'format' => null
        ],

        'Broker Points Rate' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_BROKERPOINTSRATE, // 'brokerPointsRate',
            'format' => null
        ],

        'Broker Points Value' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_BROKERPOINTSVALUE, // 'brokerPointsValue',
            'format' => null
        ],

        'Closing Cost Financing fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_CLOSINGCOSTFINANCINGFEE, // 'closingCostFinancingFee',
            'format' => null
        ],

        'Attorney fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_ATTORNEYFEE, // 'attorneyFee',
            'format' => null
        ],

        'Application Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_APPLICATIONFEE, // 'applicationFee',
            'format' => null
        ],

        'Appraisal Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_APPRAISALFEE, // 'appraisalFee',
            'format' => null
        ],

        'Estimated Title Insurance Fees' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_ESTDTITLECLOSINGFEE, // 'estdTitleClosingFee',
            'format' => null
        ],

        'Processing Fees' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PROCESSINGFEE, // 'processingFee',
            'format' => null
        ],

        'Draws Set Up Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_DRAWSSETUPFEE, // 'drawsSetUpFee',
            'format' => null
        ],

        'Draws Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_DRAWSFEE, // 'drawsFee',
            'format' => null
        ],

        'Valuation - BPO' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_VALUATIONBPOFEE, // 'valuationBPOFee',
            'format' => null
        ],

        'Valuation - AVM' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_VALUATIONAVMFEE, // 'valuationAVMFee',
            'format' => null
        ],

        'Credit Report' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_CREDITREPORTFEE, // 'creditReportFee',
            'format' => null
        ],

        'Background Check' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_BACKGROUNDCHECKFEE, // 'backgroundCheckFee',
            'format' => null
        ],

        'Tax Service' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_TAXSERVICEFEE, // 'taxServiceFee',
            'format' => null
        ],

        'Document Preparation' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_DOCUMENTPREPARATIONFEE, // 'documentPreparationFee',
            'format' => null
        ],

        'Wire Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_WIREFEE, // 'wireFee',
            'format' => null
        ],

        'Servicing Set Up Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_SERVICINGSETUPFEE, // 'servicingSetUpFee',
            'format' => null
        ],

        'Flood Certificate' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_FLOODCERTIFICATEFEE, // 'floodCertificateFee',
            'format' => null
        ],

        'Flood Service' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_FLOODSERVICEFEE, // 'floodServiceFee',
            'format' => null
        ],

        'Inspection Fees' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_INSPECTIONFEES, // 'inspectionFees',
            'format' => null
        ],

        'Project Feasibility' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PROJECTFEASIBILITY, // 'projectFeasibility',
            'format' => null
        ],

        'Due Diligence' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_DUEDILIGENCE, // 'dueDiligence',
            'format' => null
        ],

        'Ucc/Lien Search' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_UCCLIENSEARCH, // 'UccLienSearch',
            'format' => null
        ],

        'Lender Credit to Offset 3rd Party Fees' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_THIRDPARTYFEES, // 'thirdPartyFees',
            'format' => null
        ],

        'Other' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_OTHERFEE, // 'otherFee',
            'format' => null
        ],

        'Escrow Fees' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_ESCROWFEES, // 'escrowFees',
            'format' => null
        ],

        'Recording Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_RECORDINGFEE, // 'recordingFee',
            'format' => null
        ],

        'Underwriting Fees' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_UNDERWRITINGFEES, // 'underwritingFees',
            'format' => null
        ],

        'Property tax' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PROPERTYTAX, // 'propertyTax',
            'format' => null
        ],

        'Discount Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_BUFFERANDMESSENGERFEE, // 'bufferAndMessengerFee',
            'format' => null
        ],

        'Travel Notary Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_TRAVELNOTARYFEE, // 'travelNotaryFee',
            'format' => null
        ],

        'Pre paid Interest' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PREPAIDINTEREST, // 'prePaidInterest',
            'format' => null
        ],

        'Real Estate Taxes' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_REALESTATETAXES, // 'realEstateTaxes',
            'format' => null
        ],

        'Insurance Premium' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_INSURANCEPREMIUM, // 'insurancePremium',
            'format' => null
        ],

        'Pay Off Liens/Creditors' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PAYOFFLIENSCREDITORS, // 'payOffLiensCreditors',
            'format' => null
        ],

        'Wire Transfer Fee to Title' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_WIRETRANSFERFEETOTITLE, // 'wireTransferFeeToTitle',
            'format' => null
        ],

        'Wire Transfer Fee to Escrow' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_WIRETRANSFERFEETOESCROW, // 'wireTransferFeeToEscrow',
            'format' => null
        ],

        'Past Due Property Taxes' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PASTDUEPROPERTYTAXES, // 'pastDuePropertyTaxes',
            'format' => null
        ],

        'Tax impounds' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_TAXIMPOUNDSFEE, // 'taxImpoundsFee',
            'format' => null
        ],

        'Ins impounds' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_INSIMPOUNDSFEE, // 'insImpoundsFee',
            'format' => null
        ],

        'Earnest Deposit' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_EARNESTDEPOSIT, // 'earnestDeposit',
            'format' => null
        ],

        'Current Lender Info' => [
            'table'  => tblRefinanceMortgage_db::TABLE,
            'column' => tblRefinanceMortgage_db::COLUMN_REFINANCECURRENTLENDER, // 'refinanceCurrentLender',
            'format' => null
        ],

        'Broker Processing Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_BROKERPROCESSINGFEE, // 'brokerProcessingFee',
            'format' => null
        ],

        'Seller Credits Fee' => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_SELLERCREDITSFEE, // 'sellerCreditsFee',
            'format' => null
        ],

        'Disclosure Sent Date'   => [
            'table'  => tblFileAdminInfo_db::TABLE,
            'column' => tblFileAdminInfo_db::COLUMN_DISCLOSURESENTDATE, // 'disclosureSentDate',
            'format' => null
        ],
        'Target Submission Date' => [
            'table'  => tblFileAdminInfo_db::TABLE,
            'column' => tblFileAdminInfo_db::COLUMN_TARGETSUBMISSIONDATE,
            'format' => null
        ],

        'Final Submission Date' => [
            'table'  => tblFileAdminInfo_db::TABLE,
            'column' => tblFileAdminInfo_db::COLUMN_FINALSUBMISSIONDATE,
            'format' => null
        ],

        'Initial Comments Date' => [
            'table'  => tblFileAdminInfo_db::TABLE,
            'column' => tblFileAdminInfo_db::COLUMN_INITIALCOMMENTSDATE,
            'format' => null
        ],

        'Final Approval Date' => [
            'table'  => tblFileAdminInfo_db::TABLE,
            'column' => tblFileAdminInfo_db::COLUMN_FINALAPPROVALDATE,
            'format' => null
        ],

        'Build Analysis Delivered Date' => [
            'table'  => tblFileAdminInfo_db::TABLE,
            'column' => tblFileAdminInfo_db::COLUMN_BUILDANALYSISDELIVEREDDATE,
            'format' => null
        ],

        'Loan Document Date' => [
            'table'  => tblFileAdminInfo_db::TABLE,
            'column' => tblFileAdminInfo_db::COLUMN_LOANDOCUMENTDATE, // 'loanDocumentDate',
            'format' => null
        ],

        'Lender Internal Notes' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_LENDERINTERNALNOTES, // 'lenderInternalNotes',
            'format' => null
        ],

        'Total Paid Payables' => [
            'table'  => tblLMRCommission_db::TABLE,
            'column' => tblLMRCommission_db::COLUMN_TOTALPAID, // 'totalPaid',
            'format' => null
        ],

        "HUD 1001 Initial Deposit for Escrow Paid From Borrower Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1001 Initial Deposit for Escrow Paid From Seller Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1002 Hazard Insurance Paid From Borrower Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1002 Hazard Insurance Paid From Seller Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1003 Property Taxes Paid From Borrower Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1003 Property Taxes Paid From Seller Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1004 Flood Insurance Paid From Borrower Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1004 Flood Insurance Paid From Seller Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1005 Paid From Borrower Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1005 Paid From Seller Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1006 Paid From Borrower Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1006 Paid From Seller Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1007 Paid From Borrower Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1007 Paid From Seller Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1008 Paid From Borrower Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1008 Paid From Seller Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1009 Aggregate Paid From Borrower Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        "HUD 1009 Aggregate Paid From Seller Settlement" => [
            'column'   => "HUDValues",
            'function' => '_HUD'
        ],

        'Backoffice File Link' => [
            'table'    => null,
            'column'   => 'BackofficeFileLink', // 'BackofficeFileLink',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'Full App for Borrower' => [
            'table'    => null,
            'column'   => 'FullAppForBorrower', // 'FullAppForBorrower',
            'function' => '_FileURLs',
            'format'   => null,
        ],

        'Quick App for Borrower' => [
            'table'    => null,
            'column'   => 'QuickAppForBorrower', // 'QuickAppForBorrower',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'Full App for Broker' => [
            'table'    => null,
            'column'   => 'FullAppForBroker', // 'FullAppForBroker',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'Quick App for Broker' => [
            'table'    => null,
            'column'   => 'QuickAppForBroker', // 'QuickAppForBroker',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'Upload Portal for Borrower' => [
            'table'    => null,
            'column'   => 'UploadPortalForBorrower', // 'UploadPortalForBorrower',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'Upload Portal for Broker' => [
            'table'    => null,
            'column'   => 'UploadPortalForBroker', // 'UploadPortalForBroker',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'QA Borrower Info Redacted' => [
            'table'    => null,
            'column'   => 'QABorrowerInfoRedacted', // 'QABorrowerInfoRedacted',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'QA Borrower Info Redacted and Offer Sub' => [
            'table'    => null,
            'column'   => 'QABorrowerInfoRedactedAndOfferSub', // 'QABorrowerInfoRedactedAndOfferSub',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'QA Read Only' => [
            'table'    => null,
            'column'   => 'QAReadOnly', // 'QAReadOnly',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'QA Read Only and Offer Sub' => [
            'table'    => null,
            'column'   => 'QAReadOnlyAndOfferSub', // 'QAReadOnlyAndOfferSub',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'QA Read Only and Upload Portal' => [
            'table'    => null,
            'column'   => 'QAReadOnlyAndUploadPortal', // 'QAReadOnlyAndUploadPortal',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'QA Read Only Upload Portal and Offer Sub' => [
            'table'    => null,
            'column'   => 'QAReadOnlyUploadPortalAndOfferSub', // 'QAReadOnlyUploadPortalAndOfferSub',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'FA Borrower Info Redacted' => [
            'table'    => null,
            'column'   => 'FABorrowerInfoRedacted', // 'FABorrowerInfoRedacted',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'FA Borrower Info Redacted and Offer Sub' => [
            'table'    => null,
            'column'   => 'FABorrowerInfoRedactedAndOfferSub', // 'FABorrowerInfoRedactedAndOfferSub',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'FA Read Only' => [
            'table'    => null,
            'column'   => 'FAReadOnly', // 'FAReadOnly',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'FA Read Only and Offer Sub' => [
            'table'    => null,
            'column'   => 'FAReadOnlyAndOfferSub', // 'FAReadOnlyAndOfferSub',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'FA Read Only and Upload Portal' => [
            'table'    => null,
            'column'   => 'FAReadOnlyAndUploadPortal', // 'FAReadOnlyAndUploadPortal',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'FA Read Only Upload Portal and Offer Sub' => [
            'table'    => null,
            'column'   => 'FAReadOnlyUploadPortalAndOfferSub', // 'FAReadOnlyUploadPortalAndOfferSub',
            'function' => '_FileURLs',
            'format'   => null
        ],

        'Client First Name' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERNAME, // 'borrowerName',
            'format' => null
        ],

        'Client Last Name'        => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERLNAME, // 'borrowerLName',
            'format' => null
        ],
        'Client Phone Number'     => [
            'table'  => tblFile_db::TABLE,
            'column' => 'phoneNumber AS ClientPhoneNumber', // 'phoneNumber AS ClientPhoneNumber',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],
        'Client Alt Phone Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_ALTPHONENUMBER, // 'altPhoneNumber',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Client Cell Phone Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_CELLNUMBER, // 'cellNumber',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Client Work Phone Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_WORKNUMBER, // 'workNumber',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Client Fax Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_FAX, // 'fax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Client Email' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWEREMAIL, // 'borrowerEmail',
            'format' => null
        ],

        'Client Time Zone' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERTIMEZONE, // 'borrowerTimeZone',
            'format' => null
        ],

        'Pre qualifier Name' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_PREQUALIFIERNAME, // 'preQualifierName',
            'format' => null
        ],

        'Client DOB' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_BORROWERDOB, // 'borrowerDOB',
            'format' => null
        ],

        'Client SMS Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => 'CONCAT(cellNumber, \'::\', serviceProvider) AS ClientSMSNumber', // CONCAT(cellNumber, \'::\', serviceProvider) AS ClientSMSNumber,
            'params' => [],
            'format' => Database2::DATATYPE_CELL_SMS_PROVIDER
        ],

        'Co-borrower Alt Phone Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_COBALTPHONENUMBER, // 'coBAltPhoneNumber',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Co-borrower Work Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_COBORROWERWORKNUMBER, // 'coBorrowerWorkNumber',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Co-borrower Time Zone' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_COBORROWERTIMEZONE, // 'coBorrowerTimeZone',
            'format' => null
        ],

        'Co-borrower SMS Number' => [
            'table'  => tblFile_db::TABLE,
            'column' => 'CONCAT(coBCellNumber, \'::\', coBServiceProvider) AS CoBorrowerSMSNumber', // CONCAT(coBCellNumber, \'::\', coBServiceProvider) AS CoBorrowerSMSNumber,
            'format' => Database2::DATATYPE_CELL_SMS_PROVIDER
        ],

        'Occupancy' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYOWNEROCCUPANCY, // 'propertyOwnerOccupancy',
            'format' => null
        ],

        'No of bed room' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYNUMBEROFBEDROOMS, // 'propertyNumberOfBedRooms',
            'format' => null
        ],

        'No of baths' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYNUMBEROFBATHROOMS, // 'propertyNumberOfBathRooms',
            'format' => null
        ],

        'Sq Ft' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYSQFT, // 'propertySqFt',
            'format' => null
        ],

        'Future property type' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_FUTUREPROPERTYTYPE, // 'futurePropertyType',
            'format' => Database2::DATATYPE_FUTURE_PROPERTY_TYPE_STR
        ],

        'Future # of units' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_FUTURENOOFUNITS, // 'futureNoOfUnits',
            'format' => null
        ],

        'Future square footage' => [
            'table'  => tblPropertiesCharacteristics_db::TABLE,
            'column' => tblPropertiesCharacteristics_db::COLUMN_PROPERTYADJUSTEDSQFT, // 'propertyAdjustedSqFt',
            'format' => null
        ],

        'Flood Zone' => [
            'table'    => null,
            'column'   => 'propertyFloodZone',
            'function' => '_FloodZone',
            'format'   => null
        ],
        //Appraisal Info
        'Property Appraisal As Is Value' => [
            'table'    => null,
            'column'   => 'propertyAppraisalAsIsValue', // 'propertyAppraisalAsIsValue',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],
        'Property Appraisal Rehabbed Value' => [
            'table'    => null,
            'column'   => 'propertyAppraisalRehabbedValue', // 'propertyAppraisalRehabbedValue',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],
        'Property Appraisal Monthly Rent' => [
            'table'    => null,
            'column'   => 'propertyAppraisalMonthlyRent', // 'propertyAppraisalMonthlyRent',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],
        'Property Appraisal Job Types' => [
            'table'    => null,
            'column'   => 'propertyAppraisalJobTypes', // 'propertyAppraisalJobTypes',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],
        'Property Appraisal Date Obtained' => [
            'table'    => null,
            'column'   => 'propertyAppraisalDateObtained', // 'propertyAppraisalDateObtained',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],
        'Property Appraisal Order Date' => [
            'table'    => null,
            'column'   => 'propertyAppraisalOrderDate', // 'propertyAppraisalOrderDate',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],
        'Property Appraisal Comments' => [
            'table'    => null,
            'column'   => 'propertyAppraisalComments', // 'propertyAppraisalComments',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],
        'Property Appraisal Effective Date' => [
            'table'    => null,
            'column'   => 'propertyAppraisalEffectiveDate', // 'propertyAppraisalEffectiveDate',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],
        'Primary Appraisal Ecoa Delivery Date' => [
            'table'    => null,
            'column'   => 'primaryAppraisalEcoaDeliveryDate', // 'primaryAppraisalEcoaDeliveryDate',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],
        'Property Appraisal Inspection Date' => [
            'table'    => null,
            'column'   => 'propertyAppraisalInspectionDate', // 'propertyAppraisalInspectionDate',
            'function' => '_PropertyAppraisal',
            'format'   => null
        ],


        'Home Value' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_HOMEVALUE, // 'homeValue',
            'format' => null
        ],

        'Year purchased' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_YEARPURCHASED, // 'yearPurchased',
            'format' => null
        ],

        'Cost Basis' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_COSTBASIS, // 'costBasis',
            'format' => null
        ],

        'BPO1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BPO1, // 'BPO1',
            'format' => null
        ],

        'BPO2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BPO2, // 'BPO2',
            'format' => null
        ],

        'BPO3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BPO3, // 'BPO3',
            'format' => null
        ],

        'Appraiser 1' => [
            'table'    => null,
            'column'   => 'appraiserName', // 'appraiserName',
            'function' => '_Appraisal',
            'format'   => null
        ],

        'Appraiser 2' => [
            'table'    => null,
            'column'   => 'appraiserName', // 'appraiserName',
            'function' => '_Appraisal',
            'format'   => null
        ],

        'Original Lender' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_ORIGINALLENDER1, // 'originalLender1',
            'format' => null
        ],

        'Current Lender' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_SERVICER1, // 'servicer1',
            'format' => null
        ],

        'Mortgage Investor 1' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MORTGAGEINVESTOR1, // 'mortgageInvestor1',
            'format' => Database2::DATATYPE_MORTGAGE_INVESTOR_OWNER
        ],

        'Loan Type lien 1' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LOANTYPE, // 'loanType',
            'format' => null
        ],

        'Current Rate' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1RATE, // 'lien1Rate',
            'format' => null
        ],

        'Lender Revenue'                      => [
            'table'    => null,
            'column'   => 'LenderRevenue', // 'LenderRevenue',
            'function' => '_LenderRevenue',
            'format'   => null
        ],
        'Monthly Payment (PITIA) - Loan Info' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_NETMONTHLYPAYMENTPITI,
            'format' => Database2::DATATYPE_MONEY_STR
        ],

        'Unpaid Balance' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1AMOUNT, // 'lien1Amount',
            'format' => null
        ],

        'Original Loan Amount' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1ORIGINALBALANCE, // 'lien1OriginalBalance',
            'format' => null
        ],

        'Amount Past Due' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1BALANCEDUE, // 'lien1BalanceDue',
            'format' => null
        ],

        'Months Past Due' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_NOOFMONTHSBEHIND1, // 'noOfMonthsBehind1',
            'format' => null
        ],

        'Mtg Type' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MORTGAGEOWNER1, // 'mortgageOwner1',
            'format' => null
        ],

        'Mtg PI Payment' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN1PAYMENT, // 'lien1Payment',
            'format' => null
        ],

        'Taxes' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_TAXES1, // 'taxes1',
            'format' => null
        ],

        'Insurance' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_INSURANCE1, // 'insurance1',
            'format' => null
        ],

        'Flood Insurance' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_FLOODINSURANCE1, // 'floodInsurance1',
            'format' => null
        ],

        'Mtg Insurance' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_MORTGAGEINSURANCE1, // 'mortgageInsurance1',
            'format' => null
        ],

        'HOA Fees' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_HOAFEES1, // 'HOAFees1',
            'format' => null
        ],

        'Total PITIA' => [
            'table'    => null,
            'column'   => 'TotalPITIA', // 'TotalPITIA',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Notice Of Acceleration' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_NOTICEACCELERATIONDATE, // 'noticeAccelerationDate',
            'format' => null
        ],

        'Loan Origination Date' => [
            'table'  => tblRestInfo_db::TABLE,
            'column' => tblRestInfo_db::COLUMN_NOTEDATE, // 'noteDate',
            'format' => null
        ],

        'Transfer Of Servicing Date' => [
            'table'  => tblFile2_db::TABLE,
            'column' => tblFile2_db::COLUMN_TRANSFEROFSERVICINGDATE, // 'transferOfServicingDate',
            'format' => null
        ],

        'Original Lender 2' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_ORIGINALLENDER2, // 'originalLender2',
            'format' => null
        ],

        'Current Lender 2' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_SERVICER2, // 'servicer2',
            'format' => null
        ],

        'Mortgage Investor 2' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MORTGAGEINVESTOR2, // 'mortgageInvestor2',
            'format' => Database2::DATATYPE_MORTGAGE_INVESTOR_OWNER
        ],

        'Loan Type lien 2' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LOANTYPE2, // 'loanType2',
            'format' => null
        ],

        'Rate' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN2RATE, // 'lien2Rate',
            'format' => null
        ],

        'Unpaid Balance lien 2' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN2AMOUNT, // 'lien2Amount',
            'format' => null
        ],

        'Original Loan Amount lien 2' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN2ORIGINALBALANCE, // 'lien2OriginalBalance',
            'format' => null
        ],

        'Payment Amount' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN2PAYMENT, // 'lien2Payment',
            'format' => null
        ],

        'Loan Number lien 2' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LOANNUMBER2, // 'loanNumber2',
            'format' => null
        ],

        'Amount Past Due lien 2' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_LIEN2BALANCEDUE, // 'lien2BalanceDue',
            'format' => null
        ],

        'Months Past Due lien 2'   => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_NOOFMONTHSBEHIND2, // 'noOfMonthsBehind2',
            'format' => null
        ],

        // space intended here
        'Current Lender Servicer ' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_SERVICER2, // 'servicer2',
            'format' => null
        ],

        'Mtg Notes' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_MORTGAGENOTES, // 'mortgageNotes',
            'format' => null
        ],

        'Borrower Gross Social Security Income' => [
            'table'    => null,
            'column'   => 'borrowergrosssocialsecurityincome', // 'borrowergrosssocialsecurityincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Borrower Gross Employee  Income' => [
            'table'    => null,
            'column'   => 'borrowergrossemployeeincome', // 'borrowergrossemployeeincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Borrower Net employee Income' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_NETMONTHLYINCOME1, // 'netMonthlyIncome1',
            'format' => null
        ],

        'Borrower Total Gross Income' => [
            'table'    => null,
            'column'   => 'borrowertotalgrossincome', // 'borrowertotalgrossincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Borrower Total Net Income' => [
            'table'    => null,
            'column'   => 'borrowertotalnetincome', // 'borrowertotalnetincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Credit Cards' => [
            'table'    => null,
            'column'   => 'creditcards', // 'creditcards',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Credit Card Balance' => [
            'table'    => null,
            'column'   => 'creditcardbalance', // 'creditcardbalance',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Borrower Student Loan Tuitions' => [
            'table'    => null,
            'column'   => 'borrowerstudentloantuitions', // 'borrowerstudentloantuitions',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Borrower Total Student Loan Balance' => [
            'table'    => null,
            'column'   => 'borrowertotalstudentloanbalance', // 'borrowertotalstudentloanbalance',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Co-borrower Employment Type' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_EMPLOYEDINFO2, // 'employedInfo2',
            'format' => null
        ],

        'Co-borrower Gross Social Security Income' => [
            'table'    => null,
            'column'   => 'coborrowergrosssocialsecurityincome', // 'coborrowergrosssocialsecurityincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Co-borrower Gross Employee  Income' => [
            'table'    => null,
            'column'   => 'coborrowergrossemployeeincome', // 'coborrowergrossemployeeincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Co-borrower Net employee Income' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_NETMONTHLYINCOME2, // 'netMonthlyIncome2',
            'format' => null
        ],

        'Co-borrower Total Gross Income' => [
            'table'    => null,
            'column'   => 'coborrowertotalgrossincome', // 'coborrowertotalgrossincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Co-borrower Total Net Income' => [
            'table'    => null,
            'column'   => 'coborrowertotalnetincome', // 'coborrowertotalnetincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Total Gross Income' => [
            'table'    => null,
            'column'   => 'totalgrossincome', // 'totalgrossincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Total Net Income' => [
            'table'    => null,
            'column'   => 'totalnetincome', // 'totalnetincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Disposable Income' => [
            'table'    => null,
            'column'   => 'disposableincome', // 'disposableincome',
            'function' => '_TotalPITIA',
            'format'   => null
        ],

        'Current DTI' => [
            'table'  => tblFileCalculatedValues_db::TABLE,
            'column' => tblFileCalculatedValues_db::COLUMN_CURRENTDTI, // 'currentDTI',
            'format' => null
        ],

        'Co-borrower Student Loan Tuitions' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_STUDENTLOANS2, // 'studentLoans2',
            'format' => null
        ],

        'Co-borrower Total Student Loan Balance' => [
            'table'  => tblIncomeInfo_db::TABLE,
            'column' => tblIncomeInfo_db::COLUMN_STUDENTLOANSBALANCE2, // 'studentLoansBalance2',
            'format' => null
        ],

        'Trustee Name' => [
            'table'    => null,
            'column'   => 'BankAttorneyName', // 'BankAttorneyName',
            'function' => '_BankAttorney',
            'format'   => null
        ],

        'Trustee Company' => [
            'table'    => null,
            'column'   => 'BankAttorneyCompany', // 'BankAttorneyCompany',
            'function' => '_BankAttorney',
            'format'   => null
        ],

        'Trustee Phone' => [
            'table'    => null,
            'column'   => 'BankAttorneyPhone', // 'BankAttorneyPhone',
            'function' => '_BankAttorney',
            'format'   => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Trustee Cell' => [
            'table'    => null,
            'column'   => 'BankAttorneyCell', // 'BankAttorneyCell',
            'function' => '_BankAttorney',
            'format'   => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Trustee Fax' => [
            'table'    => null,
            'column'   => 'BankAttorneyFax', // 'BankAttorneyFax',
            'function' => '_BankAttorney',
            'format'   => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Trustee Email' => [
            'table'    => null,
            'column'   => 'BankAttorneyEmail', // 'BankAttorneyEmail',
            'function' => '_BankAttorney',
            'format'   => null
        ],

        'Sale Date' => [
            'table'  => tblFile_db::TABLE,
            'column' => 'salesDate', // 'salesDate',
            'format' => Database2::DATATYPE_DATE
        ],

        'Priority Status' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_PRIORITYLEVEL, // 'priorityLevel',
            'format' => null
        ],

        'No of Pending Tasks' => [
            'table'    => null,
            'column'   => 'noOfPendingTask', // 'noOfPendingTask',
            'function' => '_NoOfPendingTasks',
            'format'   => null
        ],

        'File Number' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_FILENUMBER, // 'fileNumber',
            'format' => null
        ],

        'Notes' => [
            'table'  => null,
            'column' => null, // '',
            'format' => null
        ],

        'Services' => [
            'table'    => null,
            'column'   => 'ClientType', // 'ClientType',
            'function' => '_ClientType',
            'format'   => null
        ],

        'Borrower Call Back' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_BORROWERCALLBACK, // 'borrowerCallBack',
            'format' => null
        ],

        'HAFA DATE' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_HAFADATE, // 'HAFADate',
            'format' => null
        ],

        'Welcome Call Date' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_WELCOMECALLDATE, // 'welcomeCallDate',
            'format' => null
        ],

        'Bank Call Completed' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_BANKCALLCOMPLETED, // 'bankCallCompleted',
            'format' => null
        ],

        'Lender Call Back' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_LENDERCALLBACK, // 'lenderCallBack',
            'format' => null
        ],

        'File Received Date' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_RECEIVEDDATE, // 'receivedDate',
            'format' => null
        ],

        'Summon Date' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_SUMMONDATE, // 'summonDate',
            'format' => null
        ],

        'Closed Date' => [
            'table'  => tblFile_db::TABLE,
            'column' => tblFile_db::COLUMN_CLOSEDDATE, // 'closedDate',
            'format' => null
        ],

        'Date Mod Received' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_TRIALMODRECEIVEDDATE, // 'trialModReceivedDate',
            'format' => null
        ],

        'Mod 1st Payment' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_FIRSTTRIALPAYMENTDATE, // 'firstTrialPaymentDate',
            'format' => null
        ],

        'Lender Submission Date' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_LENDERSUBMISSIONDATE, // 'lenderSubmissionDate',
            'format' => null
        ],

        'Resolution Type' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_RESOLUTIONTYPE, // 'resolutionType',
            'format' => null
        ],

        'Closed Disposition' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_CLOSEDDISPOSITION, // 'closedDisposition',
            'format' => null
        ],

        'Checklist Status' => [
            'table'    => null,
            'column'   => 'DocumentStatus', // 'DocumentStatus',
            'function' => '_ChecklistStatus',
            'format'   => null
        ],

        'Checklist Status Count' => [
            'table'    => null,
            'column'   => 'DocumentCount', // 'DocumentCount',
            'function' => '_ChecklistStatus',
            'format'   => null
        ],

        'Appeal Date' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_APPEALDATE, // 'appealDate',
            'format' => null
        ],

        'Attorney Review Date' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_ATTORNEYREVIEWDATE, // 'attorneyReviewDate',
            'format' => null
        ],

        'Date of 1st Trial Payment' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_TRIALPAYMENTDATE1, // 'trialPaymentDate1',
            'format' => null
        ],

        'Date of 2nd Trial Payment' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_TRIALPAYMENTDATE2, // 'trialPaymentDate2',
            'format' => null
        ],

        'Date of 3rd Trial Payment' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_TRIALPAYMENTDATE3, // 'trialPaymentDate3',
            'format' => null
        ],

        'Amount of 1st Mod Payment' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_FIRSTMODPAYMENTAMT, // 'firstModPaymentAmt',
            'format' => null
        ],

        'Retainer Date' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_RETAINERDATE, // 'retainerDate',
            'format' => null
        ],

        'Escalation Date' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_ESCALATIONDATE, // 'escalationDate',
            'format' => null
        ],

        'Denial Date' => [
            'table'  => tblFileResponse_db::TABLE,
            'column' => tblFileResponse_db::COLUMN_DENIALDATE, // 'denialDate',
            'format' => null
        ],

        'Listing Realtor' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_REALTOR, // 'realtor',
            'format' => null
        ],

        'Listing Price' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_LISTINGPRICE, // 'listingPrice',
            'format' => null
        ],

        'Listing Date' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_LISTINGDATE, // 'listingDate',
            'format' => null
        ],

        'Listing Address' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_REALTORADDRESS, // 'realtorAddress',
            'format' => null
        ],

        'Listing Phone' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_REALTORPHONENUMBER, // 'realtorPhoneNumber',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Listing Cell' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_SALES1CELLNO, // 'sales1CellNo',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Listing Agency' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_AGENCY, // 'agency',
            'format' => null
        ],

        'Listing Fax' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_SALES1FAX, // 'sales1Fax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Listing Email' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_REALTOREMAIL, // 'realtorEmail',
            'format' => null
        ],

        'Title Name' => [
            'table'  => tblFilePropertyInfo_db::TABLE,
            'column' => tblFilePropertyInfo_db::COLUMN_TITLENAME, // 'titleName',
            'format' => null
        ],

        'Title Ordered' => [
            'table'  => tblFilePropertyInfo_db::TABLE,
            'column' => tblFilePropertyInfo_db::COLUMN_TITLEORDEREDON, // 'titleOrderedOn',
            'format' => null
        ],

        'Title Report Date' => [
            'table'  => tblFilePropertyInfo_db::TABLE,
            'column' => tblFilePropertyInfo_db::COLUMN_TITLEREPORTDATE, // 'titleReportDate',
            'format' => null
        ],

        'Title Updated' => [
            'table'  => tblFilePropertyInfo_db::TABLE,
            'column' => tblFilePropertyInfo_db::COLUMN_TITLEUPDATEDON, // 'titleUpdatedOn',
            'format' => null
        ],

        'Title Representative' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => 'contact AS TitleContact', // 'contact AS TitleContact',
            'format' => null
        ],

        'Title Fax' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_SALES2FAX, // 'sales2Fax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'MLS Number' => [
            'table'    => null,
            'column'   => 'mlsNo', // 'mlsNo',
            'function' => '_ListingHistory',
            'format'   => null
        ],

        'Listing History' => [
            'table'    => null,
            'column'   => null, // '',
            'function' => '_ListingHistory',
            'format'   => null
        ],

        'Listing History Date' => [
            'table'    => null,
            'column'   => 'listingDate', // 'listingDate',
            'function' => '_ListingHistory',
            'format'   => null
        ],

        'Listing History Price' => [
            'table'    => null,
            'column'   => 'listingPrice', // 'listingPrice',
            'function' => '_ListingHistory',
            'format'   => null
        ],

        'Listing History Notes' => [
            'table'    => null,
            'column'   => 'listingNotes', // 'listingNotes',
            'function' => '_ListingHistory',
            'format'   => null
        ],

        'Buyer Deal 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1DEAL, // 'buyer1Deal',
            'format' => null
        ],

        'First Buyer Name' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYERNAME1, // 'buyerName1',
            'format' => null
        ],

        'First Co Buyer Name' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_COBUYERNAME1, // 'coBuyerName1',
            'format' => null
        ],

        'First Buyer Phone' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_FIRSTBUYERPHONE, // 'firstBuyerPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'First Buyer Email' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_FIRSTBUYEREMAIL, // 'firstBuyerEmail',
            'format' => null
        ],

        'Offer 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_OFFER1, // 'offer1',
            'format' => null
        ],

        'Sq Ft 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_SQFT1, // 'sqft1',
            'format' => null
        ],

        'Contract Date 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_CONTRACTDATE1, // 'contractDate1',
            'format' => null
        ],

        'Closing Date 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_CLOSINGDATE1, // 'closingDate1',
            'format' => null
        ],

        'Buyer Agent Name 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1AGENTNAME, // 'buyer1AgentName',
            'format' => null
        ],

        'First Buyer Agency' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1AGENCYNAME, // 'buyer1AgencyName',
            'format' => null
        ],

        'Buyer Phone 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1PHONE, // 'buyer1Phone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Buyer Cell 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1CELL, // 'buyer1Cell',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Buyer Fax 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1FAX, // 'buyer1Fax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Buyer Email 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1EMAIL, // 'buyer1Email',
            'format' => null
        ],

        'First Buyers Loan Officer Name' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1LONAME, // 'buyer1LOName',
            'format' => null
        ],

        'First Buyers Loan Officer Company' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1LOCOMPANY, // 'buyer1LOCompany',
            'format' => null
        ],

        'First Buyers LO Phone' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1LOPHONE, // 'buyer1LOPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'First Buyers LO Cell' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1LOCELL, // 'buyer1LOCell',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'First Buyers LO Fax' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1LOFAX, // 'buyer1LOFax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'First Buyers LO Email' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1LOEMAIL, // 'buyer1LOEmail',
            'format' => null
        ],

        'Relationship to Seller 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1RELTOSELLER, // 'buyer1RelToSeller',
            'format' => null
        ],

        'Offer Notes 1' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER1NOTES, // 'buyer1Notes',
            'format' => null
        ],

        'Buyer Deal 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2DEAL, // 'buyer2Deal',
            'format' => null
        ],

        'Second Buyer Name' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYERNAME2, // 'buyerName2',
            'format' => null
        ],

        'Second Co Buyer Name' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYERNAME2, // 'coBuyerName2',
            'format' => null
        ],

        'Second Buyer Phone' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_SECONDBUYERPHONE, // 'secondBuyerPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Second Buyer Email' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_SECONDBUYEREMAIL, // 'secondBuyerEmail',
            'format' => null
        ],

        'Offer 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_OFFER2, // 'offer2',
            'format' => null
        ],

        'Sq Ft 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_SQFT2, // 'sqft2',
            'format' => null
        ],

        'Contract Date 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_CONTRACTDATE2, // 'contractDate2',
            'format' => null
        ],

        'Closing Date 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_CLOSINGDATE2, // 'closingDate2',
            'format' => null
        ],

        'Buyer Agent Name 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2AGENTNAME, // 'buyer2AgentName',
            'format' => null
        ],

        'Second Buyer Agency' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2AGENCYNAME, // 'buyer2AgencyName',
            'format' => null
        ],

        'Buyer Phone 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2PHONE, // 'buyer2Phone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Buyer Cell 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2CELL, // 'buyer2Cell',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Buyer Fax 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2FAX, // 'buyer2Fax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Buyer Email 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2EMAIL, // 'buyer2Email',
            'format' => null
        ],

        'Second Buyers Loan Officer Name' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2LONAME, // 'buyer2LOName',
            'format' => null
        ],

        'Second Buyers Loan Officer Company' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2LOCOMPANY, // 'buyer2LOCompany',
            'format' => null
        ],

        'Second Buyers LO Phone' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2LOPHONE, // 'buyer2LOPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Second Buyers LO Cell' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2LOCELL, // 'buyer2LOCell',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Second Buyers LO Fax' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2LOFAX, // 'buyer2LOFax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Second Buyers LO Email' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2LOEMAIL, // 'buyer2LOEmail',
            'format' => null
        ],

        'Relationship to Seller 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2RELTOSELLER, // 'buyer2RelToSeller',
            'format' => null
        ],

        'Offer Notes 2' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER2NOTES, // 'buyer2Notes',
            'format' => null
        ],

        'Buyer Deal 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3DEAL, // 'buyer3Deal',
            'format' => null
        ],

        'Third Buyer Name' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYERNAME3, // 'buyerName3',
            'format' => null
        ],

        'Third Co Buyer Name' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_COBUYERNAME3, // 'coBuyerName3',
            'format' => null
        ],

        'Third Buyer Phone' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_THIRDBUYERPHONE, // 'thirdBuyerPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Third Buyer Email' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_THIRDBUYEREMAIL, // 'thirdBuyerEmail',
            'format' => null
        ],

        'Offer 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_OFFER3, // 'offer3',
            'format' => null
        ],

        'Sq Ft 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_SQFT3, // 'sqft3',
            'format' => null
        ],

        'Contract Date 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_CONTRACTDATE3, // 'contractDate3',
            'format' => null
        ],

        'Closing Date 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_CLOSINGDATE3, // 'closingDate3',
            'format' => null
        ],

        'Buyer Agent Name 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3AGENTNAME, // 'buyer3AgentName',
            'format' => null
        ],

        'Third Buyer Agency' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3AGENCYNAME, // 'buyer3AgencyName',
            'format' => null
        ],

        'Buyer Phone 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3PHONE, // 'buyer3Phone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Buyer Cell 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3CELL, // 'buyer3Cell',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Buyer Fax 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3FAX, // 'buyer3Fax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Buyer Email 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3EMAIL, // 'buyer3Email',
            'format' => null
        ],

        'Third Buyers Loan Officer Name' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3LONAME, // 'buyer3LOName',
            'format' => null
        ],

        'Third Buyers Loan Officer Company' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3LOCOMPANY, // 'buyer3LOCompany',
            'format' => null
        ],

        'Third Buyers LO Phone' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3LOPHONE, // 'buyer3LOPhone',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Third Buyers LO Cell' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3LOCELL, // 'buyer3LOCell',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Third Buyers LO Fax' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3LOFAX, // 'buyer3LOFax',
            'format' => Database2::DATATYPE_PHONE_NUMBER
        ],

        'Third Buyers LO Email' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3LOEMAIL, // 'buyer3LOEmail',
            'format' => null
        ],

        'Relationship to Seller 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3RELTOSELLER, // 'buyer3RelToSeller',
            'format' => null
        ],

        'Offer Notes 3' => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_BUYER3NOTES, // 'buyer3Notes',
            'format' => null
        ],

        'Estimated Value' => [
            'table'    => null,
            'column'   => 'cmaEstimatedValue', // 'cmaEstimatedValue',
            'function' => '_CMA',
            'format'   => null
        ],

        'High Price' => [
            'table'    => null,
            'column'   => 'cmaHighPrice', // 'cmaHighPrice',
            'function' => '_CMA',
            'format'   => null
        ],

        'Suggested List Price' => [
            'table'    => null,
            'column'   => 'cmaSuggListPrice', // 'cmaSuggListPrice',
            'function' => '_CMA',
            'format'   => null
        ],

        'Quick Resale Price' => [
            'table'    => null,
            'column'   => 'cmaQuickResalePrice', // 'cmaQuickResalePrice',
            'function' => '_CMA',
            'format'   => null
        ],

        'Sale Price' => [
            'table'    => null,
            'column'   => 'cmaSalePrice', // 'cmaSalePrice',
            'function' => '_CMA',
            'format'   => null
        ],

        'Months on the MLS' => [
            'table'    => null,
            'column'   => 'cmaMonthsOnMLS', // 'cmaMonthsOnMLS',
            'function' => '_CMA',
            'format'   => null
        ],

        'Average Listing Price' => [
            'table'    => null,
            'column'   => 'cmaAverageListingPrice', // 'cmaAverageListingPrice',
            'function' => '_CMA',
            'format'   => null
        ],

        'CMA Description' => [
            'table'    => null,
            'column'   => 'cmaDesc', // 'cmaDesc',
            'function' => '_CMA',
            'format'   => null
        ],

        'Max LTV%' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_MAXLTVPERCENT, // 'maxLTVPercent',
            'format' => Database2::DATATYPE_PERCENT_STR,
        ],

        'Minimum Maintained DSCR Ratio' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_MINDSCRRATIO, // 'minDSCRRatio',
            'format' => Database2::DATATYPE_PERCENT_STR,
        ],

        'Minimum Account Balance with Lender' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_MINACTBAL, // 'minActBal',
            'format' => Database2::DATATYPE_MONEY_STR,
        ],

        'Floor Rate' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_FLOORRATE, // 'floorRate',
            'format' => Database2::DATATYPE_PERCENT_STR,
        ],

        'Prepayment Penalty Lockout Period in Months' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_PREPAYLOCKOUT, // 'prepayLockOut',
            'format' => Database2::DATATYPE_NUMBER,
        ],

        'Is this loan an adjustable or a fixed rate mortgage?' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_ISADJUSTABLE, // 'isAdjustable',
            'format' => null
        ],

        'Will there be a Pre-Stabilized and Post-Stabilized Period for this Loan?' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_ISPRESTABILIZED, // 'isPreStabilized',
            'format' => null
        ],
        'Initial Term in Years'                                                    => [
            'table'    => null,
            'column'   => 'initialTerminYears',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        'Initial Interest Rate Margin' => [
            'table'    => null,
            'column'   => 'initialInterestRateMargin',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        'Initial Interest Rate Index' => [
            'table'    => null,
            'column'   => 'initialInterestRateIndex',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        'Initial Amortization' => [
            'table'    => null,
            'column'   => 'initialAmortization',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        'Initial Term Adjustment Period' => [
            'table'    => null,
            'column'   => 'initialTermAdjustmentPeriod',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        'Indicated Rate As Of' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_INDICATEDRATEDATE, // 'indicatedRateDate',
            'format' => null
        ],

        'Indicated Rate%' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_INDICATEDRATEPERCENT, // 'indicatedRatePercent',
            'format' => null
        ],

        'Post Stabilized Rate Margin' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_STABILIZEDRATEMARGIN, // 'stabilizedrateMargin',
            'format' => null
        ],

        'Post Stabilized Rate Index' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_STABILIZEDRATEINDEX, // 'stabilizedRateIndex',
            'format' => null
        ],

        'Post Stabilized Rate Floor' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_STABILIZEDRATEFLOOR, // 'stabilizedRateFloor',
            'format' => null
        ],

        'Post Stabilized Amoritization' => [
            'table'  => tblLoanSetting_db::TABLE,
            'column' => tblLoanSetting_db::COLUMN_STABILIZEDAMOR, // 'stabilizedAmor',
            'format' => null
        ],

        //        'Will there be a 2nd Term to this loan?' => [
        //            'table' => tblLoanSetting_db::TABLE,
        //            'column'   => aaa::aaa, // 'isSecondTerm',
        //            'format' => null
        //        ], // not in database

        '2nd Term in Years' => [
            'table'    => null,
            'column'   => 'secondTerminYears',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        '2nd Rate Margin' => [
            'table'    => null,
            'column'   => 'secondInterestRateMargin', // 'RateMargin',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        '2nd Rate Index' => [
            'table'    => null,
            'column'   => 'secondInterestRateIndex', // 'RateMargin',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        '2nd Rate Amortization' => [
            'table'    => null,
            'column'   => 'secondAmortization', // 'RateMargin',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        '2nd Term Adjustment Period' => [
            'table'    => null,
            'column'   => 'secondTermAdjustmentPeriod', // 'RateMargin',
            'function' => '_LoanSetupInfo',
            'params'   => [],
            'format'   => null
        ],

        'Name and Version of Credit Scoring Model' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BORROWERCREDITSCORINGMODELOFAPPLICANT, // 'borrowerCreditScoringModelOfApplicant',
            'format' => tblCustomFieldType::borrowerCreditScoringModel
        ],

        'Name and Version of Credit Scoring Model: Conditional Free Form Text Field' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BORROWERCREDITSCORINGMODELCONDITIONALFREEOFAPPLICANT, // 'borrowerCreditScoringModelConditionalFreeOfApplicant',
            'format' => null
        ],

        'Was the ethnicity of the Borrower collected on the basis of visual observation or surname' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BFIETHNICITY, // 'bFiEthnicity',
            'format' => null
        ],

        'Was the sex of the Borrower collected on the basis of visual observation or surname' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BFISEX, // 'bFiSex',
            'format' => null
        ],

        'Was the race of the Borrower collected on the basis of visual observation or surname' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BFIRACE, // 'bFiRace',
            'format' => null
        ],

        'The Demographic Information was provided through' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BDEMOINFO, // 'bDemoInfo',
            'format' => tblCustomFieldType::bDemoInfo
        ],
        'Exclude from HMDA Reporting'                      => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_EXCLUDEFROMHMDAREPORT,
            'format' => tblCustomFieldType::excludeFromHMDAReport,
        ],
        'Action Date'                                      => [
            'table'  => tblAdverseAction_db::TABLE,
            'column' => tblAdverseAction_db::COLUMN_ACTIONDATE,
            'format' => Database2::DATATYPE_DATE
        ],

        'Co-Borrower Name and Version of Credit Scoring Model' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_COBORROWERCREDITSCORINGMODELOFAPPLICANT, // 'coBorrowerCreditScoringModelOfApplicant',
            'format' => tblCustomFieldType::coBorrowerCreditScoringModel
        ],

        'Co-Borrower Name and Version of Credit Scoring Model: Conditional Free Form Text Field' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_COBORROWERCREDITSCORINGMODELCONDITIONALFREEOFAPPLICANT, // 'coBorrowerCreditScoringModelConditionalFreeOfApplicant',
            'format' => null
        ],

        'Was the ethnicity of the Co-Borrower collected on the basis of visual observation or surname' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBFIETHNICITY, // 'CBFiEthnicity',
            'format' => null
        ],

        'Was the sex of the Co-Borrower collected on the basis of visual observation or surname' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBFIGENDER, // 'CBFiGender',
            'format' => null
        ],

        'Was the race of the Co-Borrower collected on the basis of visual observation or surname' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBFIRACE, // 'CBFiRace',
            'format' => null
        ],

        'Co-Borrower Demographic Information was provided through' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CBDDEMOINFO, // 'CBDDemoInfo',
            'format' => tblCustomFieldType::cbDemoInfo
        ],

        'Legal Entity Identifier (LEI)' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_LEGALENTITYIDENTIFIER, // 'legalEntityIdentifier',
            'format' => null
        ],

        'Introductory Rate Period' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_INTRODUCTORYRATEPERIOD, // 'introductoryRatePeriod',
            'format' => Database2::DATATYPE_NUMBER,
        ],

        'Universal Loan Identifier (ULI)' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_UNIVERSALLOANIDENTIFIER, // 'universalLoanIdentifier',
            'format' => null
        ],

        'Interest-Only Payments' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_INTERESTONLYPAYMENT, // 'interestOnlyPayment',
            'format' => Database2::DATATYPE_NUMBER,
        ],

        'HMDA Loan Purpose' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_HMDALOANPURPOSE, // 'hmdaLoanPurpose',
            'format' => Database2::DATATYPE_HMDA_LOAN_PURPOSE_STR,
        ],

        'HMDA Loan Amount' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_HMDALOANAMOUNT, // 'hmdaLoanAmount',
            'format' => Database2::DATATYPE_MONEY_STR,
        ],

        'HMDA Loan Type' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_HMDALOANTYPE, // 'loanType',
            'format' => Database2::DATATYPE_HMDA_LOAN_TYPE_STR,
        ],

        'Action Taken' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_ACTIONTAKEN, // 'actionTaken',
            'format' => Database2::DATATYPE_ACTION_TAKEN_STR,
        ],

        'Type Of Purchaser' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_TYPEOFPURCHASER, // 'typeOfPurchaser',
            'format' => Database2::DATATYPE_TYPE_OF_PURCHASER_STR
        ],

        'Total Units' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_TOTALUNITS, // 'totalUnits',
            'format' => Database2::DATATYPE_NUMBER,
        ],

        'Reason For Denial' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_REASONFORDENIAL, // 'reasonForDenial',
            'format' => Database2::DATATYPE_REASON_FOR_DENIAL_STR,
        ],

        'Submission Of Application From' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_SUBMISSIONOFAPPLICATIONFROM, // 'submissionOfApplicationFrom',
            'format' => null
        ],

        'Combined Loan-To-Value Ratio' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_COMBINEDLOANTOVALUERATIO, // 'combinedLoanToValueRatio',
            'format' => null
        ],

        'Census Tract' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CENSUSTRACT, // 'censusTract',
            'format' => null
        ],

        'Manufactured Home Secured Property Type' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_MANUFACTUREDHOMESECUREDPROPERTYTYPE, // 'manufacturedHomeSecurePropertyType',
            'format' => Database2::DATATYPE_MANUFACTURED_HOME_SECURED_PROPERTY_TYPE_STR
        ],

        'Manufactured Home Land Property Interest' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_MANUFACTUREDHOMELANDPROPERTYINTEREST, // 'manufacturedHomePropertyInterest',
            'format' => Database2::DATATYPE_MANUFACTURED_HOME_LAND_PROPERTY_INTEREST_STR
        ],

        'Preapproval' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_PREAPPROVAL, // 'preapproval',
            'format' => Database2::DATATYPE_PREAPPROVAL_STR
        ],

        'Multifamily Affordable Units' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_MULTIFAMILYAFFORDABLEUNITS, // 'multifamilyAffordableUnits',
            'format' => null
        ],

        'Construction Method' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_CONSTRUCTIONMETHOD, // 'constructionMethod',
            'format' => Database2::DATATYPE_CONSTRUCTION_METHOD_STR
        ],

        'Application Channel' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_APPLICATIONCHANNEL, // 'applicationChannel',
            'format' => Database2::DATATYPE_APPLICATION_CHANNEL_STR
        ],

        'Initially Payable To Your Institution' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_INITIALLYPAYABLETOYOURINSTITUTION, // 'initiallyPayableToYourInstitution',
            'format' => Database2::DATATYPE_INITIALLY_PAYABLE_TO_YOUR_INSTITUTION_STR
        ],

        'Rate Spread' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_RATESPREAD, // 'rateSpread',
            'format' => null
        ],

        'HOEPA Status' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_HOEPASTATUS, // 'HOEPAStatus',
            'format' => Database2::DATATYPE_HOEPA_STATUS_STR
        ],

        'Total Loan Cost Or Total Points And Fees' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_TOTALLOANCOSTORTOTALPOINTSANDFEES, // 'totalLoanCostOrTotalPointsAndFees',
            'format' => null
        ],

        'Origination Charges' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_ORIGINATIONCHARGES, // 'originationCharges',
            'format' => null
        ],

        'Discount Points' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_DISCOUNTPOINTS, // 'discountPoints',
            'format' => null
        ],

        'Lender Credits' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_LENDERCREDITS, // 'lenderCredits',
            'format' => null
        ],

        'HMDA Prepayment Penalty' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_HMDAPREPAYMENTPENALTY, // 'HMDAPrepaymentPenalty',
            'format' => null
        ],

        'Debt-To-Income Ratio' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_DEBTTOINCOMERATIO, // 'debtToIncomeRatio',
            'format' => null
        ],

        'Balloon Payment' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BALLOONPAYMENT, // 'balloonPayment',
            'format' => Database2::DATATYPE_BALLOON_PAYMENT_STR
        ],

        'Automated Underwriting System' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_AUTOMATEDUNDERWRITINGSYSTEM, // 'automatedUnderwritingSystem',
            'format' => Database2::DATATYPE_AUTOMATED_UNDERWRITING_SYSTEM_STR
        ],

        'AUS: Free Form Text Field for Code 5' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_AUSFREEFORMTEXTFIELDFORCODE5, // 'ausFreeFormTextFieldForCode5',
            'format' => null
        ],

        'Automated Underwriting System Result' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_AUTOMATEDUNDERWRITINGSYSTEMRESULT, // 'automatedUnderwritingSystemResult',
            'format' => Database2::DATATYPE_AUTOMATED_UNDERWRITING_SYSTEM_RESULT_STR
        ],

        'AUS Result: Free Form Text Field for Code 16' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_AUSRESULTFREEFORMTEXTFIELDFORCODE16, // 'ausResultFreeFormTextFieldForCode16',
            'format' => null
        ],

        'Reverse Mortgage' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_REVERSEMORTGAGE, // 'reverseMortgage',
            'format' => Database2::DATATYPE_REVERSE_MORTGAGE_STR
        ],

        'Open-End Line Of Credit' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_OPENENDLINEOFCREDIT, // 'openEndLineOfCredit',
            'format' => Database2::DATATYPE_OPEN_END_LINE_OF_CREDIT_STR
        ],

        'Business Or Commercial Purpose' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_BUSINESSORCOMMERCIALPURPOSE, // 'businessOrCommercialPurpose',
            'format' => Database2::DATATYPE_BUSINESS_OR_COMMERCIAL_PURPOSE_STR
        ],

        'Are There Interest-Only Payments' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_ARETHEREINTERESTONLYPAYMENT, // 'areThereInterestOnlyPayment',
            'format' => Database2::DATATYPE_ARE_THERE_INTEREST_ONLY_PAYMENT_STR
        ],

        'Negative Amortization' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_NEGATIVEAMORTIZATION, // 'negativeAmortization',
            'format' => Database2::DATATYPE_NEGATIVE_AMORTIZATION_STR
        ],

        'Other Non-Amortizing Features' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_OTHERNONAMORTIZINGFEATURES, // 'otherNonAmortizingFeatures',
            'format' => Database2::DATATYPE_OTHER_NON_AMORTIZING_FEATURES_STR
        ],

        'County Code' => [
            'table'  => tblQAInfo_db::TABLE,
            'column' => tblQAInfo_db::COLUMN_COUNTYCODE, // 'countyCode',
            'format' => null
        ],

        'Previous Status' => [
            'table'    => null,
            'column'   => 'previousStatus', // 'previousStatus',
            'function' => '_DaysInPreviousStatus',
            'format'   => null
        ],

        'No Of Days' => [
            'table'    => null,
            'column'   => 'previousStatusDays', // 'previousStatusDays',
            'function' => '_DaysInPreviousStatus',
            'format'   => null
        ],

        'All Custom Fields' => [
            'function' => '_CustomFields',
            'column'   => 'customFields', // 'customFields',
        ],

        'CMA Analysis' => [
            'function' => '_CMA',
        ],

        'Total Loan Amount V2'          => [
            'table'  => tblLoanPropertySummary_db::TABLE,
            'column' => tblLoanPropertySummary_db::COLUMN_TOTALPROPERTIESLOANAMOUNT, // 'totalPropertiesLoanAmount',
            'format' => null,
        ],
        'PITIA - Loan Info V2'          => [
            'table'  => tblLoanPropertySummary_db::TABLE,
            'column' => tblLoanPropertySummary_db::COLUMN_TOTALPROPERTIESPITIA, // 'totalPropertiesPITIA',
            'format' => null,
        ],
        'Lock Expiration Date'          => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_RATELOCKEXPIRATIONDATE, // 'rateLockExpirationDate',
            'format' => null,
        ],
        'Rate Lock Date'                => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_RATELOCKDATE, // 'rateLockDate',
            'format' => null,
        ],
        'Rate Lock Period'              => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_RATELOCKPERIOD, // 'rateLockPeriod',
            'format' => null,
        ],
        'Rate Lock Expiration Date'     => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_RATELOCKEXPIRATIONDATE, // 'rateLockExpirationDate',
            'format' => null,
        ],
        'PSA Closing Date'              => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_PSACLOSINGDATE,
            'format' => null,
        ],
        'Building Analysis Outstanding' => [
            'table'  => tblBuildingAnalysisOutstanding_db::TABLE,
            'column' => tblBuildingAnalysisOutstanding_db::COLUMN_BUILDINGANALYSISOUTSTANDINGNAME,
            'format' => null,
        ],
        'Building Analysis Need'        => [
            'table'  => tblBuildingAnalysisNeed_db::TABLE,
            'column' => tblBuildingAnalysisNeed_db::COLUMN_BUILDINGANALYSISNEEDNAME,
            'format' => null,
        ],
        'Building Analysis Due Date'    => [
            'table'  => tblFileHMLONewLoanInfo_db::TABLE,
            'column' => tblFileHMLONewLoanInfo_db::COLUMN_BUILDINGANALYSISDUEDATE,
            'format' => null,
        ],
        'Authorization Status'          => [
            'table'  => tblBorrowerAuthorizationStatus_DB::TABLE,
            'column' => tblBorrowerAuthorizationStatus_DB::COLUMN_STATUS_DESCRIPTION,
            'format' => null,
        ],
        'VOM Status'                    => [
            'table'  => tblVOMPayoffStatus_DB::TABLE,
            'column' => tblVOMPayoffStatus_DB::COLUMN_VOMPAYOFFSTATUSDESCRIPTION,
            'format' => null,
        ],
        'Payoff Status'                 => [
            'table'  => tblVOMPayoffStatus_DB::TABLE,
            'column' => tblVOMPayoffStatus_DB::COLUMN_VOMPAYOFFSTATUSDESCRIPTION,
            'format' => null,
        ],
        'Track Record'                  => [
            'table'  => tblBorrowerExperienceTrackRecord_DB::TABLE,
            'column' => tblBorrowerExperienceTrackRecord_DB::COLUMN_TRACK_RECORD_DESCRIPTION,
            'format' => null,
        ],
        'Welcome Call'                  => [
            'table'  => tblWelcomeCallStatus_DB::TABLE,
            'column' => tblWelcomeCallStatus_DB::COLUMN_CALLSTATUS,
            'format' => null,
        ],
        'Appraisal Status'              => [
            'table'  => tblPropertyAppraisalStatuses_DB::TABLE,
            'column' => tblPropertyAppraisalStatuses_DB::COLUMN_APPRAISALSTATUSESNAME,
            'format' => null,
        ],
        'Cost Spent'                    => [
            'table'  => tblShortSale_db::TABLE,
            'column' => tblShortSale_db::COLUMN_COSTSPENT, // 'costSpent',
            'format' => null,
        ],

        //Entity Members
        'Member Type' => [
            'table'    => null,
            'column'   => 'memberType',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Category' => [
            'table'    => null,
            'column'   => 'memberCategory',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Name' => [
            'table'    => null,
            'column'   => 'memberName',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Title' => [
            'table'    => null,
            'column'   => 'memberTitle',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Ownership' => [
            'table'    => null,
            'column'   => 'memberOwnership',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Annual Salary' => [
            'table'    => null,
            'column'   => 'memberAnnualSalary',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Address' => [
            'table'    => null,
            'column'   => 'memberAddress',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Phone' => [
            'table'    => null,
            'column'   => 'memberPhone',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Cell' => [
            'table'    => null,
            'column'   => 'memberCell',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member SSN' => [
            'table'    => null,
            'column'   => 'memberSSN',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Date Of Birth' => [
            'table'    => null,
            'column'   => 'memberDOB',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Credit Score' => [
            'table'    => null,
            'column'   => 'memberCreditScore',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Email' => [
            'table'    => null,
            'column'   => 'memberEmail',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Drivers License' => [
            'table'    => null,
            'column'   => 'memberDriversLicense',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Drivers License State' => [
            'table'    => null,
            'column'   => 'memberDriversLicenseState',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member TIN' => [
            'table'    => null,
            'column'   => 'memberTin',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Personal Guarantee' => [
            'table'    => null,
            'column'   => 'memberPersonalGuarantee',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Authorized Signer' => [
            'table'    => null,
            'column'   => 'memberAuthorizedSigner',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Citizenship' => [
            'table'    => null,
            'column'   => 'memberCitizenship',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Marital Status' => [
            'table'    => null,
            'column'   => 'memberMaritalStatus',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Marriage Date' => [
            'table'    => null,
            'column'   => 'memberMarriageDate',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Divorce Date' => [
            'table'    => null,
            'column'   => 'memberDivorceDate',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Maiden Name' => [
            'table'    => null,
            'column'   => 'memberMaidenName',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
        'Member Spouse Name' => [
            'table'    => null,
            'column'   => 'memberSpouseName',
            'function' => '_EntityMembers',
            'format'   => null,
        ],
    ];

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _LoanPrograms(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_LoanPrograms');
        }

        $data = _LoanPrograms::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $report[$row->LMRId]->fromData($row->toArray());
        }
    }

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _InternalLoanPrograms(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_InternalLoanPrograms');
        }

        $data = _InternalLoanPrograms::getReport($SQL_LMRId, $params);
        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $report[$row->LMRId]->fromData($row->toArray());
        }
    }

    /**
     * @param static[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _RehabCostFinanced(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_RehabCostFinanced');
        }

        $data = _RehabCostFinanced::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            /* @var static $record */
            $record = $report[$row->LMRId];

            $record->RehabCostFinanced = floatval($row->rehabCostFinanced ?? 0);
            if (!$record->RehabCostFinanced) {
                $rehabCostPercentageFinanced = floatval($row->rehabCostPercentageFinanced ?? 0);
                $rehabCost = floatval($row->rehabCost);
                $record->RehabCostFinanced = $rehabCostPercentageFinanced / 100.0 * $rehabCost;
            }

            $report[$row->LMRId] = $record;
        }
    }

    private static ?bool $_LastAssignedEmployee = null;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _LastAssignedEmployee(array &$report, string $SQL_LMRId, array $params)
    {
        if (!is_null(self::$_LastAssignedEmployee)) {
            return;
        }

        $data = _LastAssignedEmployee::getReport($SQL_LMRId, $params);
        foreach ($data as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $report[$row->LMRId]->EmployeeName = $row->EmployeeName;
            $report[$row->LMRId]->EmployeeCell = $row->PhoneCell;
            $report[$row->LMRId]->EmployeePhone = $row->PhoneDirect;
            $report[$row->LMRId]->EmployeeEmail = $row->EmailAddress;
            $report[$row->LMRId]->EmployeeFax = $row->PhoneFax;
        }
        self::$_LastAssignedEmployee = true;
    }

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _assignedEmployees(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_assignedEmployees');
        }

        $data = _assignedEmployees::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $employeeInfo = [];
            $employeephone = trim($row->PhoneDirect);
            $employeecell = trim($row->PhoneCell);
            $employeefax = trim($row->PhoneFax);
            $employeeemail = trim($row->EmailAddress);

            if (in_array('Assigned Employees', self::$selectedHeaders)) {
                $employeeInfo[] = $row->EmployeeName;
            }
            $employeeInfo = implode(', ', $employeeInfo);
            /** Multiple employee info in one variable as assigned employee. **/

            if ($report[$row->LMRId]->AssignedEmployees) {
                $report[$row->LMRId]->AssignedEmployees .= ', ';
            }
            $report[$row->LMRId]->AssignedEmployees .= $employeeInfo;
        }
    }

    protected static bool $_Draws = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _Draws(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_Draws');
        }

        if (self::$_Draws) {
            return;
        }

        $data = _Draws::getReport($SQL_LMRId, $params);

        $counts = [];

        foreach ($data as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            if (in_array('Draw Amount', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Draw Amount', $counts[$LMRId]),
                    'Draw Amount ' . $counts[$LMRId]
                );
                $report[$LMRId]->_DrawAmount[$counts[$LMRId]] = $row->drawFunded;
            }

            if (in_array('Draw Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Draw Date', $counts[$LMRId]),
                    'Draw Date ' . $counts[$LMRId]
                );
                $report[$LMRId]->_DrawDate[$counts[$LMRId]] = $row->dateFunded;
            }
        }

        self::unsetHeader('Draw Amount');
        self::unsetHeader('Draw Date');

        self::$_Draws = true;
    }

    protected static bool $_Insurance = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _Insurance(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_Insurance');
        }


        if (self::$_Insurance) {
            return;
        }

        $data = _Insurance::getReport($SQL_LMRId, $params);

        $counts = [];
        foreach ($data as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            $report[$row->LMRId]->_policyType[$counts[$LMRId]] = $row->policyType;

            if (in_array('Name of Carrier', self::$selectedHeaders)) {
                $report[$row->LMRId]->_policyCarrier[$counts[$LMRId]] = $row->policyCarrier;
            }

            if (in_array('Policy Name', self::$selectedHeaders)) {
                $report[$row->LMRId]->_policyName[$counts[$LMRId]] = $row->policyName;
            }

            if (in_array('Insurance Policy Number', self::$selectedHeaders)) {
                $report[$row->LMRId]->_policyNumber[$counts[$LMRId]] = $row->policyNumber;
            }

            if (in_array('Annual Premium', self::$selectedHeaders)) {
                $report[$row->LMRId]->_policyAnnualPremium[$counts[$LMRId]] = $row->policyAnnualPremium;

            }

            // arrays of values must have a leading _
            // the actual value is put into a string/float with the name without the leading _
            if (in_array('Policy Expiration Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_policyExpDate[$counts[$LMRId]] = $row->policyExpDate;
            }

            if (in_array('Policy Effective Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_policyEffDate[$counts[$LMRId]] = $row->policyEffDate;
            }

            if (in_array('Insurance Date Received', self::$selectedHeaders)) {
                $report[$row->LMRId]->_insuranceDateReceived[$counts[$LMRId]] = $row->insuranceDateReceived;
            }

            if (in_array('Insurance Date Ordered', self::$selectedHeaders)) {
                $report[$row->LMRId]->_insuranceDateOrdered[$counts[$LMRId]] = $row->insuranceDateOrdered;
            }
        }

        $counts = [];
        foreach ($data as $row) {

            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            if (in_array('Types of Required Insurance', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Types of Required Insurance', $counts[$LMRId]),
                    'Types of Required Insurance ' . $counts[$LMRId]);
            }

            if (in_array('Name of Carrier', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Name of Carrier', $counts[$LMRId]),
                    'Name of Carrier ' . $counts[$LMRId]);
            }

            if (in_array('Policy Name', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Policy Name', $counts[$LMRId]),
                    'Policy Name ' . $counts[$LMRId]
                );
            }

            if (in_array('Insurance Policy Number', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Insurance Policy Number', $counts[$LMRId]),
                    'Policy Number ' . $counts[$LMRId]
                );
            }

            if (in_array('Annual Premium', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Annual Premium', $counts[$LMRId]),
                    'Annual Premium ' . $counts[$LMRId]
                );
            }

            if (in_array('Policy Expiration Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Policy Expiration Date', $counts[$LMRId]),
                    'Policy Expiry Date ' . $counts[$LMRId]);
            }
            if (in_array('Policy Effective Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Policy Effective Date', $counts[$LMRId]),
                    'Policy Effective Date ' . $counts[$LMRId]);
            }
            if (in_array('Insurance Date Received', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Insurance Date Received', $counts[$LMRId]),
                    'Insurance Date Received ' . $counts[$LMRId]);
            }
            if (in_array('Insurance Date Ordered', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Insurance Date Ordered', $counts[$LMRId]),
                    'Insurance Date Ordered ' . $counts[$LMRId]);
            }
        }
        self::unsetHeader('Types of Required Insurance');
        self::unsetHeader('Name of Carrier');
        self::unsetHeader('Policy Name');
        self::unsetHeader('Insurance Policy Number');
        self::unsetHeader('Annual Premium');
        self::unsetHeader('Policy Expiration Date');
        self::unsetHeader('Policy Expiry Date');
        self::unsetHeader('Policy Effective Date');
        self::unsetHeader('Insurance Date Received');
        self::unsetHeader('Insurance Date Ordered');

        self::$_Insurance = true;
    }


    protected static bool $_RefinanceCurrentMortgage = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */

    protected static function _RefinanceCurrentMortgage(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_RefinanceCurrentMortgage');
        }

        if (self::$_RefinanceCurrentMortgage) {
            return;
        }

        $data = _RefinanceCurrentMortgage::getReport($SQL_LMRId, $params);
        $counts = [];
        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;
            $record = $report[$row->LMRId];
            if (in_array('Original Purchase Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_originalPurchaseDate[$counts[$LMRId]] = $row->originalPurchaseDate;
            }
            if (in_array('Refinance Current Lender', self::$selectedHeaders)) {
                $report[$row->LMRId]->_refinanceCurrentLender[$counts[$LMRId]] = $row->refinanceCurrentLender;
            }
            if (in_array('Original Purchase Price', self::$selectedHeaders)) {
                $report[$row->LMRId]->_originalPurchasePrice[$counts[$LMRId]] = $row->originalPurchasePrice;
            }
            if (in_array('Cost of Improvements Made', self::$selectedHeaders)) {
                $report[$row->LMRId]->_costOfImprovementsMade[$counts[$LMRId]] = $row->costOfImprovementsMade;
            }
            if (in_array('Refinance Monthly Payment', self::$selectedHeaders)) {
                $report[$row->LMRId]->_refinanceMonthlyPayment[$counts[$LMRId]] = $row->refinanceMonthlyPayment;
            }
            if (in_array('Refinance Current Rate', self::$selectedHeaders)) {
                $report[$row->LMRId]->_refinanceCurrentRate[$counts[$LMRId]] = $row->refinanceCurrentRate;
            }
            if (in_array('Refinance Current Loan Balance', self::$selectedHeaders)) {
                $report[$row->LMRId]->_refinanceCurrentLoanBalance[$counts[$LMRId]] = $row->refinanceCurrentLoanBalance;
            }
            if (in_array('Good Through Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_goodThroughDate[$counts[$LMRId]] = $row->goodThroughDate;
            }
            if (in_array('Original Balance', self::$selectedHeaders)) {
                $report[$row->LMRId]->_subjectOriginalBalance[$counts[$LMRId]] = $row->subjectOriginalBalance;
            }
            if (in_array('Prepayment Penalty $', self::$selectedHeaders)) {
                $report[$row->LMRId]->_originalPrepayPenalty[$counts[$LMRId]] = $row->originalPrepayPenalty;
            }
            if (in_array('Prepayment Penalty %', self::$selectedHeaders)) {
                $report[$row->LMRId]->_originalPrepayPercentage[$counts[$LMRId]] = $row->originalPrepayPercentage;
            }
            if (in_array('Are taxes and insurance included in the payment?', self::$selectedHeaders)) {
                $report[$row->LMRId]->_originalTaxesIncluded[$counts[$LMRId]] = $row->originalTaxesIncluded;
            }
            if (in_array('Maturity Date of Loan', self::$selectedHeaders)) {
                $report[$row->LMRId]->_originalMaturityDate[$counts[$LMRId]] = $row->originalMaturityDate;
            }
            if (in_array('Original Pay Off Amount', self::$selectedHeaders)) {
                $report[$row->LMRId]->_originalPayOffAmount[$counts[$LMRId]] = $row->originalPayOffAmount;
            }
            if (in_array('Original Lien Number', self::$selectedHeaders)) {
                $report[$row->LMRId]->_originalLienNumber[$counts[$LMRId]] = $row->originalLienNumber;
            }
            if (in_array('Current Lender Full Address', self::$selectedHeaders)) {
                $report[$row->LMRId]->_refinanceCurrentLenderFullAddress[$counts[$LMRId]] = $row->refinanceCurrentLenderFullAddress;
            }
            if (in_array('Current Lender Email', self::$selectedHeaders)) {
                $report[$row->LMRId]->_refinanceCurrentLenderEmail[$counts[$LMRId]] = $row->refinanceCurrentLenderEmail;
            }
        }

        $counts = [];
        foreach ($data as $row) {
            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;
            if (in_array('Original Purchase Date', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Original Purchase Date', $counts[$LMRId]), 'Original Purchase Date ' . $counts[$LMRId]);
            }
            if (in_array('Refinance Current Lender', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Refinance Current Lender', $counts[$LMRId]), 'Refinance Current Lender ' . $counts[$LMRId]);
            }
            if (in_array('Original Purchase Price', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Original Purchase Price', $counts[$LMRId]), 'Original Purchase Price ' . $counts[$LMRId]);
            }
            if (in_array('Cost of Improvements Made', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Cost of Improvements Made', $counts[$LMRId]), 'Cost of Improvements Made ' . $counts[$LMRId]);
            }
            if (in_array('Refinance Monthly Payment', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Refinance Monthly Payment', $counts[$LMRId]), 'Refinance Monthly Payment ' . $counts[$LMRId]);
            }
            if (in_array('Refinance Current Rate', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Refinance Current Rate', $counts[$LMRId]), 'Refinance Current Rate ' . $counts[$LMRId]);
            }
            if (in_array('Refinance Current Loan Balance', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Refinance Current Loan Balance', $counts[$LMRId]), 'Refinance Current Loan Balance ' . $counts[$LMRId]);
            }
            if (in_array('Good Through Date', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Good Through Date', $counts[$LMRId]), 'Good Through Date ' . $counts[$LMRId]);
            }
            if (in_array('Original Balance', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Original Balance', $counts[$LMRId]), 'Original Balance ' . $counts[$LMRId]);
            }
            if (in_array('Prepayment Penalty $', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Prepayment Penalty $', $counts[$LMRId]), 'Prepayment Penalty $ ' . $counts[$LMRId]);
            }
            if (in_array('Prepayment Penalty %', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Prepayment Penalty %', $counts[$LMRId]), 'Prepayment Penalty % ' . $counts[$LMRId]);
            }
            if (in_array('Are taxes and insurance included in the payment?', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Are taxes and insurance included in the payment?', $counts[$LMRId]), 'Are taxes and insurance included in the payment? ' . $counts[$LMRId]);
            }
            if (in_array('Maturity Date of Loan', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Maturity Date of Loan', $counts[$LMRId]), 'Maturity Date of Loan ' . $counts[$LMRId]);
            }
            if (in_array('Original Pay Off Amount', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Original Pay Off Amount', $counts[$LMRId]), 'Original Pay Off Amount ' . $counts[$LMRId]);
            }
            if (in_array('Original Lien Number', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Original Lien Number', $counts[$LMRId]), 'Original Lien Number ' . $counts[$LMRId]);
            }
            if (in_array('Current Lender Full Address', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Current Lender Full Address', $counts[$LMRId]), 'Current Lender Full Address ' . $counts[$LMRId]);
            }
            if (in_array('Current Lender Email', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Current Lender Email', $counts[$LMRId]), 'Current Lender Email ' . $counts[$LMRId]);
            }
        }

        self::unsetHeader('Original Purchase Date');
        self::unsetHeader('Refinance Current Lender');
        self::unsetHeader('Original Purchase Price');
        self::unsetHeader('Cost of Improvements Made');
        self::unsetHeader('Refinance Monthly Payment');
        self::unsetHeader('Refinance Current Rate');
        self::unsetHeader('Refinance Current Loan Balance');
        self::unsetHeader('Good Through Date');
        self::unsetHeader('Original Balance');
        self::unsetHeader('Prepayment Penalty $');
        self::unsetHeader('Prepayment Penalty %');
        self::unsetHeader('Are taxes and insurance included in the payment?');
        self::unsetHeader('Maturity Date of Loan');
        self::unsetHeader('Original Pay Off Amount');
        self::unsetHeader('Original Lien Number');
        self::unsetHeader('Current Lender Full Address');
        self::unsetHeader('Current Lender Email');

        self::$_RefinanceCurrentMortgage = true;
    }

    protected static bool $_Appraisal = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _Appraisal(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_Insurance');
        }

        if (self::$_Appraisal) {
            return;
        }

        $columns = [];
        $columns['Appraiser Name'] = in_array('Appraiser Name', self::$selectedHeaders);
        $columns['Appraiser Phone Number'] = in_array('Appraiser Phone Number', self::$selectedHeaders);
        $columns['Appraiser Email'] = in_array('Appraiser Email', self::$selectedHeaders);
        $columns['Appraiser Company'] = in_array('Appraiser Company', self::$selectedHeaders);
        $columns['Appraised Value'] = in_array('Appraised Value', self::$selectedHeaders);
        $columns['Appraiser Rehabbed Value'] = in_array('Appraiser Rehabbed Value', self::$selectedHeaders);
        $columns['Appraiser Monthly Rent'] = in_array('Appraiser Monthly Rent', self::$selectedHeaders);
        $columns['Appraisal Date'] = in_array('Appraisal Date', self::$selectedHeaders);
        $columns['Appraiser Order Date'] = in_array('Appraiser Order Date', self::$selectedHeaders);

        for ($insEach = 0; $insEach <= 1; $insEach++) {
            $inScountVal = $insEach + 1;

            $data = _Appraisal::getReport($inScountVal, $SQL_LMRId, $params);

            foreach ($data as $row) {
                if (!isset($report[$row->LMRId])) {
                    $report[$row->LMRId] = new static();
                }

                $record = $report[$row->LMRId];

                if ($columns['Appraiser Name']) {
                    $record->_appraiserName[$inScountVal] = $row->appraiserName;
                }

                if ($columns['Appraiser Phone Number']) {
                    $record->_appraiserPhone[$inScountVal] = $row->appraiserPhone;
                }

                if ($columns['Appraiser Email']) {
                    $record->_appraiserEmail[$inScountVal] = $row->appraiserEmail;
                }

                if ($columns['Appraiser Company']) {
                    $record->_appraiserCompanyName[$inScountVal] = $row->appraiserCompany;
                }

                if ($columns['Appraised Value']) {
                    $record->_appraisedValue[$inScountVal] = $row->appraisedValue;
                }

                if ($columns['Appraiser Rehabbed Value']) {
                    $record->_rehabValue[$inScountVal] = $row->rehabValue;
                }

                if ($columns['Appraiser Monthly Rent']) {
                    $record->_monthlyRent[$inScountVal] = $row->twelveMonthRent;
                }

                if ($columns['Appraisal Date']) {
                    $record->_appraisalDateObtained[$inScountVal] = $row->dateObtained;
                }

                if ($columns['Appraiser Order Date']) {
                    $record->_appraisalOrderDate[$inScountVal] = $row->appraisalOrderDate;
                }

                $report[$row->LMRId] = $record;
            }
        }

        for ($insEach = 0; $insEach <= 1; $insEach++) {
            $inScountVal = $insEach + 1;
            if (in_array('Appraiser Name', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Appraiser Name', $inScountVal), 'Appraiser Name ' . $inScountVal);
            }

            if (in_array('Appraiser Phone Number', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Appraiser Phone Number', $inScountVal), 'Appraiser Phone Number ' . $inScountVal);
            }

            if (in_array('Appraiser Email', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Appraiser Email', $inScountVal), 'Appraiser Email ' . $inScountVal);
            }

            if (in_array('Appraiser Company', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Appraiser Company', $inScountVal), 'Appraiser Company ' . $inScountVal);
            }

            if (in_array('Appraised Value', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Appraised Value', $inScountVal), 'Appraised Value ' . $inScountVal);
            }

            if (in_array('Appraiser Rehabbed Value', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Appraiser Rehabbed Value', $inScountVal), 'Appraisal Rehabbed Value ' . $inScountVal);
            }

            if (in_array('Appraiser Monthly Rent', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Appraiser Monthly Rent', $inScountVal), 'Appraisal Monthly Rent ' . $inScountVal);
            }

            if (in_array('Appraisal Date', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Appraisal Date', $inScountVal), 'Appraisal Date ' . $inScountVal);
            }

            if (in_array('Appraiser Order Date', self::$selectedHeaders)) {
                self::addHeader(self::getColumnOrder('Appraiser Order Date', $inScountVal), 'Appraisal Order Date ' . $inScountVal);
            }
        }

        self::unsetHeader('Appraisal Date');
        self::unsetHeader('Appraisal Monthly Rent');
        self::unsetHeader('Appraisal Order Date');
        self::unsetHeader('Appraisal Rehabbed Value');
        self::unsetHeader('Appraised Value');
        self::unsetHeader('Appraiser 1');
        self::unsetHeader('Appraiser 2');
        self::unsetHeader('Appraiser Company');
        self::unsetHeader('Appraiser Email');
        self::unsetHeader('Appraiser Monthly Rent');
        self::unsetHeader('Appraisal Order Date');
        self::unsetHeader('Appraiser Name');
        self::unsetHeader('Appraiser Order Date');
        self::unsetHeader('Appraiser Phone Number');
        self::unsetHeader('Appraiser Rehabbed Value');

        self::$_Appraisal = true;
    }

    protected static bool $_Workflow = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _Workflow(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_Workflow');
        }

        if (self::$_Workflow) {
            return;
        }

        $workflow = workflow::getReport(self::$PCID);
        $workflow_step_days = workflow_step_days::getReport($SQL_LMRId, $params);

        foreach ($workflow as $item) {
            self::addHeader(
                self::getColumnOrder('WorkFlow: ' . $item->WFName . ' - ' . $item->steps),
                'WorkFlow: ' . $item->WFName . ' - ' . $item->steps
            );
        }

        foreach ($workflow_step_days as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->_workflowStepDays['WorkFlow: ' . $row->WFName . ' - ' . $row->steps] = $row->noOfDays;
        }

        self::$_Workflow = true;
    }

    protected static bool $_DaysInPreviousStatus = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _DaysInPreviousStatus(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_DaysInPreviousStatus');
        }

        if (self::$_DaysInPreviousStatus) {
            return;
        }

        self::addHeader(
            self::getColumnOrder('Days In Previous Status'),
            'Previous Status'
        );

        self::addHeader(
            self::getColumnOrder('Days In Previous Status'),
            'No Of Days'
        );

        $statuses = previous_status::getReport($SQL_LMRId, $params);

        foreach ($statuses as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->previousStatus = $row->primaryStatus;
            $report[$row->LMRId]->previousStatusDays = $row->nDays;
        }

        self::unsetHeader('Days In Previous Status');

        self::$_DaysInPreviousStatus = true;
    }

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _CustomFields(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_CustomFields');
        }

        $customFieldHeaders = [];
        $res = FieldsCustom::getReport(self::$PCID);
        $customFields = [];
        foreach ($res as $fields) {
            foreach ($fields as $field) {
                $customFields[] = $field->id;
            }
        }

        if (!sizeof($customFields)) {
            self::unsetHeader('All Custom Fields');
            return;
        }

        $customValues = custom_values::getReport($SQL_LMRId, $params, $customFields);

        foreach ($customValues as $cf) {
            if (!in_array($cf->Label, $customFieldHeaders)) {
                $customFieldHeaders[] = $cf->Label;
            }

            if (!isset($report[$cf->LMRId])) {
                $report[$cf->LMRId] = new static();
            }


            $item = tblCustomField::getCached($cf->tblCustomFieldId);

            self::addHeader(
                self::getColumnOrder('All Custom Fields', 900),
                'Custom Field: ' . $item->Label
            );

            $report[$cf->LMRId]->_customFields['Custom Field: ' . $item->Label] = $cf ? $cf->render($item, true) : '';
        }

        self::unsetHeader('All Custom Fields');
    }
    protected static function _eachCustomField(array &$report, string $SQL_LMRId, array $params, $header, $customId): void
    {

        if (static::$debug) {
            Log::Insert('_CustomFields');
        }
        $customFieldHeaders = [];
        $item = tblCustomField::getCached($customId);
        foreach ($params as $eachLMRId) {
            if (!isset($report[$eachLMRId])) {
                $report[$eachLMRId] = new static();
            }
            $report[$eachLMRId]->_customFields['Custom Field: ' . $item->Label] = '';
            self::addHeader(
                self::getColumnOrder('Custom_'.$customId, 0),
                'Custom Field: ' . $item->Label
            );
        }
        $customValues = custom_values::getReport($SQL_LMRId, $params, [$customId]);
        foreach ($customValues as $cf) {
            if (!in_array($cf->Label, $customFieldHeaders)) {
                $customFieldHeaders[] = $cf->Label;
            }
            $item = tblCustomField::getCached($cf->tblCustomFieldId);
            if($cf->LMRId) {
                $report[$cf->LMRId]->_customFields['Custom Field: ' . $item->Label] = $cf ? $cf->render($item, true) : '';
            }
        }

        self::unsetHeader('Custom_'.$customId);
    }

    protected static bool $_HUD = false;

    /**
     * @param static[] $report
     * @return void
     */
    protected static function _HUD(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_HUD');
        }

        if (static::$_HUD) {
            return;
        }

        $HUDValues = _HUD::getReport($SQL_LMRId, $params);
        $unsetHeaders = [];
        foreach ($HUDValues as $hud) {
            if (!isset($report[$hud->LMRId])) {
                $report[$hud->LMRId] = new static();
            }

            if ($hud->fieldID == 1001) {
                $header = "HUD 1001 Initial Deposit for Escrow Paid From Borrower Settlement";
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
                $header = str_replace("Borrower", "Seller", $header);
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
            } elseif ($hud->fieldID == 1002) {
                $header = "HUD 1002 Hazard Insurance Paid From Borrower Settlement";
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
                $header = str_replace("Borrower", "Seller", $header);
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
            } elseif ($hud->fieldID == 1003) {
                $header = "HUD 1003 Property Taxes Paid From Borrower Settlement";
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
                $header = str_replace("Borrower", "Seller", $header);
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
            } elseif ($hud->fieldID == 1004) {
                $header = "HUD 1004 Flood Insurance Paid From Borrower Settlement";
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
                $header = str_replace("Borrower", "Seller", $header);
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
            } elseif ($hud->fieldID == 1009) {
                $header = "HUD 1009 Aggregate Paid From Borrower Settlement";
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
                $header = str_replace("Borrower", "Seller", $header);
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
            } elseif ($hud->fieldID >= 1005 && $hud->fieldID <= 1008) {
                $header = "HUD $hud->fieldID Paid From Borrower Settlement";
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
                $header = str_replace("Borrower", "Seller", $header);
                $unsetHeaders[] = self::_HUDProcessData($report, $hud, $header);
            }
        }
        $unsetHeaders = array_unique($unsetHeaders);
        foreach ($unsetHeaders as $unsetHeader) {
            self::unsetHeader($unsetHeader ?? '');
        }

        static::$_HUD = true;
    }

    protected static function _HUDProcessData(array &$report, object $hud, string $header): ?string
    {
        if (in_array($header, self::$selectedHeaders)) {
            $_header = str_replace("HUD", "HUD:", $header);
            self::addHeader(self::getColumnOrder($header), $_header);
            if (stristr($_header, 'Borrower')) {
                $report[$hud->LMRId]->_HUDValues[$_header] = $hud->borrowerSettlementValue;
            } elseif (stristr($_header, 'Seller')) {
                $report[$hud->LMRId]->_HUDValues[$_header] = $hud->sellerSettlementValue;
            }
            return $header;
        }
        return null;
    }


    protected static bool $_FileURLs = false;

    /**
     * @param static[] $report
     * @return void
     */
    protected static function _FileURLs(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_FileURLs');
        }

        if (static::$_FileURLs) {
            return;
        }

        $fileURLs = _FileURLs::getReport($SQL_LMRId, $params);

        foreach ($fileURLs as $item) {
            if (!isset($report[$item->LMRId])) {
                $report[$item->LMRId] = new static();
            }

            $report[$item->LMRId]->LMRId = $item->LMRId;

            $LMRId = cypher::myEncryption($item->LMRId);
            $branchID = cypher::myEncryption($item->branchID);
            $LMRResponseId = cypher::myEncryption($item->LMRResponseId);
            $branchReferralCode = cypher::myEncryption($item->branchReferralCode);
            $agentReferralCode = $item->agentReferralCode;
            $fileModuleCode = $item->fileModuleCode;
            $clientfirstname = cypher::myEncryption($item->clientFirstName);
            $agentfirstname = cypher::myEncryption($item->agentFirstName);
            $clientemail = cypher::myEncryption($item->clientEmail);
            $agentemail = cypher::myEncryption($item->agentEmail);

            $branch = cypher::myEncryption('branch');
            $Email = cypher::myEncryption('Email');
            $agent = cypher::myEncryption('agent');
            $borrower = cypher::myEncryption('Borrower');
            $FA = cypher::myEncryption('FA');
            $QA = cypher::myEncryption('QA');
            $shareLink = cypher::myEncryption('shareLink');
            $agentBroker = cypher::myEncryption('Agent/Broker');
            $yes = cypher::myEncryption('yes');

            $report[$item->LMRId]->BackofficeFileLink = CONST_SITE_URL . 'backoffice/LMRequest.php?eId=' . $branchID . '&lId=' . $LMRId . '&rId=' . $LMRResponseId . '&op=a72f9e967052513d';
            $report[$item->LMRId]->FullAppForBorrower = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $FA . '&UType=' . $borrower;
            $report[$item->LMRId]->QuickAppForBorrower = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $QA . '&UType=' . $borrower;
            $report[$item->LMRId]->FullAppForBroker = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&aRc=' . $agentReferralCode . '&fOpt=' . $agent . '&lid=' . $LMRId . '&ft=' . $fileModuleCode . '&opt=' . $Email . '&op=' . $FA . '&UType=' . $agentBroker;
            $report[$item->LMRId]->QuickAppForBroker = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&aRc=' . $agentReferralCode . '&fOpt=' . $agent . '&lid=' . $LMRId . '&ft=' . $fileModuleCode . '&opt=' . $Email . '&op=' . $QA . '&UType=' . $agentBroker;
            $report[$item->LMRId]->UploadPortalForBorrower = CONST_SITE_URL . 'backoffice/uploadLMRDocs.php?lid=' . $LMRId . '&ft=' . $fileModuleCode . '&UType=' . $borrower . '&UName=' . $clientfirstname . '&email=' . $clientemail;
            $report[$item->LMRId]->UploadPortalForBroker = CONST_SITE_URL . 'backoffice/uploadLMRDocs.php?lid=' . $LMRId . '&ft=' . $fileModuleCode . '&UType=' . $agentBroker . '&UName=' . $agentfirstname . '&email=' . $agentemail;
            $report[$item->LMRId]->FABorrowerInfoRedacted = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $FA . '&sl=' . $shareLink . '&bir=' . $yes;
            $report[$item->LMRId]->FABorrowerInfoRedactedAndOfferSub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $FA . '&sl=' . $shareLink . '&bir=' . $yes . '&so=' . $yes;
            $report[$item->LMRId]->QABorrowerInfoRedacted = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $QA . '&sl=' . $shareLink . '&bir=' . $yes;
            $report[$item->LMRId]->QABorrowerInfoRedactedAndOfferSub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $QA . '&sl=' . $shareLink . '&bir=' . $yes . '&so=' . $yes;
            $report[$item->LMRId]->QAReadOnly = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $FA . '&sl=' . $shareLink;
            $report[$item->LMRId]->QAReadOnlyAndUploadPortal = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $QA . '&sl=' . $shareLink . '&aud=' . $yes;
            $report[$item->LMRId]->QAReadOnlyUploadPortalAndOfferSub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $QA . '&sl=' . $shareLink . '&aud=' . $yes . '&so=' . $yes;
            $report[$item->LMRId]->FAReadOnly = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $FA . '&sl=' . $shareLink;
            $report[$item->LMRId]->FAReadOnlyAndOfferSub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $FA . '&sl=' . $shareLink . '&so=' . $yes;
            $report[$item->LMRId]->FAReadOnlyAndUploadPortal = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $FA . '&sl=' . $shareLink . '&aud=' . $yes;
            $report[$item->LMRId]->FAReadOnlyUploadPortalAndOfferSub = CONST_SITE_URL . 'HMLOWebForm.php?bRc=' . $branchReferralCode . '&fOpt=' . $branch . '&lid=' . $LMRId . '&opt=' . $Email . '&ft=' . $fileModuleCode . '&op=' . $FA . '&sl=' . $shareLink . '&aud=' . $yes . '&so=' . $yes;
        }

        static::$_FileURLs = true;
    }

    public static function ValidateConfiguration()
    {
        static::_ValidateConfiguration(glPCHMLOExportData::$Fields);
        static::_ValidateConfiguration(glHeaderArray::$glHeaderArray);
    }

    private static function _ValidateConfiguration(array $headers)
    {
        $valid = true;
        $errors = 0;
        $known = [
            'Days in Current Status', // Days In Current Status
            'Insurance Policy Number', // Policy Number
            'Total Calls Placed',
            'ANI Number',
            'Bank Number',
            'Will there be a 2nd Term to this loan?', // no longer in database
            'Appraised Value', // Appraised As Is Value
            'Appraiser Rehabbed Value', // Appraisal Rehabbed Value
            'Appraiser Monthly Rent', // Appraisal Monthly Rent
            'Appraisal Date', // Appraisal Date Obtained
            'Appraiser Order Date', // Appraisal Order Date
            'Lien Amount',
            'Notes', // this is exported as a separate report
        ];

        foreach ($headers as $columns) {
            foreach ($columns as $column) {
                if (!isset(self::$headers[$column])) {
                    if (in_array($column, $known)) {
                        continue;
                    }
                    $errors++;
                    $valid = false;
                    Log::Insert($errors . ' _ValidateConfiguration - missing column - ' . $column);
                    continue;
                }

                $settings = self::$headers[$column];
                if (!($settings['column'] ?? null) && !($settings['function'] ?? null)) {
                    if (in_array($column, $known)) {
                        continue;
                    }
                    $errors++;
                    $valid = false;
                    Log::Insert($errors . ' _ValidateConfiguration - missing data - ' . $column);
                }
            }
        }

        if (!$valid) {
            Log::Insert('Fix Issues');
            exit;
        }
    }

    protected static bool $_LenderRevenue = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    public static function _LenderRevenue(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_LenderRevenue');
        }

        if (self::$_LenderRevenue) {
            return;
        }

        $data = _LenderRevenue::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->LenderRevenue = $row->LenderRevenue;

        }

        self::$_LenderRevenue = true;
    }

    protected static bool $_ClientType = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    public static function _ClientType(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_ClientType');
        }

        if (self::$_ClientType) {
            return;
        }

        $data = _ClientType::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->ClientType = $row->ClientType;

        }

        self::$_ClientType = true;
    }

    protected static bool $_NoOfPendingTasks = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    public static function _NoOfPendingTasks(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_NoOfPendingTasks');
        }

        if (self::$_NoOfPendingTasks) {
            return;
        }

        $data = _NoOfPendingTasks::getReport($SQL_LMRId, $params);
        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->noOfPendingTask = $row->noOfPendingTask;

        }

        self::$_NoOfPendingTasks = true;
    }

    protected static bool $_ChecklistStatus = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    public static function _ChecklistStatus(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_ChecklistStatus');
        }

        if (self::$_ChecklistStatus) {
            return;
        }

        $data = _ChecklistStatus::getReport(self::$PCID, $SQL_LMRId, $params);

        foreach ($data as $row) {

            if (in_array('Checklist Status Count', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Checklist Status Count'),
                    'Checklist Status Count: ' . $row->DocumentStatus
                );
            }
        }

        foreach ($data as $row) {
            if (in_array('Checklist Status', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Checklist Status'),
                    'Checklist Status: ' . $row->docName
                );
            }

        }

        foreach ($data as $row) {
            if (!$row->LMRId) {
                continue;
            }

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->LMRId = $row->LMRId;

            if (!isset($report[$row->LMRId]->_DocumentCount['Checklist Status Count: ' . $row->DocumentStatus])) {
                $report[$row->LMRId]->_DocumentCount['Checklist Status Count: ' . $row->DocumentStatus] = 0;
            }

            $report[$row->LMRId]->_DocumentCount['Checklist Status Count: ' . $row->DocumentStatus]++;

            $report[$row->LMRId]->_DocumentStatus['Checklist Status: ' . $row->docName] = $row->DocumentStatus;

        }

        foreach ($data as $row) {
            if ($row->LMRId) {
                continue;
            }

            foreach (self::$LMRIds as $LMRId) {
                if (!isset($report[$LMRId])) {
                    $report[$LMRId] = new static();
                }
                $report[$LMRId]->LMRId = $LMRId;

                if (!isset($report[$LMRId]->_DocumentCount['Checklist Status Count: ' . $row->DocumentStatus])) {
                    $report[$LMRId]->_DocumentCount['Checklist Status Count: ' . $row->DocumentStatus] = 0;
                }

                if (!isset($report[$LMRId]->_DocumentStatus['Checklist Status: ' . $row->docName])) {
                    $report[$LMRId]->_DocumentCount['Checklist Status Count: ' . $row->DocumentStatus]++;
                    $report[$LMRId]->_DocumentStatus['Checklist Status: ' . $row->docName] = $row->DocumentStatus;
                }

            }
        }
        self::unsetHeader('Checklist Status');
        self::unsetHeader('Checklist Status Count');

        self::$_ChecklistStatus = true;
    }

    protected static bool $_CMA = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _CMA(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_CMA');
        }

        if (self::$_CMA) {
            return;
        }

        $data = _CMA::getReport($SQL_LMRId, $params);

        $counts = [];
        foreach ($data as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            $report[$LMRId]->cmaEstimatedValue[$counts[$LMRId]] = $row->cmaEstimatedValue;
            $report[$LMRId]->cmaHighPrice[$counts[$LMRId]] = $row->cmaHighPrice;
            $report[$LMRId]->cmaSuggListPrice[$counts[$LMRId]] = $row->cmaSuggListPrice;
            $report[$LMRId]->cmaQuickResalePrice[$counts[$LMRId]] = $row->cmaQuickResalePrice;
            $report[$LMRId]->cmaSalePrice[$counts[$LMRId]] = $row->cmaSalePrice;
            $report[$LMRId]->cmaMonthsOnMLS[$counts[$LMRId]] = $row->cmaMonthsOnMLS;
            $report[$LMRId]->cmaAverageListingPrice[$counts[$LMRId]] = $row->cmaAverageListingPrice;
            $report[$LMRId]->cmaDesc[$counts[$LMRId]] = $row->cmaDesc;
        }

        $counts = [];
        foreach ($data as $row) {

            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            self::addHeader(
                self::getColumnOrder('Estimated Value', $counts[$LMRId]),
                'Estimated Value ' . $counts[$LMRId]);

            if (in_array('High Price', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('High Price', $counts[$LMRId]),
                    'High Price ' . $counts[$LMRId]);
            }

            if (in_array('Suggested List Price', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Suggested List Price', $counts[$LMRId]),
                    'Suggested List Price ' . $counts[$LMRId]
                );
            }

            if (in_array('Quick Resale Price', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Quick Resale Price', $counts[$LMRId]),
                    'Quick Resale Price ' . $counts[$LMRId]
                );
            }

            if (in_array('Sale Price', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Sale Price', $counts[$LMRId]),
                    'Sale Price ' . $counts[$LMRId]
                );
            }

            if (in_array('Months on the MLS', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Months on the MLS', $counts[$LMRId]),
                    'Months on the MLS ' . $counts[$LMRId]);
            }

            if (in_array('Average Listing Price', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Average Listing Price', $counts[$LMRId]),
                    'Average Listing Price ' . $counts[$LMRId]);
            }

            if (in_array('CMA Description', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('CMA Description', $counts[$LMRId]),
                    'CMA Description ' . $counts[$LMRId]);
            }
        }
        self::unsetHeader('CMA Analysis');
        self::unsetHeader('Estimated Value');
        self::unsetHeader('High Price');
        self::unsetHeader('Suggested List Price');
        self::unsetHeader('Quick Resale Price');
        self::unsetHeader('Sale Price');
        self::unsetHeader('Months on the MLS');
        self::unsetHeader('Average Listing Price');
        self::unsetHeader('CMA Description');

        self::$_CMA = true;
    }

    protected static bool $_ListingHistory = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _ListingHistory(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_ListingHistory');
        }

        if (self::$_ListingHistory) {
            return;
        }

        $data = _ListingHistory::getReport($SQL_LMRId, $params);

        $counts = [];
        foreach ($data as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            if (in_array('MLS Number', self::$selectedHeaders)) {
                $report[$row['LMRId']]->mlsNo[$counts[$LMRId]] = $row['mlsNo'];
            }

            if (in_array('Listing History Date', self::$selectedHeaders)) {
                $report[$row['LMRId']]->listingDate[$counts[$LMRId]] = $row['listingDate'];
            }

            if (in_array('Listing History Price', self::$selectedHeaders)) {
                $report[$row['LMRId']]->listingPrice[$counts[$LMRId]] = $row['listingPrice'];
            }

            if (in_array('Listing History Notes', self::$selectedHeaders)) {
                $report[$row['LMRId']]->listingNotes[$counts[$LMRId]] = $row['listingNotes'];

            }
        }

        $counts = [];
        foreach ($data as $row) {

            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            if (in_array('MLS Number', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('MLS Number', $counts[$LMRId]),
                    'MLS Number ' . $counts[$LMRId]);
            }

            if (in_array('Listing History Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Listing History Date', $counts[$LMRId]),
                    'Listing History Date ' . $counts[$LMRId]
                );
            }

            if (in_array('Listing History Price', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Listing History Price', $counts[$LMRId]),
                    'Listing History Price ' . $counts[$LMRId]
                );
            }

            if (in_array('Listing History Notes', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Listing History Notes', $counts[$LMRId]),
                    'Listing History Notes ' . $counts[$LMRId]
                );
            }
        }
        self::unsetHeader('Listing History');
        self::unsetHeader('MLS Number');
        self::unsetHeader('Listing History Date');
        self::unsetHeader('Listing History Price');
        self::unsetHeader('Listing History Notes');

        self::$_ListingHistory = true;
    }

    protected static bool $_TotalPITIA = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _TotalPITIA(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_CMA');
        }

        if (self::$_TotalPITIA) {
            return;
        }

        $data = _TotalPITIA::getReport($SQL_LMRId, $params);


        foreach ($data as $row) {

            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $report[$row->LMRId]->fromData($row->toArray());

        }

        self::$_TotalPITIA = true;
    }

    private static bool $_BankAttorney = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    public static function _BankAttorney(array &$report, string $SQL_LMRId, array $params)
    {
        if (self::$_BankAttorney) {
            return;
        }

        if (static::$debug) {
            Log::Insert('_BankAttorney');
        }

        $data = _BankAttorney::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }

            $report[$row->LMRId]->fromData($row->toArray());
        }

        self::$_BankAttorney = true;
    }

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    public static function _PayOffAmount(array &$report, string $SQL_LMRId, array $params)
    {
        // TODO: sort out the query to get all the required data for calculation
//        $payOffAmountArray = [
//            'paymentduedate' => $paymentduedate,
//            'payOffDate' => $payoffdate,
//            'currentLoanBalance' => self::$currentLoanBalance,
//            'totalDailyInterestCharge' => $totalDailyInterestCharge,
//            'totalMonthlyPayment' => self::$totalMonthlyPayment,
//            'amountdue' => $amountdue,
//        ];
//        $payoffamount = proposalFormula::calculatePayOffAmount($payOffAmountArray);
    }


    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _FileSubStatus(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_FileSubStatus');
        }

        $data = _FileSubStatus::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->substatus = $row->subStatus;

        }
    }

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _CreatedDateWithTimestamp(array &$report, string $SQL_LMRId, array $params)
    {
        global $userTimeZone;
        if (static::$debug) {
            Log::Insert('_CreatedDateWithTimestamp');
        }
        $data = _CreatedDateWithTimestamp::getReport($SQL_LMRId, $params);
        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $dateWithTimestamp = $row->recordDate;
            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
            $ipArray['outputZone'] = $userTimeZone;
            $ipArray['inputTime'] = $dateWithTimestamp;
            $dateWithTimestamp = Dates::timeZoneConversion($ipArray);
            $dateWithTimestamp = Dates::formatDateWithRE($dateWithTimestamp, 'YMD_HMS', 'm/d/Y h:i A');

            if ($dateWithTimestamp) {
                $dateWithTimestamp .= ' - ' . $userTimeZone;
            }
            $report[$row->LMRId]->createdDateWithTimestamp = $dateWithTimestamp;

        }
    }

    protected static function _LoanSetupInfo(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_LoanSetupInfo');
        }
        $data = _LoanSetupInfo::getReport($SQL_LMRId, $params);
        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            if (!isset($report[$row->LMRId]->initialTerminYears)) {
                $report[$row->LMRId]->initialTerminYears = $row->TermYears;
            } else {
                $report[$row->LMRId]->secondTerminYears = $row->TermYears;
            }
            if (!isset($report[$row->LMRId]->initialInterestRateMargin)) {
                $report[$row->LMRId]->initialInterestRateMargin = $row->RateMargin;
            } else {
                $report[$row->LMRId]->secondInterestRateMargin = $row->RateMargin;
            }
            if (!isset($report[$row->LMRId]->initialInterestRateIndex)) {
                $report[$row->LMRId]->initialInterestRateIndex = $row->RateIndex;
            } else {
                $report[$row->LMRId]->secondInterestRateIndex = $row->RateIndex;
            }
            if (!isset($report[$row->LMRId]->initialAmortization)) {
                $report[$row->LMRId]->initialAmortization = $row->RateAmortization;
            } else {
                $report[$row->LMRId]->secondAmortization = $row->RateAmortization;
            }
            if (!isset($report[$row->LMRId]->initialTermAdjustmentPeriod)) {
                $report[$row->LMRId]->initialTermAdjustmentPeriod = $row->TermAdjust;
            } else {
                $report[$row->LMRId]->secondTermAdjustmentPeriod = $row->TermAdjust;
            }

        }
    }

    protected static bool $_docStatus = false;

    /**
     * @param self[] $report
     * @param string $SQL_LMRId
     * @param array $params
     * @return void
     */
    protected static function _RequiredDocStatus(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_RequiredDocStatus');
        }

        if (self::$_docStatus) {
            return;
        }
        $docStatus = docStatusArray::getStatuses();
        foreach ($docStatus as $item) {
            self::addHeader(
                self::getColumnOrder('DocStatus: ' . $item->name),
                'DocStatus: ' . $item->name
            );
        }

        $fileModules = FileModules::getReport($SQL_LMRId, $params);
        $LMRClientTypes = LMRClientTypes::getReport($SQL_LMRId, $params);
        foreach ($params as $LMRId) {
            $RequiredDocsInfo = RequiredDocStatus::getReport(
                PageVariables::$PCID,
                $LMRId,
                $fileModules[array_search($LMRId, array_column($fileModules, 'fileID'))]->moduleCode,
                $LMRClientTypes[array_search($LMRId, array_column($LMRClientTypes, 'LMRID'))]->ClientType
            );
            foreach ($RequiredDocsInfo as $row) {
                if (!isset($report[$LMRId])) {
                    $report[$LMRId] = new static();
                }
                if (!$docStatus[$row->docStatusId]->name) {
                    $docName = $docStatus[array_search($row->docStatusId, array_column($docStatus, 'documentStatusId', 'id'))]->name;
                } else {
                    $docName = $docStatus[$row->docStatusId]->name;
                }
                $report[$LMRId]->requiredDocStatus['DocStatus: ' . $docName] = $row->docName;
            }
        }

        self::unsetHeader('Required Doc Status');
        self::$_docStatus = true;
    }

    protected static function _PrePaymentPenalty(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_PrePaymentPenalty');
        }

        $data = _PrePaymentPenalty::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->PrePaymentPenalty = $row->prePaymentPenaltyPercentage . ' % For ' . $row->prePaymentPenalty;
        }

    }

    protected static function _FloodZone(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_FloodZone');
        }

        $data = _FloodZone::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->propertyFloodZone = Property::$propertyFloodZoneList[$row->propertyFloodZone];
        }
    }

    protected static function _BalloonPayment(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_BalloonPayment');
        }

        $data = _BalloonPayment::getReport($SQL_LMRId, $params);

        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $report[$row->LMRId]->isThisBalloonPayment = $row->balloonPayment;
        }

    }

    protected static function _PurposeOfLoan(array &$report, string $SQL_LMRId, array $params)
    {
        if (static::$debug) {
            Log::Insert('_PurposeOfLoan');
        }

        $data = _PurposeOfLoan::getReport($SQL_LMRId, $params);
        foreach ($data as $row) {
            $PurposeOfLoan = [];
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            if ($row->purposeOfLoan) {
                $explodePurposeOfLoan = explode('~', $row->purposeOfLoan);
                foreach ($explodePurposeOfLoan as $eachPurposeOfLoan) {
                    $PurposeOfLoan[] = purposeOfLoanArray::$purposeOfLoanArray[$eachPurposeOfLoan];
                }
            }
            $report[$row->LMRId]->purposeOfLoan = implode(',', $PurposeOfLoan);
        }
    }

    protected static ?bool $__EntityMembers = false;

    protected static function _EntityMembers(array &$report, string $SQL_LMRId, array $params)
    {
        if (self::$__EntityMembers) {
            return;
        }
        if (static::$debug) {
            Log::Insert('_EntityMembers');
        }
        $data = _EntityMembers::getReport($SQL_LMRId, $params);
        $counts = [];
        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            if (in_array('Entity Member Type', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberType[$counts[$LMRId]] = $row->memberType;
            }
            if (in_array('Member Category', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberCategory[$counts[$LMRId]] = $row->memberCategory;
            }
            if (in_array('Member Name', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberName[$counts[$LMRId]] = $row->memberName;
            }
            if (in_array('Member Title', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberTitle[$counts[$LMRId]] = $row->memberTitle;
            }
            if (in_array('Member Ownership', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberOwnership[$counts[$LMRId]] = $row->memberOwnership;
            }
            if (in_array('Member Annual Salary', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberAnnualSalary[$counts[$LMRId]] = $row->memberAnnualSalary;
            }
            if (in_array('Member Address', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberAddress[$counts[$LMRId]] = $row->memberAddress;
            }
            if (in_array('Member Phone', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberPhone[$counts[$LMRId]] = $row->memberPhone;
            }
            if (in_array('Member Cell', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberCell[$counts[$LMRId]] = $row->memberCell;
            }
            if (in_array('Member SSN', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberSSN[$counts[$LMRId]] = $row->memberSSN;
            }
            if (in_array('Member Date Of Birth', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberDOB[$counts[$LMRId]] = $row->memberDOB;
            }
            if (in_array('Member Credit Score', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberCreditScore[$counts[$LMRId]] = $row->memberCreditScore;
            }
            if (in_array('Member Email', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberEmail[$counts[$LMRId]] = $row->memberEmail;
            }
            if (in_array('Member Drivers License', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberDriversLicense[$counts[$LMRId]] = $row->memberDriversLicense;
            }
            if (in_array('Member Drivers License State', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberDriversLicenseState[$counts[$LMRId]] = $row->memberDriversLicenseState;
            }
            if (in_array('Member TIN', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberTin[$counts[$LMRId]] = $row->memberTin;
            }
            if (in_array('Member Personal Guarantee', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberPersonalGuarantee[$counts[$LMRId]] = $row->memberPersonalGuarantee;
            }
            if (in_array('Member Authorized Signer', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberAuthorizedSigner[$counts[$LMRId]] = $row->memberAuthorizedSigner;
            }
            if (in_array('Member Citizenship', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberCitizenship[$counts[$LMRId]] = $row->memberCitizenship;
            }
            if (in_array('Member Marital Status', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberMaritalStatus[$counts[$LMRId]] = $row->memberMaritalStatus;
            }
            if (in_array('Member Marriage Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberMarriageDate[$counts[$LMRId]] = $row->memberMarriageDate;
            }
            if (in_array('Member Divorce Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberDivorceDate[$counts[$LMRId]] = $row->memberDivorceDate;
            }
            if (in_array('Member Maiden Name', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberMaidenName[$counts[$LMRId]] = $row->memberMaidenName;
            }
            if (in_array('Member Spouse Name', self::$selectedHeaders)) {
                $report[$row->LMRId]->_memberSpouseName[$counts[$LMRId]] = $row->memberSpouseName;
            }
        }

        $counts = [];
        foreach ($data as $row) {
            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            if (in_array('Entity Member Type', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Entity Member Type', $counts[$LMRId]),
                    'Entity Member Type ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Category', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Category', $counts[$LMRId]),
                    'Member Category ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Name', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Name', $counts[$LMRId]),
                    'Member Name ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Title', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Title', $counts[$LMRId]),
                    'Member Title ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Ownership', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Ownership', $counts[$LMRId]),
                    'Member Ownership ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Annual Salary', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Annual Salary', $counts[$LMRId]),
                    'Member Annual Salary ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Address', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Address', $counts[$LMRId]),
                    'Member Address ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Phone', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Phone', $counts[$LMRId]),
                    'Member Phone ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Cell', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Cell', $counts[$LMRId]),
                    'Member Cell ' . $counts[$LMRId]
                );
            }
            if (in_array('Member SSN', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member SSN', $counts[$LMRId]),
                    'Member SSN ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Date Of Birth', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Date Of Birth', $counts[$LMRId]),
                    'Member Date Of Birth ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Credit Score', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Credit Score', $counts[$LMRId]),
                    'Member Credit Score ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Email', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Email', $counts[$LMRId]),
                    'Member Email ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Drivers License', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Drivers License', $counts[$LMRId]),
                    'Member Drivers License ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Drivers License State', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Drivers License State', $counts[$LMRId]),
                    'Member Drivers License State ' . $counts[$LMRId]
                );
            }
            if (in_array('Member TIN', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member TIN', $counts[$LMRId]),
                    'Member TIN ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Personal Guarantee', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Personal Guarantee', $counts[$LMRId]),
                    'Member Personal Guarantee ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Authorized Signer', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Authorized Signer', $counts[$LMRId]),
                    'Member Authorized Signer ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Citizenship', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Citizenship', $counts[$LMRId]),
                    'Member Citizenship ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Marital Status', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Marital Status', $counts[$LMRId]),
                    'Member Marital Status ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Marriage Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Marriage Date', $counts[$LMRId]),
                    'Member Marriage Date ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Divorce Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Divorce Date', $counts[$LMRId]),
                    'Member Divorce Date ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Maiden Name', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Maiden Name', $counts[$LMRId]),
                    'Member Maiden Name ' . $counts[$LMRId]
                );
            }
            if (in_array('Member Spouse Name', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Member Spouse Name', $counts[$LMRId]),
                    'Member Spouse Name ' . $counts[$LMRId]
                );
            }
        }

        self::unsetHeader('Member Type');
        self::unsetHeader('Member Category');
        self::unsetHeader('Member Name');
        self::unsetHeader('Member Title');
        self::unsetHeader('Member Ownership');
        self::unsetHeader('Member Annual Salary');
        self::unsetHeader('Member Address');
        self::unsetHeader('Member Phone');
        self::unsetHeader('Member Cell');
        self::unsetHeader('Member SSN');
        self::unsetHeader('Member Date Of Birth');
        self::unsetHeader('Member Credit Score');
        self::unsetHeader('Member Email');
        self::unsetHeader('Member Drivers License');
        self::unsetHeader('Member Drivers License State');
        self::unsetHeader('Member TIN');
        self::unsetHeader('Member Personal Guarantee');
        self::unsetHeader('Member Authorized Signer');
        self::unsetHeader('Member Citizenship');
        self::unsetHeader('Member Marital Status');
        self::unsetHeader('Member Marriage Date');
        self::unsetHeader('Member Divorce Date');
        self::unsetHeader('Member Maiden Name');
        self::unsetHeader('Member Spouse Name');

        self::$__EntityMembers = true;
    }

    protected static bool $_PropertyAppraisal = false;

    protected static function _PropertyAppraisal(array &$report, string $SQL_LMRId, array $params)
    {
        if (self::$_PropertyAppraisal) {
            return;
        }
        if (static::$debug) {
            Log::Insert('_PropertyAppraisal');
        }
        $data = _PropertyAppraisal::getReport($SQL_LMRId, $params);
        $counts = [];
        foreach ($data as $row) {
            if (!isset($report[$row->LMRId])) {
                $report[$row->LMRId] = new static();
            }
            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;
            if (in_array('Property Appraisal As Is Value', self::$selectedHeaders)) {
                $report[$row->LMRId]->_propertyAppraisalAsIsValue[$counts[$LMRId]] = $row->propertyAppraisalAsIsValue;
            }
            if (in_array('Property Appraisal Rehabbed Value', self::$selectedHeaders)) {
                $report[$row->LMRId]->_propertyAppraisalRehabbedValue[$counts[$LMRId]] = $row->propertyAppraisalRehabbedValue;
            }
            if (in_array('Property Appraisal Monthly Rent', self::$selectedHeaders)) {
                $report[$row->LMRId]->_propertyAppraisalMonthlyRent[$counts[$LMRId]] = $row->propertyAppraisalMonthlyRent;
            }
            if (in_array('Property Appraisal Job Types', self::$selectedHeaders)) {
                $report[$row->LMRId]->_propertyAppraisalJobTypes[$counts[$LMRId]] = $row->propertyAppraisalJobTypes;
            }
            if (in_array('Property Appraisal Date Obtained', self::$selectedHeaders)) {
                $report[$row->LMRId]->_propertyAppraisalDateObtained[$counts[$LMRId]] = $row->propertyAppraisalDateObtained;
            }
            if (in_array('Property Appraisal Order Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_propertyAppraisalOrderDate[$counts[$LMRId]] = $row->propertyAppraisalOrderDate;
            }
            if (in_array('Property Appraisal Comments', self::$selectedHeaders)) {
                $report[$row->LMRId]->_propertyAppraisalComments[$counts[$LMRId]] = $row->propertyAppraisalComments;
            }
            if (in_array('Property Appraisal Effective Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_propertyAppraisalEffectiveDate[$counts[$LMRId]] = $row->propertyAppraisalEffectiveDate;
            }
            if (in_array('Primary Appraisal Ecoa Delivery Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_primaryAppraisalEcoaDeliveryDate[$counts[$LMRId]] = $row->primaryAppraisalEcoaDeliveryDate;
            }
            if (in_array('Property Appraisal Inspection Date', self::$selectedHeaders)) {
                $report[$row->LMRId]->_propertyAppraisalInspectionDate[$counts[$LMRId]] = $row->propertyAppraisalInspectionDate;
            }
        }

        $counts = [];
        foreach ($data as $row) {
            $LMRId = $row->LMRId;
            if (!isset($counts[$LMRId])) {
                $counts[$LMRId] = 0;
            }
            $counts[$LMRId]++;

            if (in_array('Property Appraisal As Is Value', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Property Appraisal As Is Value', $counts[$LMRId]),
                    'Property Appraisal As Is Value ' . $counts[$LMRId]
                );
            }
            if (in_array('Property Appraisal Rehabbed Value', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Property Appraisal Rehabbed Value', $counts[$LMRId]),
                    'Property Appraisal Rehabbed Value ' . $counts[$LMRId]
                );
            }
            if (in_array('Property Appraisal Monthly Rent', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Property Appraisal Monthly Rent', $counts[$LMRId]),
                    'Property Appraisal Monthly Rent ' . $counts[$LMRId]
                );
            }
            if (in_array('Property Appraisal Job Types', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Property Appraisal Job Types', $counts[$LMRId]),
                    'Property Appraisal Job Types ' . $counts[$LMRId]
                );
            }
            if (in_array('Property Appraisal Date Obtained', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Property Appraisal Date Obtained', $counts[$LMRId]),
                    'Property Appraisal Date Obtained ' . $counts[$LMRId]
                );
            }
            if (in_array('Property Appraisal Order Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Property Appraisal Order Date', $counts[$LMRId]),
                    'Property Appraisal Order Date ' . $counts[$LMRId]
                );
            }
            if (in_array('Property Appraisal Comments', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Property Appraisal Comments', $counts[$LMRId]),
                    'Property Appraisal Comments ' . $counts[$LMRId]
                );
            }
            if (in_array('Property Appraisal Effective Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Property Appraisal Effective Date', $counts[$LMRId]),
                    'Property Appraisal Effective Date ' . $counts[$LMRId]
                );
            }
            if (in_array('Primary Appraisal Ecoa Delivery Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Primary Appraisal Ecoa Delivery Date', $counts[$LMRId]),
                    'Primary Appraisal Ecoa Delivery Date ' . $counts[$LMRId]
                );
            }
            if (in_array('Property Appraisal Inspection Date', self::$selectedHeaders)) {
                self::addHeader(
                    self::getColumnOrder('Property Appraisal Inspection Date', $counts[$LMRId]),
                    'Property Appraisal Inspection Date ' . $counts[$LMRId]
                );
            }
        }

        self::unsetHeader('Property Appraisal As Is Value');
        self::unsetHeader('Property Appraisal Rehabbed Value');
        self::unsetHeader('Property Appraisal Monthly Rent');
        self::unsetHeader('Property Appraisal Job Types');
        self::unsetHeader('Property Appraisal Date Obtained');
        self::unsetHeader('Property Appraisal Order Date');
        self::unsetHeader('Property Appraisal Comments');
        self::unsetHeader('Property Appraisal Effective Date');
        self::unsetHeader('Primary Appraisal Ecoa Delivery Date');
        self::unsetHeader('Property Appraisal Inspection Date');

        self::$_PropertyAppraisal = true;
    }


}
