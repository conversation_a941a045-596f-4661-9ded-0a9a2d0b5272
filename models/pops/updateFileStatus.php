<?php

namespace models\pops;


use models\composite\oBroker\getAgentsInfo;
use models\composite\oBroker\listAllAgents;
use models\composite\oFile\getFileInfo;
use models\composite\oUserAccess\getAutomationControlAccess;
use models\constants\agentUpdateStatusException;
use models\constants\gl\glAllowedAgentToUpdateStatus;
use models\cypher;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;


/**
 *
 */
class updateFileStatus
{

    public $thankYouMessage = '';
    public $error = '';

    /**
     * @param int $ajax
     * @param array $inArray
     * @return string
     */
    public function getFormHtml1(int $ajax = 0, array $inArray = []): string
    {
        global $filePrimaryStatus;

        $agentUpdateStatusException = agentUpdateStatusException::$agentUpdateStatusException;
        $glAllowedAgentToUpdateStatus = glAllowedAgentToUpdateStatus::$glAllowedAgentToUpdateStatus;

        $formHtml1 = '';
        if (isset($_POST['httpReferer']) && !$this->error) {
            $out = '<p id="contact-pop-error" class="formItem">' . $this->thankYouMessage . '</p>';
            if ($ajax) $out .= '<a href="#" class="close-overlay">Close</a>';

            return $out;
        }
        if ($this->error) $formHtml1 .= '<p id="contact-pop-error" class="formItem">' . $this->error . '</p>';

        $LMRClientTypes = null;
        $resultArray = [];
        $PCPLMSArray = [];
        $tempResultArray = [];
        $PCSubstatusArray = [];
        $selectedPrimeStatusId = 0;
        $selectedSubstatusArray = [];
        $ResponseInfoArray = [];
        $previousFileSubStatus = '';
        $BranchInfo = [];
        $LMRClientTypeInfo = $tempBrokerListArray = $branchListArray = $LMRInfoArray = [];

        $LMRId = $inArray['LMRId'];
        $LMRResponseId = $inArray['LMRResponseId'];
        $branchId = $inArray['branchId'];
        $PCID = $inArray['PCID'];
        $userRole = $inArray['userRole'];
        $userName = $inArray['userName'];
        $userNumber = $inArray['userNumber'];
        $allowAutomation = $inArray['allowAutomation'];
        $fileTypesTxt = $inArray['fileTypesTxt'];
        $encryptedPCID = cypher::myEncryption($PCID);
        $encryptedLMRId = cypher::myEncryption($LMRId);
        $userGroup = $inArray['userGroup'];
        $dataLoc = $inArray['dataLoc'] ?? 'myPipeline';

        $userAutomationControlAccess = getAutomationControlAccess::getReport($userNumber);
        if ($userGroup != 'Employee') {
            $userAutomationControlAccess = 0;
        }


        if ($LMRId > 0) $resultArray = getFileInfo::getReport($inArray);

        if (array_key_exists($LMRId, $resultArray)) $tempResultArray = $resultArray[$LMRId];
        if (array_key_exists('LMRInfo', $tempResultArray)) $LMRInfoArray = $tempResultArray['LMRInfo'];
        if (array_key_exists('ResponseInfo', $tempResultArray)) $ResponseInfoArray = $tempResultArray['ResponseInfo'];
        if (array_key_exists('fileSubstatusInfo', $tempResultArray)) $selectedSubstatusArray = $tempResultArray['fileSubstatusInfo'];

        if (count($ResponseInfoArray) > 0) $selectedPrimeStatusId = $ResponseInfoArray['primeStatusId'];
        /*** PC File Status ***/
        if (array_key_exists('PCStatusInfo', $tempResultArray)) $PCPLMSArray = $tempResultArray['PCStatusInfo'];

        /*** PC File Status ***/

        /*** PC File Sub-Status ***/

        if (array_key_exists('PCSubStatusInfo', $tempResultArray)) $PCSubstatusArray = $tempResultArray['PCSubStatusInfo'];
        /*** PC File Sub-Status ***/

        if (array_key_exists('BranchInfo', $tempResultArray)) $BranchInfo = $tempResultArray['BranchInfo'];
        if (array_key_exists('LMRClientTypeInfo', $tempResultArray)) $LMRClientTypeInfo = $tempResultArray['LMRClientTypeInfo'];
        /** Fetch file services **/

        /*** PC File Sub-Status ***/
        $saveUrl = CONST_URL_POPS . 'fileStatusSave.php';
        $brokerNumber = null;

        if (count($LMRInfoArray) > 0) {
            $brokerNumber = $LMRInfoArray['brokerNumber'];
        }

        $stCnt = count($selectedSubstatusArray);
        for ($st = 0; $st < $stCnt; $st++) {
            $subStatus = $selectedSubstatusArray[$st]['substatusId'];
            if ($subStatus != '') {
                if ($st > 0) $previousFileSubStatus .= ',';
                $previousFileSubStatus .= $subStatus;
            }
        }
        $previousModStatus = '';
        for ($j = 0; $j < count($PCPLMSArray); $j++) {
            if (trim($PCPLMSArray[$j]['PSID']) == $selectedPrimeStatusId) {
                $previousModStatus = trim($PCPLMSArray[$j]['primaryStatus']);
                break;
            }
        }

        if ($userRole == 'Agent' && array_key_exists($PCID, $agentUpdateStatusException)) {
            if (trim($selectedPrimeStatusId) == trim($agentUpdateStatusException[$PCID])) {
                $tempPCStatusArray = $PCPLMSArray;
                $PCPLMSArray = [];
                for ($ps = 0; $ps < count($tempPCStatusArray); $ps++) {
                    if (in_array(trim($tempPCStatusArray[$ps]['PSID']), $glAllowedAgentToUpdateStatus)) {
                        $PCPLMSArray[] = $tempPCStatusArray[$ps];
                    }
                }
            }
        }
        $allowAgentToSeeFile = 0;
        if (count($BranchInfo) > 0) {
            $allowAgentToSeeFile = $BranchInfo['agentFileAccess'];
        }
        /* Allow Agent's to update Admin section under Lead Status / their assigned files as per the branch settings logic (Lead Pool mode) - Apr 07, 2017  */
        if ($userRole == 'Agent' &&
            ($allowAgentToSeeFile == 2 ||
                ($allowAgentToSeeFile == 3 && strtolower($filePrimaryStatus) == 'lead')
            ) &&
            ($userNumber != $brokerNumber)
        ) {
            $allowToUpdateFileAdminSection = 1;
        } else {
            $allowToUpdateFileAdminSection = 0;
        }

        if ($allowToUpdateFileAdminSection == 1 && $userRole == 'Agent') {

            $agentResultArray = null;

            if ($LMRId > 0) {
                $ip = ['agentID' => $userNumber, 'PCID' => $PCID];
                $agentResultArray = getAgentsInfo::getReport($ip);
            }
            $LMRClientTypes = Arrays::implode2dArray(',', $LMRClientTypeInfo, 'ClientType');
            $ip['userRole'] = $userRole;
            $ip['PCID'] = $PCID;
            $ip['stats'] = 1;

            $ip['execID'] = $branchId;

            if ($PCID > 0) $ip['PCID'] = $PCID;
            $ip['opt'] = 'list';
            $tempBrokerListArray = listAllAgents::getReport($ip);
            if (count($agentResultArray) > 0)
                if (array_key_exists('branchInfo', $agentResultArray))
                    $branchListArray = $agentResultArray['branchInfo'];
        }

        $formHtml1 .= <<<EOT
<script type="text/javascript" src="/assets/js/popup.js"></script>
	 <div class="card-body p-0">
 <form name="updateFileStatusForm" id="updateFileStatusForm" method="POST"  action="$saveUrl" >
   <input type="hidden" name="executiveId" id="executiveId" value="$branchId">
   <input type="hidden" name="LMRId" id="LMRId" value="$LMRId">
   <input type="hidden" id="encryptedLId" value="$encryptedLMRId">
   <input type="hidden" name="rId" id="rId" value="$LMRResponseId">
   <input type="hidden" name="previousModStatusId" id="previousModStatusId" value="$selectedPrimeStatusId">
   <input type="hidden" name="previousPSName" id="previousPSName" value="$previousModStatus">
   <input type="hidden" name="previousFileSubStatus" id="previousFileSubStatus" value="$previousFileSubStatus">
   <input type="hidden" name="primaryStatusName" id="primaryStatusName" value="">
   <input type="hidden" name="allowToUpdateFileAdminSection" id="allowToUpdateFileAdminSection" value="$allowToUpdateFileAdminSection">
   <input type="hidden" name="PCID" id="PCID" value="$PCID">
   
   
   <input type="hidden" id="allowAutomation" value="$allowAutomation">   
   <input type="hidden" id="encryptedPCID" value="$encryptedPCID">      
   <input type="hidden" name="isPfsFssUpdated" id="isPfsFssUpdated" value="No">   
   <input type="hidden" name="fileTypesTxt" id="fileTypesTxt" value="$fileTypesTxt"> 
   <input type="hidden" name="lastUpdatedParam" id="lastUpdatedParam" value="">
   <input type="hidden" name="lastUpdatedFss" id="lastUpdatedFss" value="">  
   <input type="hidden" name="triggerRule" id="triggerRule" value="No">
   <input type="hidden" name="userAutomationControlAccess" id="userAutomationControlAccess" value="$userAutomationControlAccess">
   <input type="hidden" name="autoTaskIds" id="autoTaskIds" value="">
   <input type="hidden" name="autoEmailIds" id="autoEmailIds" value="">
   <input type="hidden" name="autoWebhookIds" id="autoWebhookIds" value="">
   <input type="hidden" name="manual" id="manual" value="">
   <input type="hidden" name="dataLoc" id="dataLoc" value="$dataLoc">
EOT;
        if ($allowToUpdateFileAdminSection == 1 && $userRole == 'Agent') {
            $formHtml1 .= <<<EOT
      <input type="hidden" name="LMRResponseId" id="LMRResponseId" value="$LMRResponseId">
      <input type="hidden" name="LMRExecutiveId" id="LMRExecutiveId" value="$branchId">
      <input type="hidden" name="userRole" id="userRole" value="$userRole">
      <input type="hidden" name="excBranchId" id="excBranchId" value="$branchId">
      <input type="hidden" name="excAgentId" id="excAgentId" value="$brokerNumber">
      <input type="hidden" name="assignedAgentId" id="assignedAgentId" value="$userNumber">
      <input type="hidden" name="LMRClientTypes" id="LMRClientTypes" value="$LMRClientTypes">
EOT;
        }
        $formHtml1 .= <<<EOT
		  <div class="form-group">
            <label class="font-weight-bold">Primary Client File Status</label>
EOT;
        $statuaCnt = count($PCPLMSArray);
        $tempModulesCodeArr = [];
        for ($j = 0; $j < $statuaCnt; $j++) {
            $moduleCode = $PCPLMSArray[$j]['moduleName'];
            $tempModulesCodeArr[$moduleCode][] = $PCPLMSArray[$j];
        }
        $moduleKeys = array_keys($tempModulesCodeArr);
        $formHtml1 .= <<<EOT
	    <select name="primaryStatus"  id="primaryStatus" class="mandatory primaryStatus form-control form-controller-solid chzn-select onChangePrimaryStatus" data-placeholder="Select Primary Status">
EOT;
        for ($k = 0; $k < count($moduleKeys); $k++) {
            $formHtml1 .= <<<EOT
        <optgroup label="$moduleKeys[$k]">
EOT;
            $tempKeys = $tempModulesCodeArr[$moduleKeys[$k]];
            for ($plm = 0; $plm < count($tempKeys); $plm++) {
                $PSID = trim($tempKeys[$plm]['PSID']);
                $statusValue = trim($tempKeys[$plm]['primaryStatus']);
                $sel = Arrays::isSelected($selectedPrimeStatusId, $PSID);
                $showHidePrimaryStatusGroup = '';
                if (PageVariables::$userGroup == 'Employee') {
                    if ((trim($tempKeys[$plm]['allowBOToEditFile']) == 0)
                        && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                    }
                } elseif (PageVariables::$userGroup == 'Branch') {
                    if ((trim($tempKeys[$plm]['allowOthersToUpdate']) == 0)
                        && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                    }
                } elseif (PageVariables::$userGroup == 'Agent' && PageVariables::$externalBroker == 0) {
                    if ((trim($tempKeys[$plm]['allowAgentToEditFile']) == 0)
                        && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                    }
                } elseif (PageVariables::$userGroup == 'Agent' && PageVariables::$externalBroker == 1) {
                    if ((trim($tempKeys[$plm]['allowLoanOfficerToEditFile']) == 0)
                        && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                    }
                } elseif (PageVariables::$userGroup == 'Client') {
                    if ((trim($tempKeys[$plm]['allowClientToEditFile']) == 0)
                        && (PageVariables::$allowToEditMyFile == 2 || PageVariables::$allowToEditMyFile == 0)) {
                        $showHidePrimaryStatusGroup = 'disabled title="Status Disabled"';
                    }
                }

                $formHtml1 .= <<<EOT
          <option $showHidePrimaryStatusGroup  value="$PSID" $sel>$statusValue</option>
EOT;
            }
            $formHtml1 .= <<<EOT
      </optgroup>
EOT;
        }
        $formHtml1 .= <<<EOT
      </select>
EOT;
        $formHtml1 .= <<<EOT
</div>
  <div class="form-group">
    <label class="font-weight-bold">File Sub Status</label>
    <select data-placeholder="File Sub Status" name="subStatus" id="subStatus" class="chzn-select1 form-control form-controller-solid" multiple="" >
EOT;
        $tempModulesCodeArr = [];
        for ($ps = 0; $ps < count($PCSubstatusArray); $ps++) {
            $moduleCode = trim($PCSubstatusArray[$ps]['moduleName']);
            $category = trim($PCSubstatusArray[$ps]['category']);
            $tempModulesCodeArr[$moduleCode][$category][] = $PCSubstatusArray[$ps];
        }
        $moduleKeys = array_keys($tempModulesCodeArr);
        for ($j = 0; $j < count($moduleKeys); $j++) {
            $moduleCode = $moduleKeys[$j];
            $categoryArr = $tempModulesCodeArr[$moduleCode];
            $categoryKeys = array_keys($categoryArr);
            $formHtml1 .= <<<EOT
		<option value="" class="optnGrp" disabled>$moduleCode </option>
EOT;
            for ($k = 0; $k < count($categoryKeys); $k++) {
                $category = $categoryKeys[$k];
                $substatusArr = $tempModulesCodeArr[$moduleCode][$category];
                $formHtml1 .= <<<EOT
			<optgroup label="$category">
EOT;
                for ($l = 0; $l < count($substatusArr); $l++) {
                    $substatusId = trim($substatusArr[$l]['PFSID']);
                    $statusValue = trim($substatusArr[$l]['substatus']);

                    $substatusSel = Strings::isKeyChecked($selectedSubstatusArray, 'substatusId', $substatusId);
                    if (trim($substatusSel) == 'checked') $substatusSel = 'selected ';
                    $formHtml1 .= <<<EOT
				<option value="$substatusId" $substatusSel>$statusValue </option>
EOT;
                }
                $formHtml1 .= <<<EOT
			</optgroup>
EOT;
            }
            $formHtml1 .= <<<EOT
EOT;
        }
        $formHtml1 .= <<<EOT
		</select>
     </div>
EOT;

        if ($allowToUpdateFileAdminSection == 1 && $userRole == 'Agent') {
            $formHtml1 .= <<<EOT
  <div class="form-group">
    <label class="font-weight-bold">Select Branch</label>
            <select name="branchId" id="branchId" class=" chzn-select form-control form-controller-solid" data-placeholder="Select Branch">
EOT;

            for ($i1 = 0; $i1 < count($branchListArray); $i1++) {
                $branchNo = trim($branchListArray[$i1]['executiveId']);
                $branchName = trim($branchListArray[$i1]['LMRExecutive']);
                if ($branchNo == $branchId) {
                    $selOpt = ' selected ';
                } else {
                    $selOpt = '';
                }
                $formHtml1 .= <<<EOT
        <option value="$branchNo" $selOpt>$branchName</option>
EOT;
            }
            $formHtml1 .= <<<EOT
           </select>
      </div> 
      <div class="form-group">
EOT;
            $formHtml1 .= <<<EOT
    <label class="font-weight-bold">Existing Loan Officer/Broker</label>
EOT;
            $formHtml1 .= <<<EOT

        
EOT;
            $formHtml1 .= <<<EOT
              <select name="agentId" id="agentId" disabled class=" chzn-select form-control form-controller-solid" 
>
EOT;
            $formHtml1 .= <<<EOT
           <option value=""> - Select Loan Officer/Broker - </option>
EOT;

            for ($i1 = 0; $i1 < count($tempBrokerListArray); $i1++) {
                $brokerNo = trim($tempBrokerListArray[$i1]['brokerNumber']);
                $brokerName = trim($tempBrokerListArray[$i1]['bName']) . ' ' . trim($tempBrokerListArray[$i1]['bLName']);
                if ($brokerNo == $brokerNumber) {
                    $selOpt = ' selected ';
                } else {
                    $selOpt = '';
                }
                $formHtml1 .= <<<EOT
        <option value="$brokerNo" $selOpt>$brokerName</option>
EOT;
            }
            $formHtml1 .= <<<EOT
           </select>
     </div>
EOT;
            $formHtml1 .= <<<EOT
  <div class="form-group">
    <label class="font-weight-bold">Assign to me</label>
     $userName
    </div>
EOT;
        }

        $formHtml1 .= <<<EOT


   <div class="row d-none">
        <div class="col-xl-5 col-md-4"></div> 
        <div class="col-xl-6 col-md-6">
         <input type="submit" name="but_save" value="Save"  class="btn btn-primary mr-2">
            <button type="reset" class="btn btn-secondary cancelButton">Reset</button>
        </div>
    </div>

</form>
		  </div>
EOT;
        return $formHtml1;
    }
}
