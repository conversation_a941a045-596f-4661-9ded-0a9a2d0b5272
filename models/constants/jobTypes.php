<?php

namespace models\constants;

use models\types\strongType;

class jobTypes extends strongType
{
    public const JOB_TYPE_AS_IS = 'As Is';
    public const JOB_TYPE_AS_COMPLETE = 'As Complete';
    public const JOB_TYPE_AS_STABILIZED = 'As Complete / Stabilized';
    public const JOB_TYPE_INSURABLE_VALUE = 'Insurable Value';
    public const JOB_TYPE_OTHER = 'Other';
    public const SUBJECT_TO = 'Subject To';

    const jobTypes = [
        self::JOB_TYPE_AS_IS =>  self::JOB_TYPE_AS_IS,
        self::JOB_TYPE_AS_COMPLETE =>  self::JOB_TYPE_AS_COMPLETE,
        self::JOB_TYPE_AS_STABILIZED =>  self::JOB_TYPE_AS_STABILIZED,
        self::JOB_TYPE_INSURABLE_VALUE =>  self::JOB_TYPE_INSURABLE_VALUE,
        self::JOB_TYPE_OTHER =>  self::JOB_TYPE_OTHER,
        self::SUBJECT_TO =>  self::SUBJECT_TO,
    ];

    public static array $jobTypes = self::jobTypes;
}
