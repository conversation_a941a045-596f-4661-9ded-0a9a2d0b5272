<?php

namespace models\inArrayData;

use models\lendingwise\tblProcessingCompany;

class PCInfoData extends tblProcessingCompany
{
    public ?string $marketPlace = null;
    public ?string $eTransStatus = null;
    public ?string $sbaPartnerId = null;
    public ?string $sbaPartnerLocationId = null;
    public ?string $ListingPage = null;
    public ?string $IPAddress = null;
    public ?string $creditCardType = null;
    public ?string $cardHolderName = null;
    public ?string $creditCardNumber = null;
    public ?string $expirationMonth = null;
    public ?string $expirationYear = null;
    public ?string $cardNumberOnBack = null;
    public ?string $billingAddress1 = null;
    public ?string $billingAddress2 = null;
    public ?string $billingCity = null;
    public ?string $billingState = null;
    public ?string $billingZip = null;
}
