<?php

namespace models\packages;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFirstRehabLending;
use models\constants\gl\glHMLOExtensionOption;
use models\constants\gl\glPCID;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\feesAndCost;
use models\Controllers\LMRequest\Property;
use models\pdf\CustomTCPDF;
use models\pdf\loanTermSheet;
use models\servicing\LoanTerms;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

/**
 *
 */
class PackageLoanTermSheet
{
    /**
     * @param $inArray
     * @return loanTermSheet
     */
    public static function GeneratePDF($inArray, CustomTCPDF $pdf = null): CustomTCPDF
    {
        global $glPositionArray;
        global $processingCompanyArray, $txnID;
        global $imgWdth, $imgHt, $showHeader, $showFooter, $loanNumber,
               $initialLoanAmount, $totalMonthlyPayment, $prepaidInterestReserve, $totalCashOutAmt,
               $totalRequiredReserves, $paymentReservesAmt, $requiredConstructionAmt, $contingencyReserveAmt;
        global $myFileInfo, $insImpoundsMonth, $fileHMLONewLoanInfo, $taxImpoundsFee, $fileHMLOPropertyInfo,
               $isBorBorrowedDownPayment, $isTaxesInsEscrowed, $propertyNeedRehab, $haveBorSquareFootage,
               $additionalPropertyRestrictions, $exitStrategy, $acceptedPurchase, $haveCurrentLoanBal,
               $doYouHaveInvoiceToFactor, $haveInterestreserve, $maxAmtToPutDown, $closingCostFinanced,
               $payOffMortgage1, $payOffMortgage2, $payOffOtherOutstandingAmounts, $payOffOutstandingTaxes,
               $cashOutAmt, $filepaydownInfo, $lien1Terms, $taxes1, $isBlanketLoan, $interestChargedFromDate,
               $interestChargedEndDate, $originationPointsValue, $brokerPointsValue, $taxImpoundsMonth,
               $taxImpoundsMonthAmt, $insImpoundsFee, $paymentReserves, $fv, $insImpoundsMonthAmt,
               $requiredConstruction, $contingencyReserve, $guess;
        global $LMRId, $isCoBorrower;

        $glFirstRehabLending = glFirstRehabLending::$glFirstRehabLending;

        $selectedImg = CONST_ROOT_PATH . 'assets/images/icon_chk_1.jpg';

        $fileDetailsArray = [];
        $fileHMLOPropertyInfo = [];
        $HMLOPCBasicLoanInfo = [];
        $fileHMLONewLoanInfo = [];
        // $myFileInfo = [];
        $LMRInfoArray = [];
        $PCInfoArray = [];
        $LMRClientTypeInfoArray = [];
        $listingRealtorInfo = [];
        $PkgInfoArray = [];
        $fileHMLOInfo = [];
        $fileHMLOEntityInfo = [];
        $AssignedBOStaffInfo = [];
        $branchInfoArray = [];
        $fileHMLOExperienceInfo = [];

        $borrowerName = '';
        $borrowerFName = '';
        $borrowerLName = '';
        $coBorrowerFName = '';
        $coBorrowerDOB = '';
        $coBorrowerLName = '';
        $lenderName1 = '';
        $fileModuleInfoArray = [];
        $LMRClientType = '';
        $showHeader = 0;
        $showFooter = 0;
        $coBCellNumber = '';
        $borrowerDOB = '';
        $ssnNumber = '';
        $coBSsnNumber = '';
        $loanTerm = '';
        $lien1Terms = '';
        $assumability = 'NO';
        $lien1Rate = '';
        $originationPointsRate = '';
        $isTaxesInsEscrowed = '';
        $rehabCostFinanced = '';
        $originationPointsValue = '';
        $processingFee = '';
        $brokerPointsRate = '';
        $brokerPointsValue = '';
        $appraisalFee = '';
        $applicationFee = '';
        $drawsSetUpFee = '';
        $estdTitleClosingFee = '';
        $miscellaneousFee = '';
        $closingCostFinanced = '';
        $extensionOption = '';
        $loanTermExpireDate = '';
        $maxAmtToPutDown = '';
        $costBasis = '';
        $realtor = '';
        $closingDate1 = '';
        $sales1Phone = '';
        $agency = '';
        $offerPropertyAgencyName = '';
        $offerPropertyAgentName = '';
        $OPAPhone = '';
        $listingPrice = '';
        $assessedValue = '';
        $acquisitionLTV = '';
        $rehabCost = '';
        $acquisitionPriceFinanced = '';
        $totalLoanAmount = '';
        $acquisitionLTV = '';
        $perRehabCostFinanced = '';
        $totalProjectCost = '';
        $LTC = '';
        $entityName = '';
        $originationPointsValue = '';
        $brokerPointsValue = '';
        $totalFeesAndCost = '';
        $lenderFeesAppraisal = '';
        $estimatedOtherTitleClosingFees = '';
        $homeValue = '';
        $perClosingCostFinanced = '';
        $closingCostNotFinanced = '';
        $totalCashToClose = '';
        $ARV = '';
        $prePaymentPenalty = '';
        $HMLOLender = '';
        $propAddress = '';
        $drawsFee = 0;
        $interestReserves = '';
        $percentageOfBudget = '';
        $payOffMortgage1 = '';
        $payOffMortgage2 = '';
        $payOffOutstandingTaxes = '';
        $payOffOtherOutstandingAmounts = '';
        $cashOutAmt = '';
        $typeOfHMLOLoanRequesting = '';
        $prePaymentPenaltyPercentage = '';
        $prePaymentPenaltyPerAndValue = '';

        $valuationBPOFee = 0;
        $valuationCMAFee = 0;
        $valuationAVEFee = 0;
        $valuationAVMFee = 0;
        $creditReportFee = 0;
        $creditCheckFee = 0;
        $employmentVerificationFee = 0;
        $backgroundCheckFee = 0;
        $cityCountyTaxStamps = 0;
        $taxReturnOrderFee = 0;
        $floodCertificateFee = 0;
        $loanOriginationFee = 0;
        $documentPreparationFee = 0;
        $wireFee = 0;
        $servicingSetUpFee = 0;
        $taxServiceFee = 0;
        $floodServiceFee = 0;
        $constructionHoldbackFee = 0;
        $thirdPartyFees = 0;
        $otherFee = 0;
        $taxImpoundsMonth = 0;
        $taxImpoundsMonthAmt = 0;
        $taxImpoundsFee = 0;
        $insImpoundsMonth = 0;
        $insImpoundsMonthAmt = 0;
        $insImpoundsFee = 0;
        $interestChargedFromDate = '';
        $interestChargedEndDate = '';
        $netLenderFundsToBorrower = 0;
        $diemDays = '';
        $totalDailyInterestCharge = 0;
        $totalEstPerDiem = 0;
        $fileHMLOBackGroundInfo = [];
        $isBorPersonallyGuaranteeLoan = '';
        $HMLOEstateHeldIn = '';
        $isBorBorrowedDownPayment = '';
        $propertyNeedRehab = '';
        $additionalPropertyRestrictions = '';
        $mailingCity = '';
        $acceptedPurchase = '';
        $isBlanketLoan = '';
        $paymentReserves = '';
        $requiredConstruction = '';
        $contingencyReserve = '';
        $actualRentsInPlace = 0;
        $lessActualExpenses = 0;
        $rehabCostPercentageFinanced = 0;
        $downPaymentPercentage = 0;
        $pcName = '';
        $borPropAddress = '';
        $propertyCity = '';
        $propertyState = '';
        $propertyZip = '';
        $coBMailingAddress = '';
        $coBMailingCity = '';
        $coBMailingState = '';
        $coBMailingZip = '';
        $mailingState = '';
        $pcAddr = '';
        $pcCity = '';
        $pcState = '';
        $pcZip = '';
        $borPhone = '';
        $borCell = '';
        $coBPhoneNumber = '';
        $mailingAddress = '';
        $mailingZip = '';
        $branchLogo = '';

        $checkDisplayTermSheet = $lenderNotes = '';
        $isEF = 0;
        $exitStrategy = '';
        $escrowFees = 0;
        $recordingFee = 0;
        $wireTransferToTitle = 0;
        $wireTransferToEscrow = 0;
        $travellingNotaryFee = 0;
        $bufferAndMessengerFees = 0;
        $brokerComissionFee = 0;
        $wireTransferToOthers = 0;
        $travellingNotaryFee = 0;
        $archiveItToEscrow = 0;
        $bufferAndMessengerFees = 0;
        $inspectionFees = 0;
        $totalEstPerDiem = 0;
        $totalDailyInterestCharge = 0;
        $prePaidInterest = 0;
        $realEstateTaxes = 0;
        $insurancePremium = 0;
        $payOffLiensCreditors = 0;
        $pastDuePropertyTaxes = 0;
        $propertyTax = 0;
        $underwritingFees = 0;
        $checkDisplayTermSheet = $lenderNotes = '';
        $isEF = 0;
        $exitStrategy = '';
        $extensionOptionPercentage = '';
        $marketLTV = 0;
        $originalPurchasePrice = 0;
        $closingCostFinancingFee = 0;
        $attorneyFee = 0;
        $dueDiligence = 0;
        $UccLienSearch = 0;
        $projectFeasibility = 0;
        $isLoanPaymentAmt = '';
        $haveBorSquareFootage = $insurance1 = '';
        $nored = 0;
        $nocd = 0;
        $midFico = 0;
        $noOfProjectCurrently = 0;
        $pastDeals = 0;
        $haveInterestreserve = '';
        $expectForDueDiligence = $lienPosition = '';
        $survey = 0;
        $wholeSaleAdminFee = 0;
        $isLoanPaymentAmt = '';
        $spcf_hoafees = 0;
        $spcf_hoafeesMonthly = 0;
        $finalTotalCashToCloseAmtPkg = 0;

        $unSelectedImg = "<img src=\"" . CONST_ROOT_PATH . "assets/images/icon_chk_0.jpg\">";
        $selectedImg = "<img src=\"" . CONST_ROOT_PATH . "assets/images/icon_chk_1.jpg\">";

        /* new PDF */
        $pdf = $inArray['oPDF'] ?? $pdf;
        if (count($inArray) > 0) {
            $fileDetailsArray = $inArray['fileDetails'];
//            $pdf = $inArray['oPDF'];
            $packageId = $inArray['pkgID'];
            $LMRId = $inArray['LMRId'];
            if (array_key_exists('txnID', $inArray)) $txnID = trim($inArray['txnID']);
        }
        if (count($fileDetailsArray) > 0) {
            $PkgInfoArray = $myFileInfo = $fileDetailsArray[$LMRId];
        }

        $myFileInfo = $PkgInfoArray;
        // pr($myFileInfo);
        if (count($PkgInfoArray) > 0) {
            $LMRInfoArray = $PkgInfoArray['LMRInfo'];
            $branchInfoArray = $PkgInfoArray['BranchInfo'];
            $PCInfoArray = $PkgInfoArray['PCInfo'];
            $agentInfoArray = $PkgInfoArray['BrokerInfo'];
            $ACHInfoArray = $PkgInfoArray['ACHInfo'];
            $billingFeeInfoArray = $PkgInfoArray['BillingFeeInfo'];
            $billingPaymentInfoArray = $PkgInfoArray['BillingPaymentInfo'];
            $AssignedBOStaffInfo = $PkgInfoArray['AssignedBOStaffInfo'];
            $CourtInfo = $PkgInfoArray['CourtInfo'];
            $file2Info = $PkgInfoArray['file2Info'];
            $hardshipDescArray = $PkgInfoArray['hardshipDescInfo'];
            $hardshipInfoArray = $PkgInfoArray['hardshipInfo'];
            $QAInfoArray = $PkgInfoArray['QAInfo'];
            $listingRealtorInfo = $PkgInfoArray['listingRealtorInfo'];
            $incomeInfoArray = $PkgInfoArray['incomeInfo'];
            $assetInfoArray = $PkgInfoArray['AssetsInfo'];
            $listingInfoArray = $PkgInfoArray['listingInfo'];
            $LMRClientTypeInfoArray = $PkgInfoArray['LMRClientTypeInfo'];
            $fileModuleInfoArray = $PkgInfoArray['fileModuleInfo'];
            $fileHMLOPropertyInfo = $PkgInfoArray['fileHMLOPropertyInfo'];
            $HMLOPCBasicLoanInfo = $PkgInfoArray['HMLOPCBasicLoanInfo'];
            $fileHMLONewLoanInfo = $PkgInfoArray['fileHMLONewLoanInfo'];
            $fileHMLOInfo = $PkgInfoArray['fileHMLOInfo'];
            $fileHMLOEntityInfo = $PkgInfoArray['fileHMLOEntityInfo'];
            $fileHMLOBackGroundInfo = $PkgInfoArray['fileHMLOBackGroundInfo'];
            $fileHMLOExperienceInfo = $PkgInfoArray['fileHMLOExperienceInfo'];

        }
        if (array_key_exists($LMRId, $LMRClientTypeInfoArray)) $LMRClientTypeInfo = $LMRClientTypeInfoArray[$LMRId];
        if (array_key_exists($LMRId, $fileModuleInfoArray)) $fileModuleInfo = $fileModuleInfoArray[$LMRId];


        if (count($fileHMLOPropertyInfo) > 0) {
            $loanTerm = trim($fileHMLOPropertyInfo['loanTerm']);
            $maxAmtToPutDown = trim($fileHMLOPropertyInfo['maxAmtToPutDown']);
            $prePaymentPenalty = trim($fileHMLOPropertyInfo['prePaymentPenalty']);
            $typeOfHMLOLoanRequesting = trim($fileHMLOPropertyInfo['typeOfHMLOLoanRequesting']);
            $HMLOEstateHeldIn = trim($fileHMLOPropertyInfo['HMLOEstateHeldIn']);

            $propertyNeedRehab = trim($fileHMLOPropertyInfo['propertyNeedRehab']);
            $acceptedPurchase = trim($fileHMLOPropertyInfo['acceptedPurchase']);
            $isBlanketLoan = trim($fileHMLOPropertyInfo['isBlanketLoan']);
            $paymentReserves = trim($fileHMLOPropertyInfo['paymentReserves']);
            $requiredConstruction = trim($fileHMLOPropertyInfo['requiredConstruction']);
            $contingencyReserve = trim($fileHMLOPropertyInfo['contingencyReserve']);
            $lenderNotes = trim($fileHMLOPropertyInfo['lenderNotes']);
            $checkDisplayTermSheet = trim($fileHMLOPropertyInfo['checkDisplayTermSheet']);
            $expectForDueDiligence = trim($fileHMLOPropertyInfo['expectForDueDiligence']);
            $lienPosition = trim($fileHMLOPropertyInfo['lienPosition']);
            $annualPremium = trim($fileHMLOPropertyInfo['annualPremium']);

//		pr($annualPremium); die();
            $monthlyIns = ($annualPremium / 12);
        }

        if (count($incomeInfoArray) > 0) {
            $taxes = Strings::replaceCommaValues(trim($incomeInfoArray['taxes1']));
            $taxes = $taxes / 12;
        }
        if (count($HMLOPCBasicLoanInfo) > 0 && $expectForDueDiligence == '') {
            $expectForDueDiligence = urldecode($HMLOPCBasicLoanInfo[0]['reqForLoanProUnderwriting']);
        }

        if ($expectForDueDiligence == '') $expectForDueDiligence = preliminaryApproval('');

        $lienPositionVal = '';
        if ($lienPosition == 1) $lienPositionVal = $lienPosition . 'st';
        if ($lienPosition == 2) $lienPositionVal = $lienPosition . 'nd';
        if ($lienPosition == 3) $lienPositionVal = $lienPosition . 'rd';

        if (count($fileHMLOEntityInfo) > 0) {
            $entityName = $fileHMLOEntityInfo['entityName'];
        }

        if (count($fileHMLOBackGroundInfo) > 0) {
            $isBorPersonallyGuaranteeLoan = $fileHMLOBackGroundInfo['isBorPersonallyGuaranteeLoan'];
        }
        if (count($fileHMLOExperienceInfo) > 0) {
            $nored = $fileHMLOExperienceInfo['borNoOfREPropertiesCompleted'];
            $nocd = $fileHMLOExperienceInfo['borRehabPropCompleted'];
            $noOfProjectCurrently = $fileHMLOExperienceInfo['borNoOfProjectCurrently'];

        }
        if (count($fileHMLONewLoanInfo) > 0) {
            $assumability = trim($fileHMLONewLoanInfo['assumability']);
            $originationPointsRate = trim($fileHMLONewLoanInfo['originationPointsRate']);
            $isTaxesInsEscrowed = trim($fileHMLONewLoanInfo['isTaxesInsEscrowed']);
            $rehabCostFinanced = trim($fileHMLONewLoanInfo['rehabCostFinanced']);
            $originationPointsValue = trim($fileHMLONewLoanInfo['originationPointsValue']);
            $processingFee = trim($fileHMLONewLoanInfo['processingFee']);
            $brokerPointsRate = trim($fileHMLONewLoanInfo['brokerPointsRate']);
            $brokerPointsValue = trim($fileHMLONewLoanInfo['brokerPointsValue']);
            $appraisalFee = trim($fileHMLONewLoanInfo['appraisalFee']);
            $applicationFee = trim($fileHMLONewLoanInfo['applicationFee']);
            $drawsSetUpFee = trim($fileHMLONewLoanInfo['drawsSetUpFee']);
            $estdTitleClosingFee = trim($fileHMLONewLoanInfo['estdTitleClosingFee']);
            $miscellaneousFee = trim($fileHMLONewLoanInfo['miscellaneousFee']);
            $closingCostFinanced = trim($fileHMLONewLoanInfo['closingCostFinanced']);
            $extensionOption = trim($fileHMLONewLoanInfo['extensionOption']);
            $loanTermExpireDate = trim($fileHMLONewLoanInfo['loanTermExpireDate']);
            $HMLOLender = trim($fileHMLONewLoanInfo['HMLOLender']);
            $prePaymentPenaltyPercentage = trim($fileHMLONewLoanInfo['prePaymentPenaltyPercentage']);
            $drawsFee = trim($fileHMLONewLoanInfo['drawsFee']);
            $interestReserves = trim($fileHMLONewLoanInfo['interestReserves']);
            $percentageOfBudget = trim($fileHMLONewLoanInfo['percentageOfBudget']);
            $payOffMortgage1 = trim($fileHMLONewLoanInfo['payOffMortgage1']);
            $payOffMortgage2 = trim($fileHMLONewLoanInfo['payOffMortgage2']);
            $payOffOutstandingTaxes = trim($fileHMLONewLoanInfo['payOffOutstandingTaxes']);
            $payOffOtherOutstandingAmounts = trim($fileHMLONewLoanInfo['payOffOtherOutstandingAmounts']);
            $cashOutAmt = trim($fileHMLONewLoanInfo['cashOutAmt']);
            $originalPurchaseDate = trim($fileHMLONewLoanInfo['originalPurchaseDate']);
            $originalPurchasePrice = trim($fileHMLONewLoanInfo['originalPurchasePrice']);
            $datesigned = trim($fileHMLONewLoanInfo['datesigned']);
            $resaleClosingDate = trim($fileHMLONewLoanInfo['resaleClosingDate']);

            $valuationBPOFee = trim($fileHMLONewLoanInfo['valuationBPOFee']);
            $valuationCMAFee = trim($fileHMLONewLoanInfo['valuationCMAFee']);
            $valuationAVEFee = trim($fileHMLONewLoanInfo['valuationAVEFee']);
            $valuationAVMFee = trim($fileHMLONewLoanInfo['valuationAVMFee']);
            $creditReportFee = trim($fileHMLONewLoanInfo['creditReportFee']);
            $creditCheckFee = trim($fileHMLONewLoanInfo['creditCheckFee']);
            $employmentVerificationFee = trim($fileHMLONewLoanInfo['employmentVerificationFee']);
            $backgroundCheckFee = trim($fileHMLONewLoanInfo['backgroundCheckFee']);
            $cityCountyTaxStamps = trim($fileHMLONewLoanInfo['cityCountyTaxStamps']);
            $taxReturnOrderFee = trim($fileHMLONewLoanInfo['taxReturnOrderFee']);
            $floodCertificateFee = trim($fileHMLONewLoanInfo['floodCertificateFee']);
            $loanOriginationFee = trim($fileHMLONewLoanInfo['loanOriginationFee']);
            $documentPreparationFee = trim($fileHMLONewLoanInfo['documentPreparationFee']);
            $wireFee = trim($fileHMLONewLoanInfo['wireFee']);
            $servicingSetUpFee = trim($fileHMLONewLoanInfo['servicingSetUpFee']);
            $taxServiceFee = trim($fileHMLONewLoanInfo['taxServiceFee']);
            $floodServiceFee = trim($fileHMLONewLoanInfo['floodServiceFee']);
            $constructionHoldbackFee = trim($fileHMLONewLoanInfo['constructionHoldbackFee']);
            $thirdPartyFees = trim($fileHMLONewLoanInfo['thirdPartyFees']);
            $otherFee = trim($fileHMLONewLoanInfo['otherFee']);
            $taxImpoundsMonth = trim($fileHMLONewLoanInfo['taxImpoundsMonth']);
            $taxImpoundsMonthAmt = trim($fileHMLONewLoanInfo['taxImpoundsMonthAmt']);
            $taxImpoundsFee = trim($fileHMLONewLoanInfo['taxImpoundsFee']);
            $insImpoundsMonth = trim($fileHMLONewLoanInfo['insImpoundsMonth']);
            $insImpoundsMonthAmt = trim($fileHMLONewLoanInfo['insImpoundsMonthAmt']);
            $insImpoundsFee = trim($fileHMLONewLoanInfo['insImpoundsFee']);
            $interestChargedFromDate = trim($fileHMLONewLoanInfo['interestChargedFromDate']);
            $interestChargedEndDate = trim($fileHMLONewLoanInfo['interestChargedEndDate']);

            $actualRentsInPlace = trim($fileHMLONewLoanInfo['actualRentsInPlace']);
            $lessActualExpenses = trim($fileHMLONewLoanInfo['lessActualExpenses']);
            $rehabCostPercentageFinanced = trim($fileHMLONewLoanInfo['rehabCostPercentageFinanced']);
            $downPaymentPercentage = trim($fileHMLONewLoanInfo['downPaymentPercentage']);
            $escrowFees = trim($fileHMLONewLoanInfo['escrowFees']);
            $recordingFee = trim($fileHMLONewLoanInfo['recordingFee']);
            $underwritingFees = trim($fileHMLONewLoanInfo['underwritingFees']);
            $propertyTax = trim($fileHMLONewLoanInfo['propertyTax']);
            $isFeeUpdated = trim($fileHMLONewLoanInfo['isFeeUpdated']);
            $finalLoanAmt = trim($fileHMLONewLoanInfo['finalLoanAmt']);
            $inspectionFees = trim($fileHMLONewLoanInfo['inspectionFees']);
            $wireTransferToTitle = trim($fileHMLONewLoanInfo['wireTransferFeeToTitle']);
            $wireTransferToEscrow = trim($fileHMLONewLoanInfo['wireTransferFeeToEscrow']);
            $travellingNotaryFee = trim($fileHMLONewLoanInfo['travelNotaryFee']);
            $bufferAndMessengerFees = trim($fileHMLONewLoanInfo['bufferAndMessengerFee']);
            $prePaidInterest = trim($fileHMLONewLoanInfo['prePaidInterest']);
            $realEstateTaxes = trim($fileHMLONewLoanInfo['realEstateTaxes']);
            $insurancePremium = trim($fileHMLONewLoanInfo['insurancePremium']);
            $payOffLiensCreditors = trim($fileHMLONewLoanInfo['payOffLiensCreditors']);
            $pastDuePropertyTaxes = trim($fileHMLONewLoanInfo['pastDuePropertyTaxes']);
            $closingCostFinancingFee = trim($fileHMLONewLoanInfo['closingCostFinancingFee']);
            $attorneyFee = trim($fileHMLONewLoanInfo['attorneyFee']);
            $dueDiligence = trim($fileHMLONewLoanInfo['dueDiligence']);
            $UccLienSearch = trim($fileHMLONewLoanInfo['UccLienSearch']);
            $projectFeasibility = trim($fileHMLONewLoanInfo['projectFeasibility']);
            $earnestDeposit = trim($fileHMLONewLoanInfo['earnestDeposit']);
            $otherDownPayment = trim($fileHMLONewLoanInfo['otherDownPayment']);
            $survey = trim($fileHMLONewLoanInfo['survey']);
            $wholeSaleAdminFee = trim($fileHMLONewLoanInfo['wholeSaleAdminFee']);
            $isLoanPaymentAmt = trim($fileHMLONewLoanInfo['isLoanPaymentAmt']);
            $spcf_hoafees = trim($fileHMLONewLoanInfo['spcf_hoafees']);
            $finalTotalCashToCloseAmtPkg = $fileHMLONewLoanInfo['finalTotalCashToCloseAmtPkg'] ?: 0;
            $prePaymentSelectVal = feesAndCost::formatPrePaymentPenaltyOptions($fileHMLONewLoanInfo['prePaymentSelectVal']);

            if ($spcf_hoafees) {
                $spcf_hoafeesMonthly = $spcf_hoafees / 12;
            }


            if (Dates::IsEmpty($originalPurchaseDate)) {
                $originalPurchaseDate = '';
            } else {
                $originalPurchaseDate = Dates::formatDateWithRE($originalPurchaseDate, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($datesigned)) {
                $datesigned = '';
            } else {
                $datesigned = Dates::formatDateWithRE($datesigned, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($resaleClosingDate)) {
                $resaleClosingDate = '';
            } else {
                $resaleClosingDate = Dates::formatDateWithRE($resaleClosingDate, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($interestChargedFromDate)) {
                $interestChargedFromDate = '';
            } else {
                $interestChargedFromDate = Dates::formatDateWithRE($interestChargedFromDate, 'YMD', 'm/d/Y');
            }
            if (Dates::IsEmpty($interestChargedEndDate)) {
                $interestChargedEndDate = '';
            } else {
                $interestChargedEndDate = Dates::formatDateWithRE($interestChargedEndDate, 'YMD', 'm/d/Y');
            }

        }

        $prePaymentPenaltyPercentage = preg_replace('~\.0+$~', '', $prePaymentPenaltyPercentage);

        if ($prePaymentPenaltyPercentage != '') {
            $prePaymentPenaltyPerAndValue = $prePaymentPenaltyPercentage;
        }

        if ($prePaymentPenalty != 'None') {
            $prePaymentPenaltyPerAndValue .= ' % for ' . $prePaymentPenalty;
        } else {
            $prePaymentPenaltyPerAndValue = $prePaymentPenalty;
        }

        if ($extensionOption == 0) $extensionOption = '';

        $extensionOption = glHMLOExtensionOption::$glHMLOExtensionOption[$extensionOption] ?? null;

        if (count($fileHMLOInfo) > 0) {
            $rehabCost = $fileHMLOInfo['rehabCost'];
            $midFico = $fileHMLOInfo['midFicoScore'];
        }
        if (Dates::IsEmpty($loanTermExpireDate)) {
            $loanTermExpireDate = '';
        } else {
            $loanTermExpireDate = Dates::formatDateWithRE($loanTermExpireDate, 'YMD', 'm/d/Y');
        }

        if (count($listingRealtorInfo) > 0) {
            $realtor = $listingRealtorInfo['realtor'];
            $agency = $listingRealtorInfo['agency'];
            $listingDate1 = $listingRealtorInfo['listingDate'];
            $closingDate1 = $listingRealtorInfo['closingDate1'];
            $sales1Phone = $listingRealtorInfo['realtorPhoneNumber'];
            $propertyListedDate = $listingRealtorInfo['listingDate'];
            $offerPropertyAgencyName = $listingRealtorInfo['agency'];
            $offerPropertyAgentName = $listingRealtorInfo['realtor'];
            $OPAPhone = $listingRealtorInfo['realtorPhoneNumber'];
            $listingPrice = $listingRealtorInfo['listingPrice'];

            $costBasis = $listingRealtorInfo['costBasis'];
            $assessedValue = $listingRealtorInfo['assessedValue'];
            $OPAPhone = Strings::formatPhoneNumber($OPAPhone);
        }

        if (count($PCInfoArray) > 0) {
            $PCID = $PCInfoArray['PCID'];
        }
        for ($i = 0; $i < count($AssignedBOStaffInfo); $i++) {
            $role = '';
            $attorneyName = '';
            $role = trim($AssignedBOStaffInfo[$i]['role']);
            if ($role == 'Attorney') {
                $attorneyName = trim($AssignedBOStaffInfo[$i]['processorName']);

            }
        }

        if (count($PCInfoArray) > 0) {
            //pr($PCInfoArray);die();
            $pcAddr = trim($PCInfoArray['attorneyAddress']);
            $pcCity = trim($PCInfoArray['attorneyCity']);
            $pcState = trim($PCInfoArray['attorneyState']);
            $pcZip = trim($PCInfoArray['attorneyZipCode']);
            $pcName = trim($PCInfoArray['processingCompanyName']);

            if (array_key_exists('PCID', $PCInfoArray)) {
                $processingCompanyId = $PCInfoArray['PCID'];
            }
        }

        if ($HMLOLender == 'Array' || $HMLOLender == '') $HMLOLender = $pcName;

        if (count($LMRInfoArray) > 0) {
            // pr($LMRInfoArray);die();
            $borrowerFName = trim($LMRInfoArray['borrowerName']);
            $borrowerLName = trim($LMRInfoArray['borrowerLName']);
            $borrowerMName = trim($LMRInfoArray['borrowerMName']);
            $coBorrowerFName = trim($LMRInfoArray['coBorrowerFName']);
            $coBorrowerLName = trim($LMRInfoArray['coBorrowerLName']);
            $lenderName1 = trim($LMRInfoArray['servicer1']);
            $lenderName2 = trim($LMRInfoArray['servicer2']);
            $isCoBorrower = trim($LMRInfoArray['isCoBorrower']);
            $loanNumber = trim($LMRInfoArray['loanNumber']);
            $loanNumber2 = trim($LMRInfoArray['loanNumber2']);
            $borPropAddress = trim(Property::$primaryPropertyInfo->propertyAddress);
            $propertyCity = trim(Property::$primaryPropertyInfo->propertyCity);
            $propertyState = trim(Property::$primaryPropertyInfo->propertyState);
            $propertyZip = trim(Property::$primaryPropertyInfo->propertyZipCode);
            $mailingAddress = trim($LMRInfoArray['mailingAddress']);
            $mailingCity = trim($LMRInfoArray['mailingCity']);
            $mailingState = trim($LMRInfoArray['mailingState']);
            $mailingZip = trim($LMRInfoArray['mailingZip']);
            $phoneNumber = Strings::formatPhoneNumber(trim($LMRInfoArray['phoneNumber']));
            $borrowerEmail = trim($LMRInfoArray['borrowerEmail']);
            $coBorrowerEmail = trim($LMRInfoArray['coBorrowerEmail']);
            $borrowerDOB = trim($LMRInfoArray['borrowerDOB']);
            $ssnNumber = Strings::formatSSNNumber(trim($LMRInfoArray['ssnNumber']));
            $coBSsnNumber = trim(Strings::formatSSNNumber($LMRInfoArray['coBSsnNumber']));
            $saleDate = trim($LMRInfoArray['salesDate']);
            $coBMailingAddress = trim($LMRInfoArray['coBorrowerMailingAddress']);
            $coBMailingCity = trim($LMRInfoArray['coBorrowerMailingCity']);
            $coBMailingState = trim($LMRInfoArray['coBorrowerMailingState']);
            $coBMailingZip = trim($LMRInfoArray['coBorrowerMailingZip']);
            $occupancy = trim($LMRInfoArray['occupancy']);
            $coBorrowerDOB = trim($LMRInfoArray['coBorrowerDOB']);
            $borPhone = trim($LMRInfoArray['phoneNumber']);
            $coBPhoneNumber = trim($LMRInfoArray['coBPhoneNumber']);
            $borCell = $LMRInfoArray['cellNumber'];
            $coBCellNumber = trim($LMRInfoArray['coBCellNumber']);
            $lien1Payment = $LMRInfoArray['lien1Payment'];
            $lien2Payment = $LMRInfoArray['lien2Payment'];
            $lien2Rate = $LMRInfoArray['lien2Rate'];
            $lien2Amount = $LMRInfoArray['lien2Amount'];

            $lien1Terms = $LMRInfoArray['lien1Terms'];
            $lien1Rate = $LMRInfoArray['lien1Rate'];
            $lien1RateDirect = $LMRInfoArray['lien1Rate'];
            $homeValue = $LMRInfoArray['homeValue'];
            /*$nored					= $LMRInfoArray["nored"];
            $nocd					= $LMRInfoArray["nocd"];
            $midFico					= $LMRInfoArray["midFico"]; */

//            $borrowerName = ucwords($borrowerFName . ' ' . $borrowerMName . ' ' . $borrowerLName);
//            $coBorrowerName = ucwords($coBorrowerFName . ' ' . $coBorrowerLName);
        }


        require CONST_ROOT_PATH . 'backoffice/incExpCalculation.php';

        /** Income & expenses calculation for a file **/
        require CONST_ROOT_PATH . 'backoffice/proposalCalculation.php';
        $tempTotalLoanAmount = 0;
        if ($LMRId) {
            require CONST_BO_PATH . 'HMLOLoanTermsCalculation.php';
        }
        /** Loan Info Section Calculation. **/

        $percentageTotalLoan = $fileHMLOPropertyInfo['percentageTotalLoan'];
        $totalLoanAmount = $tempTotalLoanAmount;
        //dd($finalTotalCashToCloseAmtPkg);
        $totalCashToBorrower = Strings::replaceCommaValues($totalCashOutAmt) - $finalTotalCashToCloseAmtPkg;
        if ($totalCashToBorrower > 0) {
            $cashToBorLabel = 'Cash To Borrower';
        } else {
            $cashToBorLabel = 'Cash From Borrower';
            $totalCashToBorrower = -($totalCashToBorrower);
        }


        $propAddressInfo = '';
        $coMailingAddressInfo = '';
        $maillingAddressInfo = '';

        if ($borPropAddress != '') {
            $propAddressInfo = $borPropAddress;
        }
        if ($propertyCity != '') {
            $propAddressInfo .= ', ' . $propertyCity;
        }
        if ($propertyState != '') {
            $propAddressInfo .= ', ' . $propertyState;
        }
        if ($propertyZip != '') {
            $propAddressInfo .= ' ' . $propertyZip;
        }


        if ($coBMailingAddress != '') {
            $coMailingAddressInfo = $coBMailingAddress;
        }
        if ($coBMailingCity != '') {
            $coMailingAddressInfo .= ', ' . $coBMailingCity;
        }
        if ($coBMailingState != '') {
            $coMailingAddressInfo .= ', ' . $coBMailingState;
        }
        if ($coBMailingZip != '') {
            $coMailingAddressInfo .= ' ' . $coBMailingZip;
        }

        $branchLogo = '';
        if (count($branchInfoArray) > 0) {
            $branchCompany = $branchInfoArray['company'];
            $branchLogo = trim($branchInfoArray['logo']);
        }

        /*** Logo Start ***/
        if (trim($branchLogo) != '') {
            $logoSize1 = getimagesize($branchLogo);
            if ($logoSize1) {
                [$newWidth, $newHeight] = getimagesize($branchLogo);
            }
        }

        if ($pcName != '') {
            $pcAddressInfo = $pcName;
        }
        if ($pcAddr != '') {
            $pcAddressInfo .= ',' . $pcAddr;
        }
        if ($pcCity != '') {
            $pcAddressInfo .= ', ' . $pcCity;
        }

        if ($pcState != '') {
            $pcAddressInfo .= ', ' . $pcState;
        }
        if ($pcZip != '') {
            $pcAddressInfo .= ', ' . $pcZip . '.';
        }

        $borPhone = Strings::formatPhoneNumber($borPhone);
        $borCell = Strings::formatPhoneNumber($borCell);
        $coBPhoneNumber = Strings::formatPhoneNumber($coBPhoneNumber);
        $coBCellNumber = Strings::formatPhoneNumber($coBCellNumber);
        if (Dates::IsEmpty($borrowerDOB)) {
            $borrowerDOB = '';
        } else {
            $borrowerDOB = Dates::formatDateWithRE($borrowerDOB, 'YMD', 'm/d/Y');
        }
        if (Dates::IsEmpty($coBorrowerDOB)) {
            $coBorrowerDOB = '';
        } else {
            $coBorrowerDOB = Dates::formatDateWithRE($coBorrowerDOB, 'YMD', 'm/d/Y');
        }
        if (trim($borrowerDOB) == '') {
            $borrowerDOB = '';
        }
        if (trim($coBorrowerDOB) == '') {
            $coBorrowerDOB = '';
        }
        if (trim($borPhone) == '') {
            $borPhone = '';
        }
        if (trim($coBPhoneNumber) == '') {
            $coBPhoneNumber = '';
        }
        if (trim($borCell) == '') {
            $borCell = '';
        }
        if (trim($coBCellNumber) == '') {
            $coBCellNumber = '';
        }

        $formatSsnNumber = Strings::formatSSNNumber($ssnNumber);
        $formatCoBSsnNumber = Strings::formatSSNNumber($coBSsnNumber);

        $borrowerName = ucwords($borrowerFName . ' ' . $borrowerMName . ' ' . $borrowerLName);
        $coBorrowerName = ucwords($coBorrowerFName . ' ' . $coBorrowerLName);
        $clientName = $borrowerName;

        if (($coBorrowerFName != '') || ($coBorrowerLName != '')) {
            $clientName .= ', ' . $coBorrowerName;
        }

        if ($borPropAddress != '') {
            $propAddress = $borPropAddress;
        }
        if ($propertyCity != '') {
            $propAddress .= ', ' . $propertyCity;
        }
        if ($propertyState != '') {
            $propAddress .= ', ' . $propertyState;
        }
        if ($propertyZip != '') {
            $propAddress .= ' ' . $propertyZip . '.';
        }

        $appendComma = '';

        if (($mailingAddress == '') || ($mailingAddress == NULL)) {
        } else {
            $maillingAddressInfo .= $appendComma . $mailingAddress;
            $appendComma = ', ';
        }
        if (($mailingCity == '') || ($mailingCity == NULL)) {
        } else {
            $maillingAddressInfo .= $appendComma . $mailingCity;
            $appendComma = ', ';
        }
        if (($mailingState == '') || ($mailingState == NULL)) {
        } else {
            $maillingAddressInfo .= $appendComma . $mailingState;
            $appendComma = ' ';
        }
        if (($mailingZip == '') || ($mailingZip == NULL)) {
        } else {
            $maillingAddressInfo .= $appendComma . $mailingZip . '.';
            $appendComma = '';
        }

        $sameAddressOfProperty = '';
        if (trim($maillingAddressInfo) == trim($propAddress)) {
            $sameAddressOfProperty = 'same';
        } else {
            $sameAddressOfProperty = $propAddress;
        }

        $pastDeals = Strings::replaceCommaValues($nored) + Strings::replaceCommaValues($nocd) + Strings::replaceCommaValues($noOfProjectCurrently);


        if ($earnestDeposit < 0) $earnestDeposit = abs($earnestDeposit);

        if ($otherDownPayment < 0) $otherDownPayment = abs($otherDownPayment);

        /********* Extend the TCPDF class to create custom Header and Footer ********/

        $pdf = $pdf ?? new loanTermSheet (PDF_PAGE_ORIENTATION, PDF_UNIT, NEW_PDF_PAGE_FORMAT, true, 'UTF-8', false);

        /** Page layout params **/
        $xVal = 10;
        $yVal = $startYval = 20;
        $tempYval = 35;
        $w5 = 5;
        $w2 = 2;
        $w8 = 8;
        $unSelectedImg = "<img src=\"" . CONST_ROOT_PATH . "assets/images/icon_chk_0.jpg\">";
        $compHeight = 0;
        $contactHeight = 0;
        $tempHeight = 0;
        $curDate = date('m/d/Y');
        $curdate1 = date('M j , Y');

        //$pdf->setMargins(leftAndRightMarginVal, topMarginVal, leftAndRightMarginVal);
        $pdf->setMargins(leftAndRightMarginVal, 10, leftAndRightMarginVal);
        $pdf->setAutoPageBreak(TRUE, leftAndRightMarginVal);

        /******** Editable PDF Code Starts Here  -- Do not Change *********/

        $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
        $pdf->setFontSubsetting(false);

        /******** Editable PDF Code Ends Here  -- Do not Change *********/

        $pdf->AddPage();
        $showFooter = 1;
        $pdf->setLeftMargin($xVal);
        $pdf->setRightMargin($xVal);
        $pdf->setFont('helvetica', '', 9.3);
        $pdf->setCellHeightRatio('1.5');


         $pcLogo = $PCInfoArray['procCompLogo'];

        if ($branchLogo && file_exists(CONST_BRANCH_LOGO_PATH . $branchLogo)) {
            if ($PCID == 4661) { //****exception PCID = 4661
                $myStr = "<table border=\"0\"><tr><td width=\"35%\"><img src=\"" . CONST_BRANCH_LOGO_PATH . $branchLogo . "\" height=\"65px\"></td></tr></table>";
            } else {
                $myStr = "<table border=\"0\"><tr><td width=\"35%\"><img src=\"" . CONST_BRANCH_LOGO_PATH . $branchLogo . "\" height=\"50px\"></td></tr></table>";
            }
            $pdf->setXY($xVal, $tempYval - 25);
            $pdf->writeHTML($myStr, true, 0, true, 0);
            $yVal = $pdf->GetY();
        }else if($pcLogo && file_exists(CONST_PATH_PC_LOGO . $pcLogo)) {
            $myStr = "<table border=\"0\"><tr><td width=\"35%\"><img src=\"" . CONST_PATH_PC_LOGO . $pcLogo . "\" height=\"70px\"></td></tr></table>";
            $pdf->setXY($xVal, $tempYval - 25);
            $pdf->writeHTML($myStr, true, 0, true, 0);
            $yVal = $pdf->GetY();
        }


        $title = 'Loan Term Sheet';
        if ($packageId == 1166) $title = 'Loan Approval'; //Rock East funding https://app.shortcut.com/lendingwise/story/38492/copy-of-a-package-loan-term-sheet
        if ($packageId == 1096) $title = 'Conditional Loan Approval';
        $myStr = "<h1 style=\"text-align:center\"><u>{$title}</u></h1>
	<p style=\"text-align:center\"><b>" . $curdate1 . "</b></p>
	<table border=\"0\">
		<tr><td width=\"40%\"><b>Borrower:</b></td><td width=\"60%\"><b>" . $entityName . '</b></td></tr>
		<tr><td><b>Principal:</b></td><td><b>' . $clientName . '</b></td></tr>
		<tr><td><b>Property Address:</b></td><td><b>' . $propAddress . '</b></td></tr>
		<tr><td><b>Loan Program:</b></td><td><b>' . $LMRClientTypeInfo[0]['ClientTypeName'] . '</b></td></tr>
		<tr><td><b>Transaction Type:</b></td><td><b>' . $typeOfHMLOLoanRequesting . '</b></td></tr>
		<tr><td><b>Term:</b></td><td><b>';
        $comma = '';
        if ($loanTerm != '' && $lien1Terms != '') $comma = ', ';
        if ($loanTerm != '') {
            $myStr .= $loanTerm;
        }
        $myStr .= $comma;
        if ($lien1Terms != '') {
            $myStr .= $lien1Terms;
        }
        $myStr .= '</b></td></tr>';
        if ($typeOfHMLOLoanRequesting == 'Purchase' || $typeOfHMLOLoanRequesting == 'Commercial Purchase') {
            $myStr .= '<tr><td><b>Loan Amount:</b></td><td><b>$ ' . Currency::formatDollarAmountWithDecimal($totalLoanAmount) . '</b></td></tr>
			  <tr><td><b>Interest Rate:</b></td><td><b>' . $lien1RateDirect . '%</b></td></tr>';
        }
        if ($extensionOption != 0 && !glCustomJobForProcessingCompany::isCustomLoanTermSheetHideExtensionOption($PCID)) {
            $myStr .= '<tr><td><b>Extension Options:</b></td><td><b>' . $extensionOption . '</b></td></tr>';
        }
        if ($prePaymentPenaltyPerAndValue != 0) {
            $myStr .= '<tr><td><b>Early Payoff:</b></td><td><b>' . $prePaymentPenaltyPerAndValue . '</b></td></tr>';
        }

        $myStr .= '<tr><td><b>Assumability:</b></td><td>';
        if ($assumability == 'Yes') {
            $myStr .= 'Loan is assumable.';
        } else {
            $myStr .= 'Loan is NOT assumable.';
        }
        if (in_array($PCID, $glFirstRehabLending)) {
            $noYearTxt = '18 months';
        } else {
            $noYearTxt = '3 years';
        }
        $myStr .= '</td></tr>';
        if ($loanTermExpireDate != '') {
            $myStr .= '<tr><td><b>Approval Expiration:</b></td><td><b>' . $loanTermExpireDate . '</b></td></tr>';
        }

        $myStr .= '<tr><td><b>Guarantor:</b></td><td >Personal guarantee by the principal(s) of the Borrower</td></tr>
			<tr><td><b>Collateral:</b></td><td>' . $lienPositionVal . ' lien deed of trust/mortgage, assignment of rents and fixtures.</td></tr>';
        if ($HMLOEstateHeldIn != '') {
            $myStr .= '<tr><td><b>Estate will be held in:</b></td><td>' . $HMLOEstateHeldIn . '</td></tr>';
        }
        if ($prePaymentSelectVal) {
            $myStr .= '<tr><td><b>Pre-Payment Penalty Options::</b></td><td>' . $prePaymentSelectVal . '</td></tr>';
        }
        $myStr .= '</table>';

        $pdf->setXY($xVal, $yVal - 10);
        $pdf->writeHTML($myStr, true, 0, true, 0);
        $yVal = $pdf->GetY() + 0.5;
        $myStr = "";
        if ($PCID != 5128) {  //sc 55409 hide from pc
            $myStr .= '<p>PLEASE NOTE: <i>The Following Terms are conditional and subject to appraised value, verification of funds, borrower experience [of <b>' . $pastDeals . '</b> of past deals] and FICO score';
            if ($midFico != 0) {

                $myStr .= '( <b>' . $midFico . '</b> ).';
            }
            if ($midFico == 0) {

                $myStr .= '(if applicable*).';
            }
            $myStr .= " Terms may be subject to change at the lenders discretion</i></p>";
        }
        $myStr .= "<table border=\"1\" cellpadding=\"5\" width=\"100%\">
		<tr><th width=\"100%\" style=\"border-bottom:1px solid #000;\"><h3> Rate & Payment</h3></th></tr>
		<tr><td width=\"50%\">RATE:</td><td width=\"50%\"><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> " . $lien1RateDirect . '</b> %</td></tr></table></td></tr>';
//<tr><td>ORIGINATION POINTS:</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> ".$originationPointsRate."</b></td></tr></table></td></tr>
        if ($typeOfHMLOLoanRequesting == 'Rate & Term Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance') {

        } else if ($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance' || $typeOfHMLOLoanRequesting == 'New Construction - Existing Land' || $typeOfHMLOLoanRequesting == 'Refinance') {
            $myStr .= "<tr><td>Total Loan Amount:</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($totalLoanAmount) . '</b></td></tr></table></td></tr>';
        } else {
            $myStr .= "<tr><td>Acquisition LOAN AMOUNT:</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($initialLoanAmount) . '</b></td></tr></table></td></tr>';
        }
        if ($isLoanPaymentAmt != 'SMP') {
            if ($lien1Terms == LoanTerms::INTEREST_ONLY) {
                $myStr .= "<tr><td>Payment (Interest):</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($totalMonthlyPayment) . '</b></td></tr></table></td>
		</tr>';
            } else {
                $myStr .= "<tr><td>Payment (Principal + Interest):</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($totalMonthlyPayment) . '</b></td></tr></table></td>
		</tr>';
            }
        } else {
            if ($lien1Terms == LoanTerms::INTEREST_ONLY) {
                $myStr .= "<tr><td>Payment (Interest):</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($lien1Payment) . '</b></td></tr></table></td>
		</tr>';
            } else {
                $myStr .= "<tr><td>Payment (Principal+Interest):</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($lien1Payment) . '</b></td></tr></table></td>
		</tr>';
            }
        }
        if ($prepaidInterestReserve != 0) {
            $myStr .= "<tr><td>Pre-paid Interest Reserve:</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($prepaidInterestReserve) . '</b></td></tr></table></td>
		</tr>';
        }
//echo $monthlyIns; die();
        $piti = Currency::formatDollarAmountWithDecimal(Strings::replaceCommaValues($totalMonthlyPayment) + $taxes + $monthlyIns + $spcf_hoafeesMonthly);
        $piti2 = Currency::formatDollarAmountWithDecimal(Strings::replaceCommaValues($lien1Payment) + $taxes + $monthlyIns + $spcf_hoafeesMonthly);
        if ($isTaxesInsEscrowed == 'Yes') {
            if ($isLoanPaymentAmt != 'SMP') {
                $myStr .= "<tr><td>Monthly Payment (Principal + Interest + Taxes + Insurance):</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> $ " . $piti . '</b></td></tr></table></td></tr>';
            } else {
                $myStr .= "<tr><td>Monthly Payment (Principal + Interest + Taxes + Insurance):</td><td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> $ " . $piti2 . '</b></td></tr></table></td></tr>';
            }
        }
        $myStr .= '</table>';

        $pdf->setXY($xVal, $yVal - 4);
        $pdf->writeHTML($myStr, true, 0, true, 0);
        $yVal = $pdf->GetY() + 1;

        $myStr = "<table border=\"1\" cellpadding=\"5\">
		<tr><th width=\"100%\" style=\"border-bottom:1px solid #000;\"><h3> LOAN AMOUNT</h3></th></tr>";
        if ($typeOfHMLOLoanRequesting == 'Rate & Term Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance' || $typeOfHMLOLoanRequesting == 'Cash-Out / Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance' || $typeOfHMLOLoanRequesting == 'Refinance') {
            $myStr .= "<tr>
			<td width=\"50%\">
				Property Value As Is:<br/>Initial Loan Amount<br/>";
//            if ($typeOfHMLOLoanRequesting != 'Cash-Out / Refinance' && $typeOfHMLOLoanRequesting != 'Commercial Cash Out Refinance') {  //commented because sc30930 miguel says marketltv should be in refis
            $myStr .= 'Market LTV:<br/>';
//            }  //commented because sc30930 miguel says marketltv should be in refis
            if ($payOffMortgage1 > 0) {
                $myStr .= 'Pay Off on Mortgage 1:<br/>';
            }
            if ($payOffMortgage2 > 0) {
                $myStr .= 'Pay Off on Mortgage 2:<br>';
            }
            if (($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance' || $typeOfHMLOLoanRequesting == 'Refinance') && $payOffOutstandingTaxes > 0) {
                $myStr .= 'Pay Off on Outstanding Taxes:<br/>';
            }
            if ($closingCostFinanced > 0) {
                $myStr .= 'Closing Costs Financed:';
            }
            $myStr .= "</td>
			<td width=\"50%\"><table>
			<tr><td style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($homeValue) . "</b></td></tr>
            <tr><td style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($initialLoanAmount) . '</b></td></tr>';
//            if ($typeOfHMLOLoanRequesting != 'Cash-Out / Refinance' && $typeOfHMLOLoanRequesting != 'Commercial Cash Out Refinance' && $typeOfHMLOLoanRequesting != 'Refinance') {  //commented because sc30930 miguel says marketltv should be in refis
            $myStr .= "<tr><td style=\"text-align:right\"><b> " . Currency::formatDollarAmountWithDecimal($marketLTV) . '%</b></td></tr>';
//            }     //commented because sc30930 miguel says marketltv should be in refis
            if ($payOffMortgage1 != 0) {
                $myStr .= "<tr><td style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($payOffMortgage1) . '</b></td></tr>';
            }
            if ($payOffMortgage2 != 0) {
                $myStr .= "<tr><td style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($payOffMortgage2) . '</b></td></tr>';
            }
            if (($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance' || $typeOfHMLOLoanRequesting == 'Refinance') && $payOffOutstandingTaxes > 0) {
                $myStr .= "<tr><td style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($payOffOutstandingTaxes) . '</b></td></tr>';
            }
            if ($closingCostFinanced != 0) {
                $myStr .= "<tr><td style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($closingCostFinanced) . '</b></td></tr>';
            }
            $myStr .= '</table>
			</td>
		</tr>';
        } else {
            if ($typeOfHMLOLoanRequesting == 'New Construction - Existing Land') {
            } else {
                $myStr .= "<tr nobr=\"true\">
			<td width=\"50%\">";
                if ($costBasis) {
                    $myStr .= "Acquisition Price:<br/>";
                }
                if ($homeValue) {
                    $myStr .= "As-is Price:<br/>";
                }
                if ($acquisitionLTV) {
                    $myStr .= "Acquisition LTV:<br/>";
                }
                if ($acquisitionPriceFinanced) {
                    $myStr .= "Acquisition Price Financed:<br/>";
                }
                if ($earnestDeposit) {
                    $myStr .= "Earnest Deposit:<br/>";
                }
                if ($otherDownPayment) {
                    $myStr .= "Other Down Payment:<br/>";
                }
                if ($maxAmtToPutDown) {
                    $myStr .= "Acquisition Down Payment:";
                }
                $myStr .= "</td>
			<td width=\"50%\"><table>";
			if($costBasis){
			    $myStr .= "<tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($costBasis) . "</b></td></tr>";
            }
            if($homeValue){
			    $myStr .= "<tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($homeValue) . "</b></td></tr>";
            }
            if($acquisitionLTV){
			    $myStr .= "<tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> " . $acquisitionLTV . "</b> %</td></tr>";
            }
            if($acquisitionPriceFinanced){
			    $myStr .= "<tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($acquisitionPriceFinanced) . "</b></td></tr>";
            }
            if($earnestDeposit){
			    $myStr .= "<tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($earnestDeposit) . "</b></td></tr>";
			}
            if($otherDownPayment){
			    $myStr .= "<tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($otherDownPayment) . "</b></td></tr>";
			}
			if($maxAmtToPutDown){
			    $myStr .= "<tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($maxAmtToPutDown) . "</b></td></tr>";
			}
            $myStr .= '</table>
			</td>
		</tr>';
            }
        }
        /*	$myStr .="<td width=\"20%\"><table>
                    <tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b>$ ".Currency::formatDollarAmountWithDecimal($costBasis)."</b></td></tr>
                    <tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> $".Currency::formatDollarAmountWithDecimal($homeValue)."</b></td></tr>
                    <tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> ".$acquisitionLTV."</b> %</td></tr>
                    <tr style=\"background-color:#a6a6a6\"><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b>$ ".$acquisitionPriceFinanced."</b></td></tr>
                    <tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> $".Currency::formatDollarAmountWithDecimal($maxAmtToPutDown)."</b></td></tr>
                    </table>
                    </td>
                </tr>";*/
        if ($propertyNeedRehab == 'Yes' && ($typeOfHMLOLoanRequesting != 'Transactional')) {

            if (Strings::replaceCommaValues($rehabCost) > 0 || Strings::replaceCommaValues($perRehabCostFinanced) > 0 || Strings::replaceCommaValues($rehabCostFinanced)) {
                $myStr .= "<tr nobr=\"true\">
			<td width=\"50%\">";
                if ($typeOfHMLOLoanRequesting == 'New Construction - Existing Land') {

                    if (Strings::replaceCommaValues($originalPurchasePrice) > 0) $myStr .= 'Original Purchase price:<br/>';

                    if ($originalPurchaseDate != '') $myStr .= 'Original Purchase Date:<br/>';

                    if (Strings::replaceCommaValues($homeValue) > 0) $myStr .= 'As-Is Value:<br/>';

                }
                if (Strings::replaceCommaValues($rehabCost) > 0) $myStr .= 'Rehab/Construction Cost:<br/>';

                if (Strings::replaceCommaValues($perRehabCostFinanced) > 0) $myStr .= 'Percentage of Rehab/Const Cost Financed:<br/>';

                if (Strings::replaceCommaValues($rehabCostFinanced) > 0) $myStr .= 'Rehab/Construction Cost Financed:';
                $myStr .= "</td>
			<td width=\"50%\">";
                if (($originalPurchasePrice) > 0 || ($rehabCost) > 0 || ($originalPurchaseDate) != '' || ($rehabCost) > 0 || ($perRehabCostFinanced) > 0 || ($rehabCostFinanced) > 0) {
                    $myStr .= "<table border=\"0\" width=\"100%\">";

                    if ($typeOfHMLOLoanRequesting == 'New Construction - Existing Land') {

                        if (Strings::replaceCommaValues($originalPurchasePrice) > 0) $myStr .= "<tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($originalPurchasePrice) . '</b></td></tr>';

                        if ($originalPurchaseDate != '') $myStr .= "<tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> " . $originalPurchaseDate . '</b></td></tr>';

                        if (Strings::replaceCommaValues($homeValue) > 0) $myStr .= "<tr><td style=\"text-align:right\" colspan=\"2\"><b>$ " . Currency::formatDollarAmountWithDecimal($homeValue) . '</b></td></tr>';
                    }

                    if (Strings::replaceCommaValues($rehabCost) > 0) $myStr .= "<tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($rehabCost) . '</b></td></tr>';

                    if (Strings::replaceCommaValues($perRehabCostFinanced) > 0) $myStr .= "<tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> " . $perRehabCostFinanced . '</b> %</td></tr>';

                    if (Strings::replaceCommaValues($rehabCostFinanced) > 0) $myStr .= "<tr><td style=\"text-align:right\"></td><td style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($rehabCostFinanced) . '</b></td></tr>';

                    $myStr .= '</table>';
                }
                $myStr .= '</td>
		</tr>';
            }

            $myStr .= " <tr nobr=\"true\">";
            if ($typeOfHMLOLoanRequesting == 'New Construction - Existing Land') {
                $myStr .= '<td>Total Project Value (As-Is Value + Rehab Const Cost):<br/>';
            } else {
                $myStr .= '<td>Total Project Cost (As-Is Value + Rehab Const Cost):<br/>';
            }


            $myStr .= "</td>
			<td><table>
			<tr><td style=\"text-align:right\" width=\"30%\"></td>
			<td width=\"70%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($totalProjectCost) . "</b></td></tr>";

            $myStr .= '</table>
			</td>
		</tr>';
        }
        $myStr .= '</table>';

        if ($typeOfHMLOLoanRequesting == 'Purchase' || $typeOfHMLOLoanRequesting == 'Commercial Purchase' || $typeOfHMLOLoanRequesting == 'New Construction - Existing Land') {
            $myStr .= "<table border=\"1\" width=\"100%\" cellpadding=\"5\" nobr=\"true\">
			<tr>
				<td width=\"50%\">Acquisition + Rehab + Closings Costs Financed</td>
				<td width=\"50%\" style=\"text-align:right\"><b>$  " . Currency::formatDollarAmountWithDecimal($totalLoanAmount) . '</b></td>
			</tr>
		</table>';
        }
        if ($typeOfHMLOLoanRequesting == 'Delayed Purchase' || $typeOfHMLOLoanRequesting == 'Rate & Term Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance' || $typeOfHMLOLoanRequesting == 'Purchase' || $typeOfHMLOLoanRequesting == 'Commercial Purchase' || $typeOfHMLOLoanRequesting == 'New Construction - Existing Land') {
            $myStr .= "<table border=\"1\" width=\"100%\" cellpadding=\"5\" nobr=\"true\">
			<tr>
				<td width=\"50%\" >Total Loan Amount</td>
				<td width=\"50%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($totalLoanAmount) . '</b></td>
			</tr>
		</table>';
        }

        $pdf->setXY($xVal, $yVal);
        $pdf->writeHTML($myStr, true, 0, true, 0);
        $myStr = '';
        $yVal = $pdf->GetY() + 1;
        /* Check if there is enough space to start writing a new section */
        if ($yVal > 250) {
            $yVal = $startYval;
            $pdf->AddPage();
        }

        if ($propertyNeedRehab == 'Yes') {
            $myStr = "<table border=\"1\" cellpadding=\"5\">
		<tr><th width=\"100%\" style=\"border-bottom:1px solid #000;\"><h3> AFTER REPAIR VALUE (ARV)</h3></th></tr>
		<tr>
			<td width=\"50%\">ARV %<br/>
				After-Repair-value
			</td>
			<td width=\"50%\"><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\" ><b>" . $ARV . "</b> %</td></tr>
			<tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\" ><b>$ " . Currency::formatDollarAmountWithDecimal($assessedValue) . '</b></td></tr></table>
			</td>
		</tr>
	</table>';

            $pdf->setXY($xVal, $yVal);
            $pdf->writeHTML($myStr, true, 0, true, 0);
            $myStr = '';
            $yVal = $pdf->GetY();
            if ($yVal > 250) {
                $yVal = $startYval;
                $pdf->AddPage();
            }
        }
        $feeAndCostArray = ['originationPointsValue', 'brokerPointsValue', 'applicationFee', 'appraisalFee', 'estdTitleClosingFee', 'processingFee', 'drawsFee', 'drawsSetUpFee', 'miscellaneousFee', 'valuationBPOFee', 'valuationCMAFee', 'valuationAVEFee', 'valuationAVMFee', 'creditReportFee', 'creditCheckFee', 'employmentVerificationFee', 'backgroundCheckFee', '$cityCountyTaxStamps', 'taxReturnOrderFee', 'floodCertificateFee', 'loanOriginationFee', 'documentPreparationFee', 'wireFee', 'servicingSetUpFee', 'taxServiceFee', 'floodServiceFee', 'constructionHoldbackFee', 'thirdPartyFees', 'otherFee', 'taxImpoundsFee', 'insImpoundsFee', 'totalEstPerDiem', 'escrowFees', 'recordingFee', 'propertyTax', 'underwritingFees', 'wireTransferToTitle', 'wireTransferToEscrow', 'bufferAndMessengerFees', 'travellingNotaryFee', 'prePaidInterest', 'realEstateTaxes', 'insurancePremium', 'payOffLiensCreditors', 'pastDuePropertyTaxes', 'attorneyFee', 'closingCostFinancingFee', 'inspectionFees', 'dueDiligence', 'UccLienSearch', 'projectFeasibility', 'survey', 'wholeSaleAdminFee'];

        $availableFeeCnt = 0;
        for ($fee = 0; $fee < count($feeAndCostArray); $fee++) {
            if (${$feeAndCostArray[$fee]} > 0) {
                $availableFeeCnt++;
            }
        }

        if ($availableFeeCnt > 0) {
            $myStr = "<table style=\"border:1px solid #000;\" nobr=\"true\" cellpadding=\"2\">
				<tr><th width=\"100%\" style=\"border-bottom:1px solid #000;\"><h3> Estimated Fees & Costs</h3></th></tr>";
            if ($originationPointsValue != 0) {
                $myStr .= "<tr>
					<td width=\"50%\">&nbsp;Origination Points</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\" ><b> $ " . Currency::formatDollarAmountWithDecimal($originationPointsValue) . '</b></td>
				</tr>';
            }
            if ($brokerPointsValue != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Broker Points</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($brokerPointsValue) . '</b></td>
					</tr>';
            }
            if ($applicationFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Application Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($applicationFee) . '</b></td>
					</tr>';
            }
            if ($appraisalFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Appraisal Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($appraisalFee) . '</b></td>
					</tr>';
            }
            if ($estdTitleClosingFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Estimated Title Insurance Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($estdTitleClosingFee) . '</b></td>
					</tr>';
            }
            if ($processingFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Processing fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($processingFee) . '</b></td>
					</tr>';
            }
            if ($drawsFee != 0) {
                $PCID != glPCID::PCID_ALTISOURCE ? $drawsLabel = "Draws Fee" : $drawsLabel = "Servicing Fee";
                $myStr .= "<tr><td width=\"50%\">&nbsp;$drawsLabel</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($drawsFee) . '</b></td>
					</tr>';
            }
            if ($drawsSetUpFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Draws Set Up Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($drawsSetUpFee) . '</b></td>
					</tr>';
            }
            if ($miscellaneousFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Miscellaneous Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($miscellaneousFee) . '</b></td>
					</tr>';
            }
            if ($valuationBPOFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Valuation - BPO</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($valuationBPOFee) . '</b></td>
					</tr>';
            }
            if ($valuationCMAFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Valuation - CMA</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($valuationCMAFee) . '</b></td>
					</tr>';
            }
            if ($valuationAVEFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Valuation - AVE</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($valuationAVEFee) . '</b></td>
					</tr>';
            }
            if ($valuationAVMFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Valuation - AVM</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($valuationAVMFee) . '</b></td>
					</tr>';
            }
            if ($creditReportFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Credit Report</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($creditReportFee) . '</b></td>
					</tr>';
            }
            if ($creditCheckFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Credit Check</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($creditCheckFee) . '</b></td>
					</tr>';
            }
            if ($employmentVerificationFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Employment Verification</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($employmentVerificationFee) . '</b></td>
					</tr>';
            }
            if ($backgroundCheckFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Background Check</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($backgroundCheckFee) . '</b></td>
					</tr>';
            }
            if ($cityCountyTaxStamps != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;City/County Tax Stamps</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($cityCountyTaxStamps) . '</b></td>
					</tr>';
            }
            if ($taxReturnOrderFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Tax Return Order</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($taxReturnOrderFee) . '</b></td>
					</tr>';
            }
            if ($floodCertificateFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Flood Certificate</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($floodCertificateFee) . '</b></td>
					</tr>';
            }
            if ($loanOriginationFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Loan Origination Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($loanOriginationFee) . '</b></td>
					</tr>';
            }
            if ($documentPreparationFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Document Preparation</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($documentPreparationFee) . '</b></td>
					</tr>';
            }
            if ($wireFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Wire Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($wireFee) . '</b></td>
					</tr>';
            }
            if ($servicingSetUpFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Servicing Set Up Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($servicingSetUpFee) . '</b></td>
					</tr>';
            }
            if ($taxServiceFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Tax Service</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($taxServiceFee) . '</b></td>
					</tr>';
            }
            if ($floodServiceFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Flood Service</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($floodServiceFee) . '</b></td>
					</tr>';
            }
            if ($constructionHoldbackFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Construction Holdback</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($constructionHoldbackFee) . '</b></td>
					</tr>';
            }
            if ($thirdPartyFees != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Lender Credit to Offset 3rd Party Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($thirdPartyFees) . '</b></td>
					</tr>';
            }
            if ($otherFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Other Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($otherFee) . '</b></td>
					</tr>';
            }
            if ($taxImpoundsFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Tax impounds Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($taxImpoundsFee) . '</b></td>
					</tr>';
            }
            if ($insImpoundsFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Ins impounds Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($insImpoundsFee) . '</b></td>
					</tr>';
            }
            if ($totalEstPerDiem != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Est Per Diem Int</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($totalEstPerDiem) . '</b></td>
					</tr>';
            }
            if ($escrowFees != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Escrow Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($escrowFees) . '</b></td>
					</tr>';
            }
            if ($recordingFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Recording Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($recordingFee) . '</b></td>
					</tr>';
            }
            if ($propertyTax != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Property Tax</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($propertyTax) . '</b></td>
					</tr>';
            }
            if ($underwritingFees != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Underwriting Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($underwritingFees) . '</b></td>
					</tr>';
            }
            if ($wireTransferToTitle != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Wire Transfer Fee to Title</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($wireTransferToTitle) . '</b></td>
					</tr>';
            }
            if ($wireTransferToEscrow != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Wire Transfer Fee to Escrow</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($wireTransferToEscrow) . '</b></td>
					</tr>';
            }
            if ($bufferAndMessengerFees != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Discount Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($bufferAndMessengerFees) . '</b></td>
					</tr>';
            }
            if ($travellingNotaryFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Travel Notary Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($travellingNotaryFee) . '</b></td>
					</tr>';
            }
            if ($prePaidInterest != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Pre paid Interest</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($prePaidInterest) . '</b></td>
					</tr>';
            }
            if ($realEstateTaxes != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Real Estate Taxes</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($realEstateTaxes) . '</b></td>
					</tr>';
            }
            if ($insurancePremium != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Insurance Premium</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($insurancePremium) . '</b></td>
					</tr>';
            }
            if ($payOffLiensCreditors != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Pay Off Liens/Creditors Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($payOffLiensCreditors) . '</b></td>
					</tr>';
            }
            if ($pastDuePropertyTaxes != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Past Due Property Taxes</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($pastDuePropertyTaxes) . '</b></td>
					</tr>';
            }
            if ($attorneyFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Attorney fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($attorneyFee) . '</b></td>
					</tr>';
            }
            if ($closingCostFinancingFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Closing Cost Financing fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($closingCostFinancingFee) . '</b></td>
					</tr>';
            }
            if ($inspectionFees != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Inspection Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($inspectionFees) . '</b></td>
					</tr>';
            }
            if ($dueDiligence != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Due Diligence Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($dueDiligence) . '</b></td>
					</tr>';
            }
            if ($UccLienSearch != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;UCC / Lien Search Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($UccLienSearch) . '</b></td>
					</tr>';
            }
            if ($projectFeasibility != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Project Feasibility Fees</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($projectFeasibility) . '</b></td>
					</tr>';
            }
            if ($survey != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Survey</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($survey) . '</b></td>
					</tr>';
            }
            if ($wholeSaleAdminFee != 0) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;Wholesale Admin Fee</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($wholeSaleAdminFee) . '</b></td>
					</tr>';
            }
            if ($availableFeeCnt == 17) {
                $myStr .= "<tr><td width=\"50%\">&nbsp;</td>
					<td width=\"50%\" style=\"border-left:1px solid #000;text-align:right\"> </td>
					</tr>";
            }
            $myStr .= '</table>';
        }


        $pdf->setXY($xVal, $yVal);
        $pdf->writeHTML($myStr, true, 0, true, 0);
        $yVal = $pdf->GetY();
        if ($yVal > 250) {
            $yVal = $startYval;
            $pdf->AddPage();
        }
        $myStr = "<table border=\"1\" width=\"100%\" cellpadding=\"5\" nobr=\"true\">
		<tr>
			<td width=\"50%\">Total Closing Costs</td>
			<td width=\"50%\" style=\"text-align:right\"><b>$&nbsp;" . Currency::formatDollarAmountWithDecimal($totalFeesAndCost) . "</b></td>
		</tr>
		<tr>
			<td >Percentage of Closing Costs Financed</td>
			<td style=\"text-align:right\"><b>" . $perClosingCostFinanced . "</b> %</td>
		</tr>";
        if($closingCostFinanced){
            $myStr .= "<tr>
			<td >Closing Costs Financed</td>
			<td style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($closingCostFinanced) . '</b></td>
		    </tr>';
        }

        $myStr .=	'</table>';

        $pdf->setXY($xVal, $yVal - 5);
        $pdf->writeHTML($myStr, true, 0, true, 0);
        $yVal = $pdf->GetY();
        if ($yVal > 250) {
            $yVal = $startYval;
            $pdf->AddPage();
        }

        $myStr = '';
        if ($typeOfHMLOLoanRequesting == 'Rate & Term Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance') {
            $casualTotalLoanAmount = ($payOffMortgage1 + $payOffMortgage2 + $closingCostFinanced);

            $myStr .= "<table border=\"1\" width=\"100%\" cellpadding=\"5\" nobr=\"true\">
			<tr>
				<td width=\"50%\">";
            if ($payOffMortgage1 != 0) $myStr .= 'Pay-Off on Mortgage 1 + ';
            if ($payOffMortgage2 != 0) $myStr .= 'Pay Off on Mortgage 2 + ';
            if ($closingCostFinanced != 0) $myStr .= 'Closing Costs Financed';
            $myStr .= "</td>
				<td width=\"50%\" style=\"text-align:right\"><b> $ " . Currency::formatDollarAmountWithDecimal($casualTotalLoanAmount) . '</b></td>
			</tr>
		</table>';

        } else if ($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance' || $typeOfHMLOLoanRequesting == 'Refinance') {
            $casualTotalLoanAmount = ($payOffMortgage1 + $payOffMortgage2 + $payOffOutstandingTaxes + $closingCostFinanced);

            $myStr .= "<table border=\"1\" width=\"100%\" cellpadding=\"5\" nobr=\"true\">
			<tr>
				<td width=\"50%\">";
            if ($payOffMortgage1 > 0) $myStr .= 'Pay-Off on Mortgage 1 + ';
            if ($payOffMortgage2 > 0) $myStr .= 'Pay Off on Mortgage 2 + ';
            if ($payOffOutstandingTaxes > 0) $myStr .= 'Pay Off Outstanding Taxes + ';
            if ($closingCostFinanced > 0) $myStr .= 'Closing Costs Financed';
            $myStr .= "</td>
				<td width=\"50%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($casualTotalLoanAmount) . '</b></td>
			</tr>
		</table>';
            if (!$payOffMortgage1 && !$payOffMortgage2 && !$payOffOutstandingTaxes && !$closingCostFinanced) {
                $myStr = '';  //empty line so im emptying it out so it doesn't render
            }

        }

        if ($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance' || $typeOfHMLOLoanRequesting == 'Rate & Term Refinance' || $typeOfHMLOLoanRequesting == 'Refinance') {
            $myStr .= "<table border=\"1\" width=\"100%\" cellpadding=\"5\" nobr=\"true\">
			<tr>
				<td width=\"50%\">" . $cashToBorLabel . "</td>
				<td width=\"50%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($totalCashToBorrower) . '</b></td>
			</tr>
		</table>';
        }


        if ($myStr != '') {
            $pdf->setXY($xVal, $yVal);
            $pdf->writeHTML($myStr, true, 0, true, 0);
            $yVal = $pdf->GetY();
        }


        if ($yVal > 250) {
            $yVal = $startYval;
            $pdf->AddPage();
        }
        $myStr = '';
//        if (Strings::replaceCommaValues($closingCostNotFinanced) > 0 || Strings::replaceCommaValues($maxAmtToPutDown) > 0) {
        if ($typeOfHMLOLoanRequesting != 'Cash-Out / Refinance' &&
            $typeOfHMLOLoanRequesting != 'Commercial Cash Out Refinance' &&
            $typeOfHMLOLoanRequesting != 'Commercial Rate / Term Refinance' &&
            $typeOfHMLOLoanRequesting != 'Rate & Term Refinance' &&
            $typeOfHMLOLoanRequesting != 'Refinance') {
            $myStr = "<table border=\"1\" cellpadding=\"5\">
		<tr><th width=\"100%\" style=\"border-bottom:1px solid #000;\"><h3> CASH-TO-CLOSE</h3></th></tr>
		<tr>
			<td width=\"50%\">";
            if (Strings::replaceCommaValues($closingCostNotFinanced) > 0) $myStr .= 'Closing Costs not financed';
            if (Strings::replaceCommaValues($closingCostNotFinanced) > 0 && Strings::replaceCommaValues($maxAmtToPutDown) > 0) $myStr .= '<br>';
            if (Strings::replaceCommaValues($maxAmtToPutDown) > 0) $myStr .= 'Down Payment';
            $myStr .= "</td>
			<td width=\"50%\">";

            if (Strings::replaceCommaValues($closingCostNotFinanced) > 0 || Strings::replaceCommaValues($maxAmtToPutDown) > 0) {
                $myStr .= '<table>';
                if (Strings::replaceCommaValues($closingCostNotFinanced) > 0) $myStr .= "<tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($closingCostNotFinanced) . '</b></td></tr>';
                if (Strings::replaceCommaValues($maxAmtToPutDown) > 0) $myStr .= "<tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($maxAmtToPutDown) . '</b></td></tr>';
                $myStr .= '</table>';
            }
            $myStr .= "</td>
		</tr>
		<tr>
			<td>Total Cash-to-Close</td>
			<td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\" ><b>$ " . Currency::formatDollarAmountWithDecimal($totalCashToClose) . "</b></td></tr></table></td>
		</tr>";

            $myStr .= "</table>";
            if ($typeOfHMLOLoanRequesting == 'Cash-Out / Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Cash Out Refinance' || $typeOfHMLOLoanRequesting == 'Commercial Rate / Term Refinance' || $typeOfHMLOLoanRequesting == 'Rate & Term Refinance' || $typeOfHMLOLoanRequesting == 'Refinance') {
                $myStr .= "<table border=\"1\" width=\"100%\" cellpadding=\"5\" nobr=\"true\">
			<tr>
				<td width=\"50%\">" . $cashToBorLabel . "</td>
				<td width=\"50%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($totalCashToBorrower) . '</b></td>
			</tr>
		</table>';
            }
            $pdf->setXY($xVal, $yVal);
            $pdf->writeHTML($myStr, true, 0, true, 0);
            $yVal = $pdf->GetY();
        }
        if ($yVal > 250) {
            $yVal = $startYval;
            $pdf->AddPage();
        }

        if ($paymentReserves != ''
            || ($propertyNeedRehab == 'Yes' &&
                (Strings::replaceCommaValues($requiredConstructionAmt) > 0 || Strings::replaceCommaValues($contingencyReserveAmt) > 0))
            || Strings::replaceCommaValues($paymentReservesAmt) > 0
            || Strings::replaceCommaValues($totalRequiredReserves) > 0 || $percentageTotalLoan > 0) {
            $myStr = "<table border=\"1\" cellpadding=\"5\" nobr=\"true\">
		<tr><th width=\"100%\" style=\"border-bottom:1px solid #000;\"><h3> REQUIRED RESERVES</h3></th></tr>
		<tr>
			<td width=\"50%\">";

            if ($paymentReserves != '') {
                if ($paymentReserves == 1) {
                    $myStr .= '<b>'.$paymentReserves . ' Month</b>';
                } else {
                    $myStr .= '<b>'.$paymentReserves . ' Months</b>';
                }
                $myStr .= ' Interest / Payment Reserves';
            }
            if ($propertyNeedRehab == 'Yes') {
                if ($requiredConstruction) {
                    $myStr .= '<br><b>' . $requiredConstruction . '</b> % Required Construction / Rehab Budget Not Financed';
                }
                if ($contingencyReserve) {
                    $myStr .= '<br><b>' . $contingencyReserve . '</b> % Contingency Reserve';
                }
            }
            if ($percentageTotalLoan > 0) {
                $myStr .= '<br><b>' . $percentageTotalLoan . '</b> % of Total Loan Amount';
            }
            $myStr .= "</td>
			<td width=\"50%\">";
            if (Strings::replaceCommaValues($paymentReservesAmt) > 0
                || ($propertyNeedRehab == 'Yes' && Strings::replaceCommaValues($contingencyReserveAmt) > 0
                    || Strings::replaceCommaValues($requiredConstructionAmt) > 0 || Strings::replaceCommaValues($percentageTotalLoanAmount) > 0)) {
                $myStr .= '<table>';
                if ($paymentReserves != '') {
                    $myStr .= "<tr>
						<td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b>";
                    if (Strings::replaceCommaValues($paymentReservesAmt) > 0) {
                        $myStr .= "$ " . Currency::formatDollarAmountWithDecimal($paymentReservesAmt);
                    }

                    $myStr .= '</b></td>
					</tr>';
                }
                if (Strings::replaceCommaValues($requiredConstructionAmt) > 0 && $propertyNeedRehab == 'Yes') {
                    $myStr .= "<tr>
					<td style=\"text-align:right\" width=\"30%\"></td>
					<td width=\"70%\" style=\"text-align:right\"><b>";
                    $myStr .= "$ " . Currency::formatDollarAmountWithDecimal($requiredConstructionAmt);
                    $myStr .= '</b></td>
				</tr>';
                }
                if (Strings::replaceCommaValues($contingencyReserveAmt) > 0 && $propertyNeedRehab == 'Yes') {
                    $myStr .= "<tr>
					<td style=\"text-align:right\" width=\"30%\"></td>
					<td width=\"70%\" style=\"text-align:right\"><b>";
                    $myStr .= "$ " . Currency::formatDollarAmountWithDecimal($contingencyReserveAmt);

                    $myStr .= '</b></td>
				</tr>';
                }
                if (Strings::replaceCommaValues($percentageTotalLoan) > 0) {
                    $myStr .= "<tr>
					<td style=\"text-align:right\" width=\"30%\"></td>
					<td width=\"70%\" style=\"text-align:right\"><b>";
                    $myStr .= "$ " . Currency::formatDollarAmountWithDecimal($percentageTotalLoanAmount);
                    $myStr .= '</b></td>
				</tr>';
                }
                $myStr .= '</table>';
            }
            $myStr .= '</td>
		</tr>';
            if (Strings::replaceCommaValues($totalRequiredReserves) > 0) {
                $myStr .= "<tr>
			<td>Total Required Reserves</td>
			<td><table><tr><td style=\"text-align:right\" width=\"30%\"></td><td width=\"70%\" style=\"text-align:right\"><b>$ " . Currency::formatDollarAmountWithDecimal($totalRequiredReserves) . '</b></td></tr></table></td>
		</tr>';
            }
            $myStr .= '</table>';

            $pdf->setXY($xVal, $yVal);
            $pdf->writeHTML($myStr, true, 0, true, 0);
            $yVal = $pdf->GetY();
        }
        if ($yVal > 250) {
            $yVal = $startYval;
            $pdf->AddPage();
        }

        $myStr = '';

        if ($checkDisplayTermSheet == 'Yes') {
            $myStr .= '<p><b>' . $lenderNotes . '</b></p><p>&nbsp;</p>';
        }
        if ($expectForDueDiligence != '') {
            $myStr .= "<p style=\"text-align:justify\"><b>Additional Terms & Requirements for Loan Processing & Underwriting</b>
	<p>" . urldecode($expectForDueDiligence) . '</p>';
        }
        $myStr .= " <table border=\"0\" cellpadding=\"5\">";
        if (!glCustomJobForProcessingCompany::isCustomLoanTermSheetHideCCostsSection($PCID)) {
            if (glCustomJobForProcessingCompany::isCustomLoanTermSheetAltCCostsSection($PCID)) {
                $myStr .= " <tr><td width=\"30%\"><b>Closing Costs:</b></td><td width=\"70%\">Customary closing costs, including but not limited to title insurance, legal fees, filing fees
and any other expense associated with the loan are all to be paid by the Borrower.</td></tr>";
            } else {
                $myStr .= " <tr><td width=\"30%\"><b>Closing Costs:</b></td><td width=\"70%\">Customary closing costs, including but not 
		limited to title insurance, legal fees, filing fees and any other expense associated with the loan are all to be
		 paid by the Borrower, whether or not the loan is ultimately consummated.</td></tr>";
            }
        }
        if ($drawsSetUpFee != 0 && $applicationFee != 0) {
            $myStr .= '<tr><td width="30%"><b>Fees:</b></td><td width="70%">An application fee of <b>$ ' . Currency::formatDollarAmountWithDecimal($applicationFee) . '</b> will be collected at the time of application and applied to the cost of the property valuation/BPO. A draw set up fee of  <b>$ ' . Currency::formatDollarAmountWithDecimal($drawsSetUpFee) . '</b> will be charged at closing. Each draw is <b>$ ' . Currency::formatDollarAmountWithDecimal($drawsFee) . '</b></td></tr>';
        }
        $myStr .= '</table>';
        $pdf->setXY($xVal, $yVal + 4);
        $pdf->writeHTML($myStr, true, 0, true, 0);
        $yVal = $pdf->GetY();

        $myStr = "<p><br><table border=\"0\" cellpadding=\"5\">
		<tr><td width=\"65%\" style=\"border-top:1px solid #000\"><b>Borrower Signature</b></td></tr>
	</table></p>";
        $pdf->setXY($xVal, $yVal + 12);
        $pdf->writeHTML($myStr, true, 0, true, 0);
        $yVal = $pdf->GetY();

        /*** Purchaser Signature Get x and y position Start ***/

        $tempPosArray['pageNo'] = $pdf->PageNo();
        $tempPosArray['xPos'] = $xVal;
        $tempPosArray['yPos'] = $yVal - 33;
        $tempPosArray['showTo'] = 'Client';
        $tempPosArray['width'] = 60;
        $tempPosArray['maxLen'] = 58;
        $tempPosArray['signField'] = 1;

        $glPositionArray[] = $tempPosArray;

        if ($isCoBorrower == 1) {

            $myStr = "<p><br><table border=\"0\" cellpadding=\"5\">
		<tr><td width=\"65%\" style=\"border-top:1px solid #000\"><b>Co-Borrower Signature</b></td></tr>
	</table></p>";
            $pdf->setXY($xVal, $yVal + 4);
            $pdf->writeHTML($myStr, true, 0, true, 0);
            $yVal = $pdf->GetY();


            /*** Get x and y position Start ***/

            $tempPosArray['pageNo'] = $pdf->PageNo();
            $tempPosArray['xPos'] = $xVal;
            $tempPosArray['yPos'] = $yVal - 33;
            $tempPosArray['showTo'] = 'Co-Client';
            $tempPosArray['signField'] = 1;
            $tempPosArray['fldType'] = '';

            $glPositionArray[] = $tempPosArray;
        }

        /*** Purchaser Signature Get x and y position End ***/
        ob_end_clean();
        return $pdf;

    }

}