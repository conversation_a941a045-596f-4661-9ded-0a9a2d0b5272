{"devDependencies": {"@faker-js/faker": "^9.6.0", "cypress": "^14.5.0", "cypress-file-upload": "^5.0.8", "dotenv": "^16.4.7", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"cypress-multi-reporters": "^2.0.5", "cypress-parallel": "^0.15.0"}, "scripts": {"cy:open-e2e": "cypress open --e2e --browser chrome", "cy:run-e2e": "cypress run --e2e --browser chrome --record", "cy:run-unit": "cypress run --component --browser chrome --record", "cy:parallel": "cypress-parallel -s cy:run -t 2 -d ./cypress/e2e"}}