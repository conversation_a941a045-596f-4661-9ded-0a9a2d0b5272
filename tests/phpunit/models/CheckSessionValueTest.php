<?php

namespace tests\phpunit\models;

use models\standard\Strings;
use models\standard\UserAccess;
use models\CheckSessionValue;
use PHPUnit\Framework\TestCase;

/**
 * Tests the redirectIfSessionEnded method of the CheckSessionValue class.
 *
 * Auto-generated docblock. Please refine descriptions as needed.
 */
class CheckSessionValueTest extends TestCase
{
  // Static-only, so no setUp needed

  /**
   * Tests the redirectIfSessionEnded method of the CheckSessionValue class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testRedirectIfSessionEnded()
  {
    // TODO: Need dependency injection on UserAccess/Strings for this test.
    // Test can't be run reliably without refactor, skip for now.
    $this->markTestSkipped('TODO: Mocking global state required, needs refactor.');
  }
}
