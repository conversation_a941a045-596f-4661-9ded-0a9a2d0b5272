<?php

namespace tests\models\Controllers\LMRequest;

use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\HUD;
use PHPUnit\Framework\TestCase;

/**
 * Tests the HUDController class.
 *
 * Auto-generated docblock. Please refine descriptions as needed.
 */
class HUDControllerTest extends TestCase
{
    /**
     * Tests the getUnderwritingFeeHUD method of the HUDController class.
     *
     * Auto-generated docblock. Please update with test specifics.
     */
    public function testGetUnderwritingFeeHUD(): void
    {
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Tests the getProcessingFeeHUD method of the HUDController class.
     *
     * Auto-generated docblock. Please update with test specifics.
     */
    public function testGetProcessingFeeHUD(): void
    {
        $this->markTestIncomplete('Not implemented');
    }
}
