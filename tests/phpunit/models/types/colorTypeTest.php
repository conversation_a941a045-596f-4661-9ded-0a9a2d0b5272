<?php

namespace tests\models\types;

use models\types\colorType;
use PHPUnit\Framework\TestCase;

/**
 * Tests the colorType class.
 *
 * Auto-generated docblock. Please refine descriptions as needed.
 *
 * @covers \models\types\colorType::addItem
 * @covers \models\types\colorType::getColor
 */
class colorTypeTest extends TestCase
{
  private colorType $colorType;

  protected function setUp(): void
  {
    $this->colorType = new colorType();
  }

  /**
   * Tests the addItem method of the colorType class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testAddItem(): void
  {
    $this->colorType->addItem('item1');
    $this->colorType->addItem('item2');
    $this->colorType->addItem('item3');

    $this->assertEquals('#3366cc', $this->colorType->getColor('item1'));
    $this->assertEquals('#dc3912', $this->colorType->getColor('item2'));
    $this->assertEquals('#ff9900', $this->colorType->getColor('item3'));
  }

  /**
   * Tests the getColor method of the colorType class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testGetColor(): void
  {
    $this->colorType->addItem('item1');
    $this->colorType->addItem('item2');
    $this->colorType->addItem('item3');

    $this->assertEquals('#3366cc', $this->colorType->getColor('item1'));
    $this->assertEquals('#dc3912', $this->colorType->getColor('item2'));
    $this->assertEquals('#ff9900', $this->colorType->getColor('item3'));
  }
}
