<?php

namespace tests;

use models\pkgUtilFunctions;
use PHPUnit\Framework\TestCase;

/**
 * Tests the docNameToPackageName method of the pkgUtilFunctions class.
 *
 * Auto-generated docblock. Please refine descriptions as needed.
 *
 * @covers \models\pkgUtilFunctions::docNameToPackageName
 */
class pkgUtilFunctionsTest extends TestCase
{
  /**
   * Tests the docNameToPackageName method of the pkgUtilFunctions class.
   *
   * Auto-generated docblock. Please update with test specifics.
   */
  public function testDocNameToPackageName()
  {
    $this->assertEquals('PackageACHFormSBA.php', pkgUtilFunctions::docNameToPackageName('ACHFormSBA.php'));
    $this->assertEquals('PackageBLMShortSalePackage2013.php', pkgUtilFunctions::docNameToPackageName('BLMShortSalePackage_2013.php'));
    $this->assertEquals('PackageBalloonPaymentDisclosure.php', pkgUtilFunctions::docNameToPackageName('Balloon_Payment_Disclosure.php'));
    $this->assertEquals('PackageCommercialLoanApplication.php', pkgUtilFunctions::docNameToPackageName('velocityCommercialLoanApp.php'));
    $this->assertEquals('PackageCreateMissingDocDemandLetter.php', pkgUtilFunctions::docNameToPackageName('createMissingDocDemandLetterPkg.php'));
    $this->assertEquals('PackageDIYPurchaseAgreementmader.php', pkgUtilFunctions::docNameToPackageName('DIYPurchaseAgreement_maderPdf.php'));
    $this->assertEquals('PackageDIYPurchaseAgreementmadercob.php', pkgUtilFunctions::docNameToPackageName('DIYPurchaseAgreement_mader_cobPdf.php'));
    $this->assertEquals('PackageEqualCreditOpportunityAct.php', pkgUtilFunctions::docNameToPackageName('ecoa.php'));
    $this->assertEquals('PackageCreateFaxCoverSheet.php', pkgUtilFunctions::docNameToPackageName('createFaxCoverSheet.php'));
    $this->assertEquals('PackageForm4506C2021.php', pkgUtilFunctions::docNameToPackageName('4506C_2021.php'));
    $this->assertEquals('PackageMCAApplication.php', pkgUtilFunctions::docNameToPackageName('appMCA.php'));
    $this->assertEquals('PackageMaderEMAAgreement.php', pkgUtilFunctions::docNameToPackageName('Mader_EMA_agreement.php'));
    $this->assertEquals('PackageMiddaghRetainer.php', pkgUtilFunctions::docNameToPackageName('MiddaghRetainerPdf.php'));
    $this->assertEquals('PackageMoneyAvenueFeeAgreement.php', pkgUtilFunctions::docNameToPackageName('moneyAvenueFeeAgreement_0003.php'));
    $this->assertEquals('PackageSeterus2015.php', pkgUtilFunctions::docNameToPackageName('Seterus-2015.php'));
    $this->assertEquals('PackageAgencyDisclosure.php', pkgUtilFunctions::docNameToPackageName('agencyDisclosurePkg.php'));
    $this->assertEquals('PackageBabbsRetainer.php', pkgUtilFunctions::docNameToPackageName('ThemisBabbs.php'));
    $this->assertEquals('PackageCooperatingBroker1.php', pkgUtilFunctions::docNameToPackageName('mssCooperatingBroker1.php'));
    $this->assertEquals('PackageCreditCheckAuthorizationGreatOak.php', pkgUtilFunctions::docNameToPackageName('CreditCheck_GreatOak.php'));
    $this->assertEquals('PackageDoddFrank.php', pkgUtilFunctions::docNameToPackageName('doddFrankPdf.php'));
    $this->assertEquals('PackageDynamicQuestionsExample.php', pkgUtilFunctions::docNameToPackageName('_dynamicQuestionsExample.php'));
    $this->assertEquals('PackageFciACHform.php', pkgUtilFunctions::docNameToPackageName('fciACH.php'));
    $this->assertEquals('PackageFirstAmericanMitigators.php', pkgUtilFunctions::docNameToPackageName('firstAmericanMitigatorsPdf.php'));
    $this->assertEquals('PackageLoanAppw9.php', pkgUtilFunctions::docNameToPackageName('Form_w9.php'));
    $this->assertEquals('PackageMasterWorkUp.php', pkgUtilFunctions::docNameToPackageName('masterWorkUp_HomeWorx.php'));
    $this->assertEquals('PackageMssCollectionChecklist.php', pkgUtilFunctions::docNameToPackageName('mssCollectionChecklistPdf.php'));
    $this->assertEquals('PackageMssReferralAgreement.php', pkgUtilFunctions::docNameToPackageName('mssReferralAgreementPdf.php'));
    $this->assertEquals('PackageMssThirdPartyAuth.php', pkgUtilFunctions::docNameToPackageName('mssThirdPartyAuthPdf.php'));
    $this->assertEquals('PackagePrintrpackage.php', pkgUtilFunctions::docNameToPackageName('_print_r_package.php'));
    $this->assertEquals('PackageReferences.php', pkgUtilFunctions::docNameToPackageName('createReferences.php'));
  }
}
