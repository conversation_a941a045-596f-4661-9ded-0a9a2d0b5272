/**
 * Common Draw Request Functions
 * Shared functionality between borrower and lender draw request forms
 */

// Namespace for draw request utilities
window.DrawRequestUtils = window.DrawRequestUtils || {};

/**
 * Calculate amount from percentage input
 * @param {jQuery} $percentInput - The percentage input element
 * @returns {number} - Calculated amount
 */
DrawRequestUtils.calculateAmountFromPercent = function($percentInput) {
    const percent = parseFloat($percentInput.val()) || 0;
    const cost = parseFloat($percentInput.data('cost')) || 0;
    const requestedAmount = (percent / 100) * cost;

    return Math.max(0, requestedAmount);
};

/**
 * Calculate percentage from amount input
 * @param {jQuery} $amountInput - The amount input element
 * @returns {number} - Calculated percentage
 */
DrawRequestUtils.calculatePercentFromAmount = function($amountInput) {
    const amount = parseFloat($amountInput.val()) || 0;
    const cost = parseFloat($amountInput.data('cost')) || 0;

    const percent = (amount / cost) * 100;
    return Math.max(0, Math.min(100, percent));
};

/**
 * Validate percentage input field
 * @param {jQuery} $percentInput - The percentage input element
 * @returns {boolean} - Whether the input is valid
 */
DrawRequestUtils.validatePercentInput = function($percentInput) {
    const percent = parseFloat($percentInput.val()) || 0;
    const completedPercent = parseFloat($percentInput.data('completed-percent')) || 0;
    const maxPercent = 100 - completedPercent;

    const $validationMsg = $percentInput.closest('td').find('.validation-message');

    if (percent < 0) {
        $validationMsg.text('Percentage cannot be negative').show();
        $percentInput.addClass('is-invalid');
        return false;
    } else if (percent > maxPercent) {
        $validationMsg.text(`Percentage cannot exceed ${maxPercent.toFixed(2)}% (100% - ${completedPercent.toFixed(2)}% completed)`).show();
        $percentInput.addClass('is-invalid');
        return false;
    } else {
        $validationMsg.hide();
        $percentInput.removeClass('is-invalid');
        return true;
    }
};

/**
 * Validate amount input field
 * @param {jQuery} $amountInput - The amount input element
 * @returns {boolean} - Whether the input is valid
 */
DrawRequestUtils.validateAmountInput = function($amountInput) {
    const amount = parseFloat($amountInput.val()) || 0;
    const cost = parseFloat($amountInput.data('cost')) || 0;
    const completedAmount = parseFloat($amountInput.data('completed-amount')) || 0;
    const maxAmount = cost - completedAmount;

    const $validationMsg = $amountInput.closest('td').find('.validation-message');

    if (amount < 0) {
        $validationMsg.text('Amount cannot be negative').show();
        $amountInput.addClass('is-invalid');
        return false;
    } else if (amount > maxAmount) {
        $validationMsg.text(`Amount cannot exceed $${maxAmount.toFixed(2)} (Total Budget - Completed Renovations)`).show();
        $amountInput.addClass('is-invalid');
        return false;
    } else {
        $validationMsg.hide();
        $amountInput.removeClass('is-invalid');
        return true;
    }
};

/**
 * Get color for percentage display based on completion percentage
 * @param {number} percentage - The percentage value (0-100)
 * @returns {string} - HSL color string
 */
DrawRequestUtils.getPercentageColor = function(percentage) {
    const p = Math.max(0, Math.min(100, percentage));
    const saturation = 100;
    const lightness = 45;
    const hue = (p / 100) * 120;

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
};

/**
 * Initialize percentage color styling
 * Applies color styling to all elements with .percentage class
 */
DrawRequestUtils.initializePercentageColors = function() {
    $('.percentage').each(function() {
        const percentage = parseInt($(this).text());
        $(this).css('background', DrawRequestUtils.getPercentageColor(percentage));
    });
};

/**
 * Setup common input event handlers for percentage and amount fields
 * @param {function} additionalValidationCallback - Optional callback for additional validation
 */
DrawRequestUtils.setupInputHandlers = function(additionalValidationCallback) {
    // Handle percentage input changes
    $('.requested-percent').on('input', function() {
        const $percentInput = $(this);
        const lineItemId = $percentInput.data('line-item-id');
        const $amountInput = $(`.requested-amount[data-line-item-id="${lineItemId}"]`);

        if (DrawRequestUtils.validatePercentInput($percentInput)) {
            const calculatedAmount = DrawRequestUtils.calculateAmountFromPercent($percentInput);
            $amountInput.val(calculatedAmount.toFixed(2));
            DrawRequestUtils.validateAmountInput($amountInput);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });

    // Handle amount input changes
    $('.requested-amount').on('input', function() {
        const $amountInput = $(this);
        const lineItemId = $amountInput.data('line-item-id');
        const $percentInput = $(`.requested-percent[data-line-item-id="${lineItemId}"]`);

        if (DrawRequestUtils.validateAmountInput($amountInput)) {
            const calculatedPercent = DrawRequestUtils.calculatePercentFromAmount($amountInput);
            $percentInput.val(calculatedPercent.toFixed(2));
            DrawRequestUtils.validatePercentInput($percentInput);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });

    // Handle blur events for final validation
    $('.requested-percent, .requested-amount').on('blur', function() {
        const $input = $(this);
        if ($input.hasClass('requested-percent')) {
            DrawRequestUtils.validatePercentInput($input);
        } else {
            DrawRequestUtils.validateAmountInput($input);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });
};

/**
 * Validate all percentage and amount inputs
 * @returns {boolean} - Whether all inputs are valid
 */
DrawRequestUtils.validateAllInputs = function() {
    let isValid = true;

    $('.requested-percent').each(function() {
        if (!DrawRequestUtils.validatePercentInput($(this))) {
            isValid = false;
        }
    });

    $('.requested-amount').each(function() {
        if (!DrawRequestUtils.validateAmountInput($(this))) {
            isValid = false;
        }
    });

    return isValid;
};

/**
 * Check if at least one line item field has a non-zero value
 * @returns {boolean} - Whether at least one field has a non-zero value
 */
DrawRequestUtils.hasNonZeroValues = function() {
    let hasNonZeroValue = false;

    $('.requested-percent, .requested-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        if (value > 0) {
            hasNonZeroValue = true;
            return false; // Break out of the loop
        }
    });

    return hasNonZeroValue;
};

/**
 * Check if there are any validation errors
 * @returns {boolean} - Whether there are validation errors
 */
DrawRequestUtils.hasValidationErrors = function() {
    return $('.is-invalid').length > 0;
};

$(document).ready(function() {
    DrawRequestUtils.initializePercentageColors();
});
