<?php

use models\composite\oBranch\getBranchAndAgentRequiredDocs;
use models\composite\oBranch\getBranchHearAbout;
use models\composite\oBranch\getBranchModules;
use models\composite\oBranch\getBranchServices;
use models\composite\oBranch\getReferralSiteInfo;
use models\composite\oBranch\isBranchActive;
use models\composite\oBranch\savePreferredAgentForBranch;
use models\composite\oBroker\getAgentReferralSiteName;
use models\composite\oBroker\getBrokerInfo;
use models\composite\oBroker\isAgentActive;
use models\composite\oBroker\isPCAgentActive;
use models\composite\oChecklist\getChecklistForPCList;
use models\composite\oFile\getBorrowerNumberOfDeals;
use models\composite\oFile\getFileCountForLP;
use models\composite\oFile\getFileInfo;
use models\composite\oFile\getFilter;
use models\composite\oFile\getMyFileInfo;
use models\composite\oPC\getAppFormFields;
use models\composite\oPC\getPermissionsToEdit;
use models\composite\oPC\PCInfo;
use models\composite\oServiceType\getServiceTypes;
use models\constants\disabledLP;
use models\constants\gl\glHMLOTAC;
use models\constants\gl\glUserRole;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;
use models\CustomField;
use models\Database2;
use models\JSCompiler;
use models\lendingwise\tblBrokerAssociatedLoanOfficers;
use models\cypher;
use models\oMicroBuiltAPI;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Strings;

if ($_SERVER['QUERY_STRING'] == 'bRc=6b2ee240df37be75&fOpt=8e614f58c0d670e4&op=aa4465703ef4b17e') {
    header('Location: https://app.lendingwise.com/HMLOWebForm.php?bRc=689e137e8f70fecd&aRc=6b2ee240df37be75&fOpt=c1cca7825dbc8710&op=aa4465703ef4b17e');
    exit;
//emergency quick fix for sunset equity
}
session_start();
unset($_SESSION['redirectUrl']);
if (count($_REQUEST) > 0) {
    foreach ($_REQUEST as $requestKey => $requestVal) {
        if (preg_match('/[\')]/', $requestVal)) {
            echo '<h3>Incorrect URL Parameters</h3>';
            exit;
        }
    }
}
require 'includes/util.php';

global $fileCT, $fileTab, $fileCLP, $glLMRId, $fileLP, $file_id, $fileLMRInternalLoanprograms;
global $encLMRId, $encrypCID;

$glHMLOTAC = glHMLOTAC::$glHMLOTAC;


// getFileInfo::$skipNotes = true; // don't load notes, they're not used on this page


if (isset($_REQUEST['bRc'])) {
    if ($_REQUEST['bRc']) {
        if (!is_numeric(cypher::myDecryption(Request::GetClean('bRc')))) {
            echo '<h3>Incorrect URL Parameters</h3>';
            exit;
        }
    }
}
if (isset($_REQUEST['lid'])) {
    if ($_REQUEST['lid']) {
        if (!is_numeric(cypher::myDecryption(Request::GetClean('lid')))) {
            echo '<h3>Incorrect URL Parameters</h3>';
            exit;
        }
    }
}

JSCompiler::initDefaultModules();
JSCompiler::addJS('/assets/js/models/CustomField.js');

$checkReferralCode = 1;
$LMRAEUserType = '';
$incomeInfo = $fileLP = [];
$allowToEdit = 1;
$referralSiteCode = 1;
$LMRInfoArray = [];
$subscribedOption = 1;
$LMRId = 0;
$showPoweredByTMPLink = 1;
$PCStatus = 0;
$branchReferralCode = 1;
$agentReferralCode = 1;
$agentInfoArray = [];
$fOpt = '';
$tabOpt = 'BI';
$responseId = 0;
$googleTrackingCode = '';
$googleTrackingCodeQA = '';
$allowToAddAgent = 0;
$processingCompanyId = 0;
$externalBroker = 0;
$processingCompanyInfoArray = [];
$userRole = '';
$stateArray = [];
$servicer1 = '';
$servicer2 = '';
$docArray = [];
$servicer1Text = $originalLender1Text = $servicer2Text = $originalLender2Text = '- Type Name Here -';
$PCStatusInfo = [];
$ResponseInfo = [];
$myFileInfo = [];
$webformName = '';
$statusInfoArray = [];
$fileInfo = [];
$primeStatusId = 0;
$isHMLO = 1;
$isLOC = 1;
$addBranchHearAbout = 0;
$publicUser = 1;
$showLimitedMandatoryField = 1;
$fileUpdLimit = 5;
$tempDocInfoArray = [];
$activeTab = 'LI';
$mandatoryChecklistItemsArray = $fileLMRAdditionalLoanprograms = [];
$borrowerName = '';
$LMRInfo = [];

$brokerNumber = 0;
$mailSent = 0;
$lId = 0;
$siteName = '';
$tempSiteName = '';
$executiveId = 0;
$sdOpt = 0;
$agentId = 0;
$modifyOpt = 0;
$tabNumb = 0;
$MLMRAllow = false;
$editOpt = 0;
$LMRResponseId = 0;
$qstrOpt = 0;
$LMRPerson = '';
$exOpt = 0;
$agentFName = '';
$agentLName = '';
$agentName = '';
$borrowerNumberOfDeals = [];
$agentNumber = 0;
$loanOfficerId = 0;
$branchName = '';
$hardshipType = '';
$originalPurchasePrice = 0;
$clientId = 0;
$isEF = 0;
$allowClientToCreateHMLOFile = '';
$branchStatus = 0;
$agentStatus = 0;
$agentPCStatus = 0;
$exeStatus = 0;
$HMLOTAC = $HMLOTACQA = '';
$fileRecordDate = '';
$leadSource = '';
$userTimeZone = '';
$brokerFName = '';
$brokerLName = '';
$brokerCompany = '';
$brokerEmail = '';
$brokerPhone = '';
$brokerPhNo1 = '';
$brokerPhNo2 = '';
$brokerPhNo3 = '';
$brokerExt = '';
$formName = 'loanModForm';
$paymentTempFormName = 'loanModForm';
$showAgentList = 0;
$webFormFOpt = '';
$checklistInfoArray = [];
$PCChecklistInfoArray = [];
$clientType = '';

$stateArray = Arrays::fetchStates();
/** Fetch all States **/
$branchHearAboutInfoArray = [];
$branchHAInfoArray = [];
$inputExeArray = [];
$LMRClientTypeInfo = [];
$ClientType = '';
$isMF = '';
$emailOpt = '';
$fileHMLOChecklistUploadDocs = [];
$REBroker = '';
$RELoanofficer = '';
$BrokerInfoDivDisp = 'display: none;';
$isBrokerExistsDisp = 'display: none;';
$fileModuleInfo = [];
$cliType = '';
$brancArray = [];
$defaultPrimaryStatus = 0;
$oldFPCID = '';
$wfOpt = '';
$PCID = null;
$userGroup = 'Client';
$processorAssignedCompany = [];
$selClientId = '';
$isClientProfile = 0;
$hideBorrowerInfo = 0;
$theme = $tempClientEmail = '';
$defaultPrimaryStatusForFA = 0;
$registerDate = '';
$ft = 'HMLO';
$userNumber = 0;
$URLPOSTING = 0;

$isPLO = 0;
$selClient = '';
$allowCaptcha = 0;
$dummyBrokerId = 0;
$SecondaryBrokerInfo = [];
$secondaryBrokerNumber = 0;
$isCoBorrower = 0;

if (isset($_REQUEST['bRc'])) {
    $branchReferralCode = cypher::myDecryption(Request::GetClean('bRc'));
}

if ($branchReferralCode == '') {
    $branchReferralCode = 1;
}

if (isset($_REQUEST['aRc'])) {
    $agentReferralCode = cypher::myDecryption(Request::GetClean('aRc'));
}

if ($agentReferralCode == '') {
    $agentReferralCode = 1;
}

if (isset($_REQUEST['fOpt'])) {
    $webFormFOpt = $fOpt = cypher::myDecryption(Request::GetClean('fOpt'));
}

if (isset($_REQUEST['opt'])) {
    $emailOpt = cypher::myDecryption(Request::GetClean('opt'));
}

if (isset($_REQUEST['op'])) {
    $wfOpt = cypher::myDecryption(Request::GetClean('op'));
}

if (isset($_REQUEST['ft'])) {
    $ft = Request::GetClean('ft');
    $ft = str_replace('\%27A=0', '', $ft);
    $ft = str_replace("\'A=0", '', $ft);
    $ft = preg_replace('/[^A-Za-z0-9]/', '', trim($ft));
}

if (strpos($wfOpt, 'QA') !== false) {
    $wfOpt = 'QA';
} else if (strpos($wfOpt, 'FA') !== false) {
    $wfOpt = 'FA';
} else {
    $wfOpt = 'QA';
}

if ($userRole == glUserRole::USER_ROLE_AGENT) {
    if (isset($_REQUEST['aRc'])) $checkReferralCode = Request::GetClean('aRc');
    header('Location: alertUser.php');
    exit();
}
if (isset($_REQUEST['lid'])) {
    $lId = cypher::myDecryption(Request::GetClean('lid'));
}
if (isset($_REQUEST['tabOpt'])) {
    $tabOpt = Request::GetClean('tabOpt');
} else if (isset($_SESSION['tabOpt'])) {
    $tabOpt = trim($_SESSION['tabOpt']);
}
if (!$tabOpt) $tabOpt = 'BI';

if ($wfOpt == 'FA') {
    $titleTxt = 'Full Application Form';
    $myOpt = 'FA';
    $reqDoc = 'LV';
    $webFormType = 'LV';
    $activeTab = 'LI';
} else {
    $titleTxt = 'Quick Application Form';
    $myOpt = 'QA';
    $reqDoc = 'SV';
    $webFormType = 'SV';
    $activeTab = 'QAPP';
}

if ($tabOpt == '1003') {
    $titleTxt = 'Uniform Residential Loan Application';
    $activeTab = '1003';
    $wfOpt = 'BO';
}
$shareLink = '';
$borInfoRed = 'no';
$hideThisField = 1; //default show
$UType = '';
$aud = 'no';
$so = 'no';//Access to Uploaded Docs

if (isset($_REQUEST['sl'])) $shareLink = cypher::myDecryption(Request::GetClean('sl'));
if (isset($_REQUEST['bir'])) $borInfoRed = cypher::myDecryption(Request::GetClean('bir'));
if (isset($_REQUEST['aud'])) $aud = cypher::myDecryption(Request::GetClean('aud'));
if (isset($_REQUEST['UType'])) $UType = cypher::myDecryption(Request::GetClean('UType'));
if (isset($_REQUEST['so'])) $so = cypher::myDecryption(Request::GetClean('so'));

Property::init($lId);
LMRequest::setLMRId($lId);
if(!$lId){
    CustomField::$isJSManaged = true; // make sure all fields are rendered and JS will handle visible / invisible
}

if ($lId > 0) {


    getFileInfo::$publicUser = $publicUser;
    $ip = ['LMRId' => $lId];
    $fileInfo = getFileInfo::getReport($ip);


    if (count($fileInfo) > 0) {
        if (array_key_exists($lId, $fileInfo)) {
            $myFileInfo = $fileInfo[$lId];
            if ($myFileInfo['LMRInfo']['activeStatus'] == 0) {
                $_SESSION['errorMsg'] = 'This file has been deactivated';
                header('Location: invalidFile.php');
                exit;
            }
        }
        $LMRId = $lId;
    }
}

$glLMRId = $LMRId;
$file_id = $LMRId;

if ($lId > 0) {
    $getMyFileInfo = getMyFileInfo::getReport($lId);
    $borrowerNumberOfDeals = getBorrowerNumberOfDeals::getReport(Arrays::getArrayValue('clientId', $getMyFileInfo[$lId]), Strings::showField('FPCID', 'LMRInfo'));
}


//if($userNumber == 0 || $LMRId > 0) {
if ($branchReferralCode == 1 || ($fOpt == 'agent' && $agentReferralCode == 1)) {
    header('location: backoffice/subscriptionExpired.php?check=24');
    exit();
} elseif (($branchReferralCode > 1) || ($agentReferralCode > 1)) {


    if (count($myFileInfo) > 0) {
        if (array_key_exists('BrokerInfo', $myFileInfo)) $BrokerInfo = $myFileInfo['BrokerInfo'];
        if (array_key_exists('SecondaryBrokerInfo', $myFileInfo)) $SecondaryBrokerInfo = $myFileInfo['SecondaryBrokerInfo'];
        /** Fetch agent info **/
        if (array_key_exists('ResponseInfo', $myFileInfo)) $ResponseInfo = $myFileInfo['ResponseInfo'];
        /** Fetch Response info **/
        if (array_key_exists('LMRInfo', $myFileInfo)) $LMRInfo = $myFileInfo['LMRInfo'];
        if (array_key_exists('LMRClientTypeInfo', $myFileInfo)) $LMRClientTypeInfo = $myFileInfo['LMRClientTypeInfo'];
        if (array_key_exists('fileModuleInfo', $myFileInfo)) $fileModuleInfo = $myFileInfo['fileModuleInfo'];
        if (array_key_exists('fileHMLOChecklistUploadDocsNew', $myFileInfo)) $fileHMLOChecklistUploadDocs = $myFileInfo['fileHMLOChecklistUploadDocsNew'];
        if (array_key_exists('LMRInternalLoanprograms', $myFileInfo)) $fileLMRInternalLoanprograms = $myFileInfo['LMRInternalLoanprograms'];
        if (array_key_exists('LMRadditionalLoanprograms', $myFileInfo)) $fileLMRAdditionalLoanprograms = $myFileInfo['LMRadditionalLoanprograms'];
        $fileLMRInternalLoanprograms = array_merge($fileLMRInternalLoanprograms, $fileLMRAdditionalLoanprograms);
        if (array_key_exists('filRentRollInfo', $myFileInfo)) $filRentRollInfo = $myFileInfo['filRentRollInfo'];
        if (count($myFileInfo['fileModuleInfo'])) {
            $ft = $myFileInfo['fileModuleInfo'][$LMRId][0]['moduleCode'] ?? '';
        }
        if (array_key_exists('SBABackground', $myFileInfo)) {
            $SBABackground = $myFileInfo['SBABackground'];
            if (count($SBABackground) > 0) {
                unset($SBABackground['SBAID']);
                unset($SBABackground['fileID']);
                unset($SBABackground['CID']);
                foreach ($SBABackground as $sbKey => $sbVal) {
                    ${$sbKey} = $sbVal;
                }
            }
        }

        if (array_key_exists($LMRId, $LMRClientTypeInfo)) $ClientType = $LMRClientTypeInfo[$LMRId][0]['ClientType'];
        $agentNumber = Strings::showField('userNumber', 'BrokerInfo');
        $LMRResponseId = Strings::showField('LMRResponseId', 'ResponseInfo');
        $borrowerName = ucwords(trim(Strings::showField('borrowerName', 'LMRInfo')) . ' ' . trim(Strings::showField('borrowerLName', 'LMRInfo')));
        $fileRecordDate = Strings::showField('recordDate', 'LMRInfo');
        $clientId = Strings::showField('clientId', 'LMRInfo');
        $leadSource = Strings::showField('leadSource', 'ResponseInfo');
        $LMRClientTypeInfo = $LMRClientTypeInfo[$LMRId];
        $REBroker = Strings::showField('REBroker', 'fileHMLOInfo');
        $PCID = Strings::showField('FPCID', 'LMRInfo');
        $registerDate = Strings::showField('registerDate', 'LMRInfo');
        /** Rehab construction Field hide and show **/
        if ($REBroker == 'Yes') {
            $BrokerInfoDivDisp = 'display: block;';
            $isBrokerExistsDisp = 'display: none;';
        } else {
            $BrokerInfoDivDisp = 'display: none;';
            $isBrokerExistsDisp = 'display: table-row;';
        }

        $inArray = [];
        $inArray['PCID'] = $PCID;
        $inArray['requiredBy'] = 'Borrower';
        $inArray['opt'] = 'reqBy';
        $inArray['moduleType'] = $ft;

        if ($PCID > 0) {
            $checklistInfoArray = getChecklistForPCList::getReport($inArray);
        }

        if (count($checklistInfoArray) > 0) $checklistInfoArray = $checklistInfoArray[$ft];

        for ($pChk = 0; $pChk < count($checklistInfoArray); $pChk++) {
            $tempArray1 = [];
            $tempArray2 = [];
            $chPos = '';
            $tempID = '';
            $tempID = trim($checklistInfoArray[$pChk]['MultiCID']);
            $MultiSType = '';
            $MultiSType = trim($checklistInfoArray[$pChk]['MultiSType']);

            if ($MultiSType != '') {
                $tempArray1 = explode(',', $MultiSType);
                $tempArray2 = explode(',', $tempID);
            }
            if (count($tempArray1) > 0) {
                if (in_array($ClientType, $tempArray1)) {
                    $chPos = array_search($ClientType, $tempArray1);
                    $PCChecklistInfoArray[$tempID] = $checklistInfoArray[$pChk];
                    $PCChecklistInfoArray[$tempID]['PCMID'] = $tempArray2[$chPos];
                }
            }
        }
    }
    $oldFPCID = $PCID;
    if ($branchReferralCode > 1) {
        $ip = ['branchReferralCode' => $branchReferralCode];
        $LMRInfoArray = getReferralSiteInfo::getReport($ip);
    }


    $lmrLogo = '';
    $tempArray = [];
    if ($branchReferralCode > 1) {
        for ($lr = 0; $lr < count($LMRInfoArray); $lr++) {
            $tempArray = [];
            $tempArray = $LMRInfoArray[$lr];
            $siteName = $tempArray['company'];
            $tempSiteName = $siteName;
            $executiveId = $tempArray['executiveId'];
            $subscribedOption = trim($tempArray['subscribedOption']);
            $LMRAEUserType = trim($tempArray['userType']);
            $showPoweredByTMPLink = trim($tempArray['showPoweredByTMPLink']);
            $googleTrackingCode = urldecode($tempArray['googleTrackingCode']);
            $googleTrackingCodeQA = urldecode($tempArray['googleTrackingCodeQA']);
            $allowToAddAgent = trim($tempArray['allowToAddAgent']);
            $processingCompanyId = trim($tempArray['processingCompanyId']);
            $branchName = trim($tempArray['LMRExecutive']);
            $addBranchHearAbout = trim($tempArray['addBranchHearAbout']);
            $HMLOTAC = trim($tempArray['TAC']);
            $HMLOTACQA = trim($tempArray['TACQA']);
            $lmrLogo = trim($tempArray['logo']);
            $userTimeZone = trim($tempArray['timeZone']);
            $PCID = $processingCompanyId;
            $assignedPCID = $processingCompanyId;
            $allowCaptcha = trim($tempArray['allowcaptcha']);
        }
        if (in_array($processingCompanyId, CONST_API_ACCESS_PC)) {

            $oMicro = new oMicroBuiltAPI([
                'MICROBILT_KEY'       => CONST_MICROBILT_KEY,
                'MICROBILT_SECRET'    => CONST_MICROBILT_SECRET,
                'MICROBILT_TOKEN_URL' => CONST_MICROBILT_TOKEN_URL,
            ]);

            $currentIP = ['IP' => $_SERVER['REMOTE_ADDR']];
            $url = CONST_MICROBILT_IPVERIFY_API;

            $encodedData = json_encode($currentIP);
            $param = ['url' => $url, 'data' => $encodedData];

            $response = $oMicro->postData($param);
            $loadingCountry = (json_decode($response)->IpInfo->Country);

            if ($loadingCountry != 'usa' && $_ENV['APP_ENV'] == 'production') {
                echo 'Unauthorised Access Location';
                exit;
            }
        }
        if (!$HMLOTAC || !$HMLOTACQA) {
            $HMLOTAC = $glHMLOTAC;
            $HMLOTACQA = $glHMLOTAC;
        }

        if ($processingCompanyId > 0) {
            $ipArray = ['PCID' => $processingCompanyId];
            $processingCompanyInfoArray = PCInfo::getReport($ipArray);
        }
        if (!$processingCompanyInfoArray['captcha']) {
            $allowCaptcha = 0;
        }

        $hideBorrowerInfo = $processingCompanyInfoArray['hideBorrowerInfo']; //echo $REBroker;exit();
        if ($REBroker == 'Yes' && $hideBorrowerInfo == 1) {
            $hideBorrowerInfo = 1;
        }

        if (count($LMRInfoArray) > 0) {
            $branchStatus = isBranchActive::getReport(['executiveId' => $executiveId]);
        } else {
            $subscribedOption = 0;
        }
    }

    if (($subscribedOption == 0) || ($branchStatus == 0)) {
        header('location: backoffice/subscriptionExpired.php?check=25');
        exit();
    }
    $brokerLogo = '';
    if ($agentReferralCode > 1) {


        $agArray = ['agentReferralCode' => $agentReferralCode, 'PCID' => $processingCompanyId];
        $agentInfoArray = getAgentReferralSiteName::getReport($agArray);

        if (count($agentInfoArray) > 0) {
            $brokerLogo = $agentInfoArray['logo'];
            if ($agentInfoArray['externalBroker'] == 0) {
                $agentNumber = trim($agentInfoArray['userNumber']);
                $brokerNumber = $agentNumber;
                $agentFName = trim($agentInfoArray['firstName']);
                $agentLName = trim($agentInfoArray['lastName']);
                $userTimeZone = trim($tempArray['timeZone']);
                //$HMLOTAC        = trim($agentInfoArray['TAC']);   /** Broker should follow the branch TAC setting -- card #219  **/
                $brokerAssociateLOArray = [];
                $brokerAssociateLO = [];
                if ($agentNumber) {
                    $brokerAssociateLO = tblBrokerAssociatedLoanOfficers::getData($agentNumber);
                    if (count($brokerAssociateLO)) {
                        $brokerAssociateLOArray = array_column($brokerAssociateLO, 'loanOfficerId');
                    }
                }
                if (count($brokerAssociateLOArray)) {
                    $loanOfficerId = $brokerAssociateLOArray[0];
                }
                $agentStatus = isAgentActive::getReport(['agentId' => $agentNumber]);
                $agentPCStatus = isPCAgentActive::getReport(['agentId' => $agentNumber]);
                if ($fOpt == 'agent') {
                    $exeStatus = isBranchActive::getReport(['executiveId' => $executiveId]);
                }


                if ($agentStatus == 0 && $processingCompanyId > 0) {
                    $agentStatus = savePreferredAgentForBranch::getReport(['agentId' => $agentNumber, 'branchId' => $executiveId, 'BPCID' => $processingCompanyId]);
                }

            } else {
                $loanOfficerId = trim($agentInfoArray['userNumber']);

                $agentStatus = isAgentActive::getReport(['agentId' => $loanOfficerId]);
                $agentPCStatus = isPCAgentActive::getReport(['agentId' => $loanOfficerId]);
                if ($fOpt == 'agent') {
                    $exeStatus = isBranchActive::getReport(['executiveId' => $executiveId]);
                }


                if ($agentStatus == 0 && $processingCompanyId > 0) {
                    $agentStatus = savePreferredAgentForBranch::getReport(['agentId' => $loanOfficerId, 'branchId' => $executiveId, 'BPCID' => $processingCompanyId]);
                }

            }


            if ($fOpt == 'agent') {
                if (($agentStatus == 0) || ($exeStatus == 0) || ($agentPCStatus == 0)) {
                    header('location: backoffice/subscriptionExpired.php?check=26');
                    exit();
                }
            } else {
                if ($agentStatus == 0) {
                    header('location: backoffice/subscriptionExpired.php?check=27');
                    exit();
                }
            }
        }
    }

    if (count($SecondaryBrokerInfo ?? []) > 0) {
        $loanOfficerId = trim($SecondaryBrokerInfo['userNumber']);
        $secondaryBrokerNumber = trim($SecondaryBrokerInfo['userNumber']);
        $agentStatus = isAgentActive::getReport(['agentId' => $loanOfficerId]);
        if ($fOpt == 'agent') {
            $exeStatus = isBranchActive::getReport(['executiveId' => $executiveId]);
        }


        if ($agentStatus == 0) {
            $agentStatus = savePreferredAgentForBranch::getReport(['agentId' => $loanOfficerId, 'branchId' => $executiveId, 'BPCID' => $processingCompanyId]);
        }


    }

    //}
}

/*
* Description   : Get the agent terms and condition desc
* Date          : Nov 15, 2017
* Developer     : Venky
 */
if (!$HMLOTAC || !$HMLOTACQA) {
    $HMLOTAC = $glHMLOTAC;
    $HMLOTACQA = $glHMLOTAC;
}


$fileCT = $ft;
$ipArray['PCID'] = $processingCompanyId;

$statusInfoArray = getFilter::getReport($ipArray);
if (count($statusInfoArray) > 0) $PCStatusInfo = $statusInfoArray['PCStatusInfo'];
if (count($PCStatusInfo) > 0) {
    if (array_key_exists($processingCompanyId, $PCStatusInfo)) $PCStatusInfo = $PCStatusInfo[$processingCompanyId];
}
if ($LMRId > 0) {
    $primeStatusId = Strings::showField('primeStatusId', 'ResponseInfo');
} else {
    for ($j = 0; $j < count($PCStatusInfo); $j++) {
        if (strtolower(trim($PCStatusInfo[$j]['primaryStatus'])) == 'lead' && (trim($PCStatusInfo[$j]['moduleCode']) == $ft)) {
            $primeStatusId = trim($PCStatusInfo[$j]['PSID']);
            break;
        }
    }
    if ($primeStatusId == 0) {
        for ($j = 0; $j < count($PCStatusInfo); $j++) {
            if (strtolower(trim($PCStatusInfo[$j]['displayOrder'])) == 1 && (trim($PCStatusInfo[$j]['moduleCode']) == $ft)) {
                $primeStatusId = trim($PCStatusInfo[$j]['PSID']);
                break;
            }
        }

    }
}
$PCInfoArray = [];
$PCInfoArray = PCInfo::getReport(['PCID' => $processingCompanyId]);
$userLogo = $PCInfoArray['procCompLogo'];
$isPLO = $PCInfoArray['isPLO'];
$allowNestedEntityMembers = $PCInfoArray['allowNestedEntityMembers'];
PageVariables::$allowNestedEntityMembers = $allowNestedEntityMembers;
PageVariables::$publicUser = $publicUser;
if (isset($_REQUEST['brokerNumber'])) {
    $brokerNumber = Request::GetClean('brokerNumber');
    if ($brokerNumber > 0) $qstrOpt = 1;
} elseif (isset($_SESSION['userNumber'])) {
    $brokerNumber = trim($_SESSION['userNumber']);
} elseif (isset($_SESSION['MLMRNo'])) {
    $brokerNumber = trim($_SESSION['MLMRNo']);
}

if (isset($_REQUEST['bRc'])) {
    $branchReferralCode = cypher::myDecryption(Request::GetClean('bRc'));
}
if (isset($_REQUEST['aRc'])) {
    $agentReferralCode = cypher::myDecryption(Request::GetClean('aRc'));
}

if (isset($_SESSION['LMRId'])) {
    $LMRId = trim($_SESSION['LMRId']);
} elseif (isset($_REQUEST['lId'])) {
    $LMRId = cypher::myDecryption(Request::GetClean('lId'));
}

$agentName = ucwords($agentFName . ' ' . $agentLName);

if ($executiveId > 0) {
    $inArray['LMRAEID'] = $executiveId;

    $branchHearAboutInfoArray = getBranchHearAbout::getReport($inArray);

    if (count($branchHearAboutInfoArray) > 0) {
        if (array_key_exists($executiveId, $branchHearAboutInfoArray)) {
            $branchHAInfoArray = $branchHearAboutInfoArray[$executiveId];
        }
    }
}

/*** Dummy Agent ID For PC 12058 **/
$dummyBrokerInfo = getBrokerInfo::getReport(['mail' => $PCID . '@dummyAgentemail.com']);
if (isset($dummyBrokerInfo['userNumber'])) {
    $dummyBrokerId = ($dummyBrokerInfo['userNumber']);
}
/***End of Dummy Agent ID For PC 12058 **/


/* Required Docs Start - https://www.pivotaltracker.com/story/show/********* */
$reqCnt = 0;
$apCo = '';
$_sty = $_mTy = '';
$fileMC = [];
if ($LMRId > 0) {

    /* Get Loan Programs */
    for ($lp = 0; $lp < count($LMRClientTypeInfo ?? []); $lp++) {
        $_sty .= $apCo . $LMRClientTypeInfo[$lp]['ClientType'];
        $apCo = ',';
    }

    /* Get File Type */
    $apCo = '';
    for ($mty = 0; $mty < count($fileModuleInfo[$LMRId]); $mty++) {
        $fileMC[] = $fileModuleInfo[$LMRId][$mty]['moduleCode'];
        $_mTy .= $apCo . $fileModuleInfo[$LMRId][$mty]['moduleCode'];
        $apCo = ',';
    }

    $mcArray = [
        '_pcID' => $processingCompanyId,
        '_mTy'  => $_mTy,
        '_sty'  => $_sty,
        '_as'   => 1,
        'wfTy'  => $reqDoc,
    ];

    if ($fOpt == 'agent' || $fOpt == 'branch' && $UType != 'CoBorrower') {
        //  if ($fOpt == 'agent') $mcArray['_brID'] = $brokerNumber;
        //if ($fOpt == 'branch') $mcArray['_exID'] = $executiveId; // story 26479 agent should follow branch required docs
        $mcArray['_exID'] = $executiveId;
        $displayedAndMandatoryItems = getBranchAndAgentRequiredDocs::getReport($mcArray);
        $reqCnt = count($displayedAndMandatoryItems);
    }
}
/* Required Docs End */

/* Custom Form Fields */
$myPCFieldsInfo = $myPCFieldsInfoArray = $PCFieldsInfo = [];
if (count($myPCFieldsInfo) > 0) {
    if (array_key_exists('PCFields', $myPCFieldsInfo)) $myPCFieldsInfoArray = $myPCFieldsInfo['PCFields'];
    for ($i = 0; $i < count($myPCFieldsInfoArray); $i++) {
        $fieldName = '';
        $fieldName = $myPCFieldsInfoArray[$i]['fieldName'];
        $PCFieldsInfo[$fieldName] = $myPCFieldsInfoArray[$i];
    }
    $mandatoryCls = ' class="mandatory" required="required" ';
}

/* Quick App Custom Form Fields */
echo BaseHTML::openPage($titleTxt);
if (isset($_REQUEST['view'])) {
    if (cypher::myDecryption(Request::GetClean('view')) == 'wizard') {
        $FSArray = ['/assetsNew/css/pages/wizard/wizard-2.css'];
        Strings::includeMyCSS($FSArray);
    }
}
?>
    <style>
        body, html {
            height: auto !important;
        }
    </style>
<?php
$FArray = [
    '/assets/js/fileCommon.js',
    '/assets/js/3rdParty/jquery-autocomplete/jquery.autocomplete_min.js',
    '/assets/js/3rdParty/jquery-chosen-0.9.8/chosen.jquery.js',
    '/assets/js/HMLOLoanInfo.js',
    '/assets/js/loanCalculation.js',
    '/assets/js/LMRequest.js',
    '/assets/js/QAForm.js',
    '/assets/js/incExp.js',
    '/assets/js/fileDocs.js',
    '/backoffice/api_v2/js/address_lookup.js',
    '/assets/js/3rdParty/esignSignature/jSignature.js',
    '/assets/js/3rdParty/esignSignature/plugins/jSignature.CompressorBase30.js',
    '/assets/js/3rdParty/esignSignature/plugins/jSignature.CompressorSVG.js',
    '/assets/js/3rdParty/esignSignature/plugins/jSignature.UndoButton.js',
    '/assets/js/3rdParty/esignSignature/plugins/signhere/jSignature.SignHere.js',
];
Strings::includeMyScript($FArray);
echo JSCompiler::scripts();

$myPCquickAppFieldsInfo = $myPCquickAppFieldsInfoArray = $PCquickAppFieldsInfo = $branchModules = [];

echo BaseHTML::thirdPartyFileManager();

$FSArray = [
    '/assets/styles/autocomplete.css',
    '/assets/styles/multi_select.css',
];
if ($theme == 2) {
    $FSArray[] = '/assets/styles/theme2.css';
}


$stateArray = Arrays::fetchStates();
$allStatesArray = [];
$allStatesArray = $stateArray;
/** Fetch all States **/

$ipArray = [
    'PCID'       => $processingCompanyId,
    'keyNeeded'  => 'n',
    'branchID'   => $executiveId,
    'moduleCode' => $ft
];
$servicesRequested = getBranchServices::getReport($ipArray);
$branchModules = getBranchModules::getReport(['branchID' => $executiveId]);


if (array_key_exists($executiveId, $servicesRequested)) $servicesRequested = $servicesRequested[$executiveId];
$glLMRClientTypeArray = getServiceTypes::getReport(['activeStatus' => '1']);
$aCm = $fileCLP = '';
foreach ($servicesRequested as $ct => $item) {
    if (in_array($item['serviceType'], disabledLP::$disabledLP)) {
        if ($processingCompanyId == 0) {
            unset($servicesRequested[$ct]);
            continue;
        } else {
            $fileCountArr = getFileCountForLP::getReport(['PCID' => $processingCompanyId,
                                                          'serviveName' => $item['serviceType']]);
            if ($fileCountArr[0]['fileCount'] == 0) {
                unset($servicesRequested[$ct]);
                continue;
            }
        }
    }
    $servicesRequested = array_values($servicesRequested);
    $fileCLP .= $aCm . trim($item['LMRClientType']);
    $aCm = ',';
}
/*get file level loan program*/

foreach ($LMRClientTypeInfo as $ct => $item) {
    $fileLP[] = trim($item['ClientType']);
}

if (count($fileLP) < 0 && $LMRId > 0) {
    $fileLP[0] = 'TBD';
} else if ($fileLP[0] == '' && $LMRId > 0) {
    $fileLP[0] = 'TBD';
}

/**
 * Dynamic Fields.
 */
$fieldsInfo = $fileTypeArray = [];

$fileTypeArray = $branchModules[$executiveId];
if ($LMRId > 0) {
    $fileTypeArray = $fileModuleInfo[$LMRId];
}
$fileTab = $wfOpt;

$fieldsInfo = getAppFormFields::getReport([
    'assignedPCID' => $PCID,
    'fTArray'      => $fileTypeArray,
    'myOpt'        => $wfOpt,
    'fileType'     => $ft,
    'activeTab'    => $activeTab
]);

PageVariables::setPCID($PCID);
LMRequest::$PCID = $PCID;
LMRequest::$activeTab = $activeTab;
loanForm::init(
    $PCID,
    $fileTab,
    Request::GetClean('lId') ?? null,
    $LMRId,
    $LMRId ? $fileMC : null,
    getFileInfo::$fileLoanPrograms,
    getFileInfo::$fileInternalLoanPrograms,
    getFileInfo::$fileAdditionalLoanPrograms,
    $fieldsInfo,
    1
);


$hsFADisplay = array_key_exists('HS', $fieldsInfo) ? $fieldsInfo['HS']['isHardshipApplies']['FADisplay'] : 0;
$hsQADisplay = array_key_exists('HS', $fieldsInfo) ? $fieldsInfo['HS']['isHardshipApplies']['QADisplay'] : 0;
$isEsignRequired = ($fileTab == 'FA') ? $fieldsInfo['TAC']['isEsignRequired']['FADisplay'] : (($fileTab == 'QA' ? $fieldsInfo['TAC']['isEsignRequired']['QADisplay'] : 0));
$tacField = array_key_exists('TAC', $fieldsInfo) ? $fieldsInfo['TAC']['agreeTC']['FADisplay'] : 0;
$FSArray = ['/assets/styles/multi_select.css'];
Strings::includeMyCSS($FSArray);
?>
    <style>
        .card.card-custom {
            margin-bottom: 4px !important;
        }
    </style>
    <body class="p-0 m-0">
    <script>
        KTApp.blockPage({
            opacity: 0.6,
            message: 'Please wait...',
        });

        class HMLOWebFormController {
            static updateCustomFields()
            {
                public_web_form_file_loan_programs = [];
                public_web_form_file_loan_programs.push($('#LMRClientType').val());

                for (let a of $('#LMRadditionalLoanProgram').val()) {
                    public_web_form_file_loan_programs.push(a);
                }

                console.log({
                    public_web_form_file_loan_programs: public_web_form_file_loan_programs,
                });

                CustomField.showHide(
                    public_web_form_file_loan_programs,
                    public_web_form_file_type
                );
            }
        }
        let public_web_form_file_loan_programs = <?php echo json_encode($fileLP); ?>;
        let public_web_form_file_type = ["<?php echo $ft; ?>"];
        $(function () {
            address_lookup.PCID = "<?php echo cypher::myEncryption($PCID); ?>";

            CustomField.showHide(
                public_web_form_file_loan_programs,
                public_web_form_file_type
            );


            $('#LMRClientType').on('change', function () {
                HMLOWebFormController.updateCustomFields();
            });

            $('#LMRadditionalLoanProgram').on('change', function () {
                HMLOWebFormController.updateCustomFields();
            });
        });
    </script>
    <div class="d-flex flex-column flex-root bg-white" id="mainDiv">
        <?php

        /**
         * Full app completion link form, editable if the loan status= Not Editable based on the PC settings--> File Status
         * For example, if the loan is in "Processing" and PC setting--> File Status--> Processing set for back office can only edit the file. then borrower can not edit the full app webform. all the data will be in read only.
         * https://app.clubhouse.io/lendingwise/story/1462/full-app-webform-needs-button-for-save-finish-later
         */
        if (($LMRId > 0 && $wfOpt == 'FA') || ($LMRId > 0 && $wfOpt == 'QA')) {
            // Get PC allowed file status.
            $permissionsToEditFile = getPermissionsToEdit::getReport([
                'PCID'      => $PCID,
                'userGroup' => 'Client',

                'op' => 'view',
            ]);

            if (isset($permissionsToEditFile[Strings::showField('primeStatusId', 'ResponseInfo')])) {
                $allowToEdit = 1;
            } else {
                $allowToEdit = 0;
            }

            //Share Link Read Only
            if ($shareLink == 'shareLink') {
                $allowToEdit = 0;
                $op = 'view';
            }
            //Share Link Read Only - Borrower Info Redacted
            if ($borInfoRed == 'yes') {
                $hideThisField = 0; //hide the field section
            }
            //Access to Uploaded Docs
            if ($aud == 'yes') {
                $aud = 1;
            }


        }
        if ($_GET['edit']) {
            if (!cypher::myDecryption($_GET['edit'])) {
                $allowToEdit = 0;
            }
        }

        $propertyTypeKeyArray = [];
        if (count(GpropertyTypeNumbArray::$GpropertyTypeNumbArray) > 0) $propertyTypeKeyArray = array_keys(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
        $GpropertyTypeKeyArray = [];
        $GpropertyTypeKeyArray = $propertyTypeKeyArray;
        /* PC loan terms rules - (Pivotal # : *********) */
        require 'backoffice/fileCustomPCLoanTerms.php';

        require 'backoffice/HMLOModuleForm.php';


        if (!$brokerNumber) {
            $brokerEmail = '';
            $brokerFName = '';
            $brokerLName = '';
            $brokerCompany = '';
            $brPhNo1 = '';
            $brPhNo2 = '';
            $brPhNo3 = '';
            $brExt = '';
            $bCellNo1 = '';
            $bCellNo2 = '';
            $bCellNo3 = '';
            $bFax1 = '';
            $bFax2 = '';
            $bFax3 = '';
            $bAddr = '';
            $bCity = '';
            $bZipCode = '';
            ?>
            <div id="userInfoDiv" style="z-index:300;">
            </div><!-- Broker Information Remote-->
        <?php } ?>

    </div>
    </body>
<?php echo BaseHTML::closePage();

Strings::includeMyScript([
    '/backoffice/LMRequest/js/formControl.js'
]);
if (isset($myFileInfo['LMRInfo']['activeStatus']) && Strings::showField('activeStatus', 'LMRInfo') == 0) { ?>
    <script>
        toastrNotification("This file has been deactivated ", 'error');
    </script>
<?php }

if ($wfOpt == 'QA') {
    $googleTrackingCode = $googleTrackingCodeQA;
}
require 'userGoogleCode.php';

if ($aud == 1 && $shareLink == 'shareLink') { ?>
    <script src="/assets/js/fileBinder.js?<?php echo CONST_JS_VERSION; ?>"></script>
<?php } ?>
    <script>
        $('#proInsFirstName').autocomplete({
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=18',
            minChars: 0,
            onSelect: function (value, data) {
                $('#proInsFirstName').val(replaceXMLProcess(value));
                document.loanModForm.insuranceCompanyID.value = data;
                document.loanModForm.insuranceCompanyName.value = value;
                showInsuranceCompanyForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
            }
        });

        $('#titleAttorneyName').autocomplete({
            serviceUrl: '/JQFiles/getContactListForFile.php?PCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>&CTypeID=3',
            minChars: 0,
            onSelect: function (value, data) {
                $('#titleAttorneyName').val(replaceXMLProcess(value));
                document.loanModForm.titleAttorneyID.value = data;
                document.loanModForm.titleAttorneyName.value = value;
                showTitleAttorneyForFile(data, '<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo'))?>');
            }
        });

        function validateFreshStartSubmissionDocs(formName) {
            var fileUpdLimit = 1, activateTab = '';

            for (var i = 1; i <= fileUpdLimit; i++) {
                var path = "";
                eval("path = document." + formName + ".fileSrc_" + i + ".value");
                var path_len = path.length;
                var file_length = path.lastIndexOf('\\');
                var fileName = path.substring(file_length + 1, path_len);
                if (path == "") {
                    alert("Select the document");
                    try {
                        eval("document." + formName + ".fileSrc_" + i + ".focus()");
                    } catch (e) {
                    }
                    eval("document.getElementById('fileSrc_" + i + "').className = 'highlights'");
                    return false;
                }
            }
            return true;
        }

        let LMRId = '';
        let LMRResponseId = 0;
        let executiveId = 0;
        let editOpt = 1;
        LMRId = '<?php echo cypher::myEncryption($LMRId)?>';
        LMRResponseId = '<?php echo cypher::myEncryption($LMRResponseId)?>';
        executiveId = '<?php echo cypher::myEncryption($executiveId)?>';
        let sslUrl = '<?php echo CONST_SITE_URL; ?>';
        //let siteUrl = '<?php echo CONST_SITE_URL; ?>';  //it is already declared in jsconfig
        let rootFile = '1';
        window.onload = function () {
            setTimeout("hideLoader()", 200);
        };

        let aud = "<?php echo htmlspecialchars($aud); ?>";
        let shareLink = "<?php echo htmlspecialchars($shareLink); ?>";
        let _submitStatus = $('#submitStatus');


        // Function to enable submit and cancel buttons
        function enableButtons() {
            $('#loanModForm input[type="submit"]').prop('disabled', false);
            $('.clBtn').prop('disabled', false);
        }

        // Function to show/hide signature sections
        function handleSignatureVisibility() {
            if (!(parseInt(aud) === 1 && shareLink === 'shareLink')) {
                $('.showEsignSignature').hide();
                $('.signatureDiv').show();
            }
            _submitStatus.val('0');
        }

        let _signatureparent = $("#signatureparent");
        let _signStatus = $("#signStatus");

        function resetSignature() {
            _signatureparent.jSignature('reset');
            _signStatus.val('');
        }

        let _signatureparentCoBorrower = $("#signatureparentCoBorrower");
        let _signStatusCoBorrower = $("#signStatusCoBorrower");

        function resetCoBorrowerSignature() {
            _signatureparentCoBorrower.jSignature('reset');
            _signStatusCoBorrower.val('');
        }

        $(document).ready(function () {
            let signatureparent = $("#signatureparent");
            let signatureparentCoBorrower = $("#signatureparentCoBorrower");
            let _signStatus = $("#signStatus");
            let _signStatusCoBorrower = $("#signStatusCoBorrower");

            let isMultiStepMode = <?php echo isset($_REQUEST['view']) ? 'true' : 'false'; ?>;
            let containerWidth = isMultiStepMode ? $("#wizardMainPage").width() : $(".container").width();
            let signatureOptions = {
                color: "#0000ff",
                'UndoButton': true,
                width: containerWidth - 50,
                height: isMultiStepMode ? 450 : 350,
            };
            signatureparent.jSignature(signatureOptions).bind('change', function () {
                console.log(isMultiStepMode ? 'Signed in Multi Step Mode' : 'Signed in Normal Mode');
                _signStatus.val('1');
                handleSignatureVisibility();
                enableButtons();
            });
            signatureparentCoBorrower.jSignature(signatureOptions).bind('change', function () {
                console.log(isMultiStepMode ? 'Cobor Signed in Multi Step Mode' : 'Cobor Signed in Normal Mode');
                _signStatusCoBorrower.val('1');
                handleSignatureVisibility();
                enableButtons();
            });
            _signStatus.val('');
            _signStatusCoBorrower.val('');

            let LMRId = parseInt("<?php echo $LMRId; ?>");
            let UType = "<?php echo htmlspecialchars($UType); ?>";
            if (LMRId > 0) {
                $('.signatureDiv').hide();
                if (UType === 'CoBorrower') {
                    $('.enabledForCoborrowerDiv').show();
                }
            } else {
                $('.isEsignRequiredCard').hide();
                $('#tacDiv').hide();
            }

            let _loanModForm1 = $('#loanModForm');
            _loanModForm1.on('change input', 'input, select, textarea', function (e) {
                //   $('#loanModForm').bind('keyup change', function (e) {
                if (_loanModForm1.serialize() !== form_original_data
                    || (e.target.type === 'file' && e.type === 'change')) {
                    // if ($('#submitStatus').val() === '0' || ($('#wizardForm').length)) {
                    e.preventDefault();
                    handleSignatureVisibility();
                    enableButtons();
                    // }
                }
            });
            $('.switch-off, .switch-on').click(function (e) {
                    e.preventDefault();
                    handleSignatureVisibility();
                    enableButtons();
                }
            );

            <?php
            /* When the web form link is sent to the user, he should be allowed to edit the file, if the Loan program / LMD ID is available. Else he should not be allowed to edit / submit the file.
              - By Berin
            - Date: 19 May 2018
             */
            if (!($emailOpt == '' || $LMRId > 0)) { ?>
            allowToEditDisabledFields('', 'disabledFields'); // Disabled Fields..
            <?php } ?>

            if (shareLink === 'shareLink') {
                $("#loanModForm input,textarea,select").prop("disabled", true);
                $("#loanModForm .chzn-select").prop('disabled', true).trigger("chosen:updated");
                $('#offers input,textarea').prop("disabled", false);
                if (parseInt(aud) === 1) {
                    $('#UploadFileDocList input').prop("disabled", false);
                }
            }
        });
    </script>
<?php if (cypher::myEncryption($executiveId) == 'dcc4a023d3742c86') { ?>
    <style>
        .mandatory {
            background-color: #fff !important;
        }

        .navbar {
            display: none !important;
        }
    </style>
<?php } ?>
    <script type="text/javascript" src="/assets/js/3rdParty/iframeSizer-4.1.1/iframeResizer.contentWindow.min.js"
            defer></script>
<?php if ($so == 'yes') {
    $soCssArray = ['/assets/css/submitOfferStyle.css'];
    Strings::includeMyCSS($soCssArray);
} ?>

    <!-- Schedule of Real Estate Start -->
    <script>
        function realEstateInfoPopup(encCID, encCLOSRID, Opt) {
            clear_form_elements('realEstateInfoContent');
            $('.anyMortgagesLiensDispOpt').css("display", "none");
            $('.anyOtherMortgagesLiensDispOpt').css("display", "none");
            $('.salesDispOpt').css("display", "none");
            $('.mortgagepopInfo').css("display", "block");
            $('.incomeValuespopInfo').css("display", "block");
            $('#encCLOSRID').val(encCLOSRID);
            $('#LOSRID').val(encCLOSRID);
            let fileLMRID = "<?php echo $encLMRId; ?>";
            let fileCID = "<?php echo $encrypCID; ?>";
            $("#anyMortgagesLiensNo").attr('checked', 'checked');
            $("#anyOtherMortgagesLiensNo").attr('checked', 'checked');
            $('#fileLMRId').val(fileLMRID);
            $('#schCID').val(fileCID);
            if (encCID !== '') {
                $.ajax({
                    type: 'POST',
                    url: 'backoffice/getRealEstateInfoPopup.php',
                    data: jQuery.param({'encCID': encCID, 'encCLOSRID': encCLOSRID, 'Opt': Opt}),
                    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                    success: function (myData) {
                        let obj = $.parseJSON(myData);
                        assignFieldValue(obj[0].schedulePropAddr, 'schedulePropAddr');
                        assignFieldValue(obj[0].schedulePropCity, 'schedulePropCity');
                        assignFieldValue(obj[0].schedulePropState, 'schedulePropState');
                        assignFieldValue(obj[0].schedulePropZip, 'schedulePropZip');
                        assignFieldValue(obj[0].scheduleStatus, 'scheduleStatus');
                        assignFieldValue(obj[0].propType, 'propType');
                        checkPropInfo(obj[0].propType);
                        assignFieldValue(obj[0].propertyDesc, 'propertyDesc');
                        assignFieldValue(obj[0].presentMarketValue, 'presentMarketValue');
                        assignFieldValue(obj[0].amountOfMortgages, 'amountOfMortgages');
                        assignFieldValue(obj[0].grossRentalIncome, 'grossRentalIncome');
                        assignFieldValue(obj[0].mortgagePayments, 'mortgagePayments');
                        assignFieldValue(obj[0].insMaintTaxMisc, 'insMaintTaxMisc');
                        assignFieldValue(obj[0].netRentalIncome, 'netRentalIncome');
                        assignFieldValue(obj[0].titledUnder, 'titledUnder');
                        assignFieldValue(autoNumericConverter(obj[0].purchasePrice), 'purchasePrice');
                        assignFieldValue(autoNumericConverter(obj[0].valueofImprovementsMade), 'valueofImprovementsMade');
                        assignFieldValue(obj[0].intendedOccupancy, 'intendedOccupancy');
                        $("#anyMortgagesLiens" + obj[0].anyMortgagesLiens).attr('checked', 'checked');
                        if (obj[0].anyMortgagesLiens === 'Yes') {
                            $('.anyMortgagesLiensDispOpt').css("display", "table-row");
                        } else {
                            $('.anyMortgagesLiensDispOpt').css("display", "none");
                        }
                        assignFieldValue(obj[0].creditorName, 'creditorName');
                        assignFieldValue(obj[0].accountNumber, 'accountNumber');
                        assignFieldValue(obj[0].loanStatus, 'loanStatus');
                        assignFieldValue(autoNumericConverter(obj[0].unpaidBalance), 'unpaidBalance');
                        assignFieldValue(autoNumericConverter(obj[0].monthlyPayment), 'monthlyPayment');
                        assignFieldValue(obj[0].mortgageType, 'type');
                        assignFieldValue(autoNumericConverter(obj[0].creditLimit), 'creditLimit');
                        $("#anyOtherMortgagesLiens" + obj[0].anyOtherMortgagesLiens).attr('checked', 'checked');
                        if (obj[0].anyOtherMortgagesLiens === 'Yes') {
                            $('.anyOtherMortgagesLiensDispOpt').css("display", "table-row");
                        } else {
                            $('.anyOtherMortgagesLiensDispOpt').css("display", "none");
                        }
                        assignFieldValue(obj[0].datePurchased, 'datePurchasedSchedule');
                        assignFieldValue(obj[0].salesDate, 'salesDate');
                        assignFieldValue(obj[0].salesPrice, 'salesPrice');
                        assignFieldValue(obj[0].maturityDateSchedule, 'maturityDateSchedule');
                        assignFieldValue(obj[0].maturityDateAnother, 'maturityDateAnother');
                        assignFieldValue(obj[0].creditorNameAnother, 'creditorNameAnother');
                        assignFieldValue(obj[0].accountNumberAnother, 'accountNumberAnother');
                        assignFieldValue(obj[0].loanStatusAnother, 'loanStatusAnother');
                        assignFieldValue(autoNumericConverter(obj[0].unpaidBalanceAnother), 'unpaidBalanceAnother');
                        assignFieldValue(autoNumericConverter(obj[0].monthlyPaymentAnother), 'monthlyPaymentAnother');
                        assignFieldValue(obj[0].typeAnother, 'typeAnother');
                        assignFieldValue(obj[0].ownership, 'ownership');
                        assignFieldValue(autoNumericConverter(obj[0].creditLimitAnother), 'creditLimitAnother');
                        if (obj[0].scheduleStatus === 'Sold') {
                            $('.salesDispOpt').css("display", "block");
                            $('.mortgagepopInfo').css("display", "none");
                            $('.incomeValuespopInfo').css("display", "none");
                        } else {
                            $('.salesDispOpt').css("display", "none");
                            $('.mortgagepopInfo').css("display", "block");
                            $('.incomeValuespopInfo').css("display", "block");
                        }
                    }
                });
            }
            $('#realEstateInfoContent').modal('toggle');
        }

        function checkPropInfo(val) {
            if (parseInt(val) === 47) {
                $('#propInfoDiv').show();
            } else {
                $('#propInfoDiv').hide();
            }
        }
    </script>
<?php
if (isset($_REQUEST['pdf'])) {
    $FArray = [
        '/assets/js/3rdParty/jquery-autogrow-textarea/jquery.autogrow-textarea.js'
    ];
    Strings::includeMyScript($FArray); ?>
    <script>
        $("textarea").autogrow();
/*        let totalHeight = 0;
        $('.HMLOLoanInfoSections:visible').each(function () {
            totalHeight += $(this).outerHeight(); // Add current card height
            if (totalHeight > 1120) {
                $(this).after('<div style="page-break-before:always"></div>');
                totalHeight = 0;
            }
        });
        */
        if( $('.TACCard:visible')) {
            $('.mortgageNotesCard').after('<div style="page-break-before:always"></div>');
         }
    </script>
<?php }

if ($activeTab == 'LI' || $activeTab == 'QAPP' || $activeTab == 'AL') { ?>
    <div class="modal fade realEstateInfoContent" id="realEstateInfoContent" role="dialog">
        <div class="modal-dialog modal-lg">
            <!-- Modal content-->
            <form name="realEstateForm" id="realEstateForm" method="POST" action="#">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                        <h4 class="modal-title">Schedule of Real Estate</h4>
                    </div>
                    <div class="modal-body">
                        <?php require 'backoffice/addLORealEstateInfoPopup.php'; ?>
                    </div>
                    <div class="modal-footer">
                        <input type="button" name="butSubmit" id="butSubmit" value="Save"
                               class="btn btn-primary">
                        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <script type="text/javascript">
        $("#butSubmit").click(function () {
            let trueCnt = 0;
            jQuery('#realEstateForm .mandatory').each(function () {
                if ($(this).is(':visible') && !($(this).prop('disabled'))) {
                    let fldVal = $(this).val();
                    let label = $('label[for="' + this.id + '"]').html();
                    if (label === undefined) label = '';                            // | Label Value is empty.

                    if (this.type === 'select-one' || this.type === 'select-multiple') {
                        if (fldVal === '') {
                            toastrNotification("Please Select " + label, 'error');
                            $(this).focus();
                            trueCnt++;
                            return false;
                        }
                    } else if (this.type === 'text' || this.type === 'textarea' ||
                        this.type === 'date' || this.type === 'number' || this.type === 'tel' || this.type === 'email') {
                        if (fldVal === '') {
                            toastrNotification("Please Enter " + label, 'error');
                            $(this).focus();
                            trueCnt++;
                            return false;
                        }
                    } else if (this.type === 'checkbox' || this.type === 'radio') {
                        if (!$("input[name=" + this.name + "]:checked").val()) {
                            toastrNotification("Please Select " + label, 'error');
                            $(this).focus();
                            trueCnt++;
                            return false;
                        }
                    }
                }
            });
            if (trueCnt === 0) {
                $.ajax({
                    url: "pops/LORealEstateFormSave.php",
                    method: "POST",
                    data: $("#realEstateForm").serialize(),
                    success: function (data) {
                        let edLOSRID = $('#LOSRID').val();
                        $('#realEstateInfoContent').modal('hide');
                        let parsed_result = $.parseJSON(data);
                        let posteddata = $("#realEstateForm").serialize();
                        let postedvalues = posteddata.split("&");
                        let finalArray = [];
                        for (let i = 0; i < postedvalues.length; i++) {
                            let finalVal = postedvalues[i].substr(0, postedvalues[i].indexOf('='));
                            if (finalVal !== 'fileLMRId' && finalVal !== 'CID' &&
                                finalVal !== 'encCLOSRID' && finalVal !== 'LOSRID' && finalVal !== 'popOpt') {
                                finalArray.push(finalVal);
                            }
                        }
                        console.log(finalArray);
                        if (jQuery.isEmptyObject(parsed_result)) {
                            toastrNotification("Error while update", 'error');
                            return false;
                        }

                        let LOSRID = parsed_result.LOSRID;
                        // let fileID = parsed_result.fileID;
                        let encLOSRID = parsed_result.encLOSRID;
                        let encFileID = parsed_result.encFileID;
                        let schedulePropAddr = parsed_result.schedulePropAddr;
                        let schedulePropCity = parsed_result.schedulePropCity;
                        let schedulePropState = parsed_result.schedulePropState;
                        let schedulePropZip = parsed_result.schedulePropZip;
                        let scheduleStatus = parsed_result.scheduleStatus;
                        // let propType = parsed_result.propType;
                        let propTypeVal = parsed_result.propTypeVal;
                        let presentMarketValue = autoNumericConverter(parsed_result.presentMarketValue);
                        let amountOfMortgages = autoNumericConverter(parsed_result.amountOfMortgages);
                        let grossRentalIncome = autoNumericConverter(parsed_result.grossRentalIncome);
                        let mortgagePayments = autoNumericConverter(parsed_result.mortgagePayments);
                        let insMaintTaxMisc = autoNumericConverter(parsed_result.insMaintTaxMisc);
                        let netRentalIncome = autoNumericConverter(parsed_result.netRentalIncome);
                        let titledUnder = parsed_result.titledUnder;
                        let datePurchased = parsed_result.datePurchased;
                        let maturityDateAnother = parsed_result.maturityDateAnother;
                        let maturityDateSchedule = parsed_result.maturityDateSchedule;
                        let purchasePrice = autoNumericConverter(parsed_result.purchasePrice);
                        let valueofImprovementsMade = autoNumericConverter(parsed_result.valueofImprovementsMade);
                        let intendedOccupancy = parsed_result.intendedOccupancy;
                        let anyMortgagesLiens = parsed_result.anyMortgagesLiens;
                        let creditorName = parsed_result.creditorName;
                        let accountNumber = parsed_result.accountNumber;
                        let loanStatus = parsed_result.loanStatus;
                        let unpaidBalance = autoNumericConverter(parsed_result.unpaidBalance);
                        let monthlyPayment = autoNumericConverter(parsed_result.monthlyPayment);
                        let mortgageType = parsed_result.mortgageType;
                        let creditLimit = autoNumericConverter(parsed_result.creditLimit);
                        let anyOtherMortgagesLiens = parsed_result.anyOtherMortgagesLiens;
                        let creditorNameAnother = parsed_result.creditorNameAnother;
                        let accountNumberAnother = parsed_result.accountNumberAnother;
                        let loanStatusAnother = parsed_result.loanStatusAnother;
                        let unpaidBalanceAnother = autoNumericConverter(parsed_result.unpaidBalanceAnother);
                        let monthlyPaymentAnother = autoNumericConverter(parsed_result.monthlyPaymentAnother);
                        let typeAnother = parsed_result.typeAnother;
                        let creditLimitAnother = autoNumericConverter(parsed_result.creditLimitAnother);
                        let ownership = (parsed_result.ownership);

                        let newHeaderTitle = '';
                        if ($.inArray('schedulePropAddr', finalArray) !== -1) {
                            newHeaderTitle += schedulePropAddr;
                        }
                        if ($.inArray('schedulePropCity', finalArray) !== -1) {
                            newHeaderTitle += ', ' + schedulePropCity;
                        }
                        if ($.inArray('schedulePropState', finalArray) !== -1) {
                            newHeaderTitle += ', ' + schedulePropState;
                        }
                        let divText1 = '<div id="realEstateInfoDivId_' + LOSRID + '">';
                        let divText2 = '<div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true"><div class="panel panel-default"><div class="panel-heading" role="tab" id="headingOne"><div class="panel-title"><div class="col-md-11"><a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapse_' + LOSRID + '" aria-expanded="true" aria-controls="collapseOne" class=""><div class="realInfoCnt"><h4>' + newHeaderTitle + '</h4></div></a></div>';
                        divText2 += "<div class=\"col-md-1\"><span class=\"with-children-tip\"><a class=\"fa fa-trash-o fa-1x icon-red tip-bottom\" style=\"text-decoration:none;\" href=\"javascript:deleteClientLOScheduleRealInfo('" + encFileID + "','" + encLOSRID + "','realEstateInfoDivId_" + LOSRID + "', 'File');\" title=\"Click to delete\"></a></span>&nbsp;<span class=\"with-children-tip\"><a class=\"fa fa-pencil fa-1x tip-bottom icon-green\" title=\"Click to edit client entity Info\" href=\"javascript:realEstateInfoPopup('" + encFileID + "','" + encLOSRID + "', 'File');\"></a></span></div>";
                        divText2 += '</div><div class="clearfix"></div></div><div id="collapse_' + LOSRID + '" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingOne" aria-expanded="true" style=""><div class="panel-body">';

                        if ($.inArray('schedulePropAddr', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Property Address : <span class="H5">' + schedulePropAddr + '</span></b></div>';
                        }
                        if ($.inArray('schedulePropCity', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Property City : <span class="H5">' + schedulePropCity + '</span></b></div>';
                        }
                        if ($.inArray('schedulePropState', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Property City : <span class="H5">' + schedulePropState + '</span></b></div>'
                        }
                        divText2 += '<div class="clearfix"></div>';
                        if ($.inArray('schedulePropZip', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Property Zip : <span class="H5">' + schedulePropZip + '</span></b></div>';
                        }
                        if ($.inArray('scheduleStatus', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Status : <span class="H5">' + scheduleStatus + '</span></b></div>';
                        }
                        if ($.inArray('ownership', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Ownership % : <span class="H5">' + ownership + '</span></b></div>';
                        }
                        if ($.inArray('propType', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Property Type : <span class="H5">' + propTypeVal + '</span></b></div>';
                        }
                        divText2 += '<div class="clearfix"></div>';
                        if ($.inArray('presentMarketValue', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Present Market Value : <span class="H5">' + presentMarketValue + '</span></b></div>';
                        }
                        if ($.inArray('amountOfMortgages', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Balance of Mortgages &amp; Liens : <span class="H5">' + amountOfMortgages + '</span></b></div>';
                        }
                        if ($.inArray('grossRentalIncome', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Gross Rental Income : <span class="H5">' + grossRentalIncome + '</span></b></div>';
                        }
                        divText2 += '<div class="clearfix"></div>';
                        if ($.inArray('mortgagePayments', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Mortgage Payments : <span class="H5">' + mortgagePayments + '</span></b></div>';
                        }
                        if ($.inArray('insMaintTaxMisc', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Insurance, Taxes, HOA (If Not Included in Payment) : <span class="H5">' + insMaintTaxMisc + '</span></b></div>';
                        }
                        if ($.inArray('netRentalIncome', finalArray) !== -1) {
                            divText2 += '<div class="no-padding col-md-4"><b>Net Rental Income : <span class="H5">' + netRentalIncome + '</span></b></div>';
                        }
                        if ($.inArray('titledUnder', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Titled Under : <span class="H5">' + titledUnder + '</span></b></div>';
                        }
                        if ($.inArray('datePurchased', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Date Purchased : <span class="H5">' + datePurchased + '</span></b></div>';
                        }
                        if ($.inArray('purchasePrice', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Purchase Price : <span class="H5">' + purchasePrice + '</span></b></div>';
                        }
                        if ($.inArray('valueofImprovementsMade', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Value of Improvements Made : <span class="H5">' + valueofImprovementsMade + '</span></b></div>';
                        }
                        if ($.inArray('intendedOccupancy', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Intended Occupancy : <span class="H5">' + intendedOccupancy + '</span></b></div>';
                        }
                        divText2 += '<div class="clearfix"></div>';
                        if ($.inArray('salesDate', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Sale Date : <span class="H5">' + parsed_result.salesDate + '</span></b></div>';
                        }
                        if ($.inArray('salesPrice', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Sale Price : <span class="H5">' + autoNumericConverter(parsed_result.salesPrice) + '</span></b></div>';
                        }
                        divText2 += '<div class="clearfix"></div>';
                        if ($.inArray('anyMortgagesLiens', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Are there any mortgages or liens? : <span class="H5">' + anyMortgagesLiens + '</span></b></div>';
                        }
                        divText2 += '<div class="clearfix"></div>';
                        if ($.inArray('creditorName', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Creditor Name : <span class="H5">' + creditorName + '</span></b></div>';
                        }
                        if ($.inArray('accountNumber', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Account Number : <span class="H5">' + accountNumber + '</span></b></div>';
                        }
                        if ($.inArray('loanStatus', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Loan Status : <span class="H5">' + loanStatus + '</span></b></div>';
                        }
                        if ($.inArray('unpaidBalance', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Unpaid Balance : <span class="H5">' + unpaidBalance + '</span></b></div>';
                        }
                        if ($.inArray('monthlyPayment', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Monthly Payment : <span class="H5">' + monthlyPayment + '</span></b></div>';
                        }
                        if ($.inArray('type', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Type : <span class="H5">' + mortgageType + '</span></b></div>';
                        }
                        if ($.inArray('creditLimit', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Credit Limit : <span class="H5">' + creditLimit + '</span></b></div>';
                        }
                        if ($.inArray('maturityDateSchedule', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Maturity Date : <span class="H5">' + maturityDateSchedule + '</span></b></div>';
                        }
                        divText2 += '<div class="clearfix"></div>';
                        if ($.inArray('anyOtherMortgagesLiens', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Is their another mortgage or lien? : <span class="H5">' + anyOtherMortgagesLiens + '</span></b></div>';
                        }
                        divText2 += '<div class="clearfix"></div>';
                        if ($.inArray('creditorNameAnother', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Creditor Name : <span class="H5">' + creditorNameAnother + '</span></b></div>';
                        }
                        if ($.inArray('accountNumberAnother', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Account Number : <span class="H5">' + accountNumberAnother + '</span></b></div>';
                        }
                        if ($.inArray('loanStatusAnother', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Loan Status : <span class="H5">' + loanStatusAnother + '</span></b></div>';
                        }
                        if ($.inArray('unpaidBalanceAnother', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Unpaid Balance : <span class="H5">' + unpaidBalanceAnother + '</span></b></div>';
                        }
                        if ($.inArray('monthlyPaymentAnother', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Monthly Payment : <span class="H5">' + monthlyPaymentAnother + '</span></b></div>';
                        }
                        if ($.inArray('typeAnother', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Type : <span class="H5">' + typeAnother + '</span></b></div>';
                        }
                        if ($.inArray('creditLimitAnother', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Credit Limit : <span class="H5">' + creditLimitAnother + '</span></b></div>';
                        }
                        if ($.inArray('maturityDateAnother', finalArray) !== -1) {
                            divText2 += '<div class="col-md-4 no-padding"><b>Maturity Date : <span class="H5">' + maturityDateAnother + '</span></b></div>';
                        }

                        divText2 += '</div></div></div></div></div>';
                        let divText3 = '</div>';
                        if (edLOSRID === '') {
                            let divText = divText1 + divText2 + divText3;
                            $(divText).appendTo('#showLORealEstateInfo');
                        } else {
                            $('#realEstateInfoDivId_' + LOSRID).html(divText2);
                        }
                    }
                });
            }
        });
    </script>
<?php } ?>
    <!-- Schedule of Real Estate End -->

    <!-- websocket start of snip HMLOWebForm.php -->
<?php
//dd($clientId);
if (WEBSOCKET_SERVER > '' && $LMRId > '') {
    $userID = $UType . $clientId;
    ?>
    <script type="text/javascript">
        let daveSocket = {
            userName: "<?php echo htmlspecialchars($userName); ?>",
            userId: "<?php echo htmlspecialchars($userGroup . $userNumber); ?>",
            documentId: "<?php echo htmlspecialchars(Request::GetClean('lId')) ?>",
        };
    </script>
    <script type="text/javascript" src="/assets/js/dave-socket.js?v1.2"></script>
    <?php
}
?>
    <!-- client portal does not have the div to show the bubbles purposely so they cannot see employees, etc.  Ignore the console error.  We want this behavior so
     they still load the script and bo users can see them -->
    <!-- websocket end of snip -->

    <script src="webForm/js/webForm.js?<?php echo CONST_JS_VERSION; ?>"></script>
<?php
require('includesNew/_customPage.php');

if (isset($_REQUEST['view'])) {
    if (cypher::myDecryption(Request::GetClean('view')) == 'wizard') {
        $FArray = ['/assets/js/3rdParty/KTApp/custom/wizard/wizard-2.js',];
        Strings::includeMyScript($FArray);
    }
}

Database2::saveLogQuery();
