<?php

define('APP_ROOT_PATH', dirname(__DIR__, 2));

global $ip, $gpSecurityInstrumentArray, $allowed;

use models\Database2;
use models\lendingwise\_tblSlowQueries;
use models\PageVariables;
use models\Server;
use models\standard\Dates;

if (($_SERVER['REMOTE_ADDR'] ?? null) === '***********') {
    exit;
}


if (!defined('INCLUDED_UTIL')) {
    define('INCLUDED_UTIL', 1);
} else {
    dd('util.php already included, fix your includes', debug_backtrace());
}

/**
 * @param string $filename
 */
function loadENV(string $filename, bool $overrideExisting = false)
{
    $filename = APP_ROOT_PATH . '/' . $filename;

    if (!file_exists($filename)) {
        return;
    }
    $lines = file($filename, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {

        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        $line = str_replace('"', '', $line);
        $line = str_replace('\'', '', $line);

        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);

        // if the Env Var is already set then do not override it. It does not matter if it has an empty value.
        if (isset($_ENV[$name]) && $overrideExisting == false) {
            continue;
        }

        putenv(sprintf('%s=%s', $name, $value));
        $_ENV[$name] = $value;
    }
}


if (!function_exists('str_starts_with')) {
    function str_starts_with(?string $str, ?string $match): bool
    {
        if (!$str || !$match) {
            return false;
        }

        return strcasecmp(substr($str, 0, strlen($match)), $match) == 0;
    }
}

loadENV('.env');
loadENV('.env.local');

define('CONST_LOG_QUERIES', $_ENV['LOG_QUERIES'] ?? false);
define('IS_MAINTENANCE', $_ENV['IS_MAINTENANCE'] ?? false);
define('ERROR_REPORTING_LEVEL', intval($_ENV['ERROR_REPORTING_LEVEL'] ?? 0));
define('ERROR_CATCH_ALL', intval($_ENV['ERROR_CATCH_ALL'] ?? 0));
define('SHOW_DEBUG_TOOLS', intval($_ENV['SHOW_DEBUG_TOOLS'] ?? 0));
define('CONST_APP_HTTP_HOST', $_ENV['APP_HTTP_HOST'] ?? null);
define('WEBHOOK_DEBUG_URL', $_ENV['WEBHOOK_DEBUG_URL'] ?? null);
define('GOOGLE_CAPTCHA_SITE_KEY', $_ENV['GOOGLE_CAPTCHA_SITE_KEY'] ?? null);
define('CONST_IS_LENDINGWISE_DOMAIN',
    stristr($_SERVER['HTTP_HOST'] ?? '', CONST_APP_HTTP_HOST) !== false
);
define('RECAPTCHA_DISABLE', $_ENV['RECAPTCHA_DISABLE'] ?? 1);
define('MAX_MYSQL_MEMORY_GB', $_ENV['MAX_MYSQL_MEMORY_GB'] ?? 1);

define('K_TCPDF_THROW_EXCEPTION_ERROR', $_ENV['K_TCPDF_THROW_EXCEPTION_ERROR'] ?? null);
define('DATA_VALIDATION', $_ENV['DATA_VALIDATION'] ?? 0);

if (IS_MAINTENANCE) {
    if (!in_array($_SERVER['REMOTE_ADDR'] ?? null, [
        '*************',
        '**************',
        '127.0.0.1',
        '*************',
        '**************',
        '*************',
        '',
    ])) {
        header("Location: ../maintenance.html");
        exit();
    }
}

switch (ERROR_REPORTING_LEVEL) {
    case 5:
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
        break;

    case 4:
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL ^ E_DEPRECATED);
        break;

    case 3:
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL ^ E_DEPRECATED ^ E_NOTICE);
        break;

    case 2:
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL ^ E_DEPRECATED ^ E_WARNING ^ E_NOTICE);
        break;

    default:
        ini_set('display_errors', 0);
        ini_set('display_startup_errors', 0);
        error_reporting(0);
        break;

}

date_default_timezone_set('America/New_York'); // EST

require_once APP_ROOT_PATH . '/modules.php';

// TODO: this needs to be ultimately removed, use Request class
$_POST = array_map('models\standard\Strings::stripSlashesFull', $_POST);
$_GET = array_map('models\standard\Strings::stripSlashesFull', $_GET);
$_REQUEST = array_map('models\standard\Strings::stripSlashesFull', $_REQUEST);

const CONST_BO_PATH = APP_ROOT_PATH . '/public/backoffice/';
const FPDF_FONTPATH = APP_ROOT_PATH . '/vendor/setasign/fpdf/font/';

require APP_ROOT_PATH . '/config.php';
require APP_ROOT_PATH . '/functions/chk_session.php';

define('GUID', uniqid('u') . '.' . Dates::microtime_float());

define('CONST_USE_LEGACY_CONNECTOR', intval($_ENV['USE_LEGACY_CONNECTOR'] ?? 0));
Database2::$useLegacy = CONST_USE_LEGACY_CONNECTOR;


if (ERROR_CATCH_ALL) {
    set_error_handler(function ($errno, $errstr, $errfile, $errline) {
        if ($errno != 8192) { // deprecated mcrypt
            DebugToFile($errfile, [
                'errstr' => $errstr,
                'errno' => $errno,
                'errfile' => $errfile,
                'errline' => $errline
            ]);
        }
    });
}

if (SLOW_PAGE_SECONDS) {
    register_shutdown_function(function () {
        if (!isset($_SERVER['HTTP_HOST'])) {
            return;
        }

        if (!PageVariables::$globalTimer) {
            return;
        }

        $start = PageVariables::$globalTimer;

        $seconds = microtime(true) - $start;
        if ($seconds <= SLOW_PAGE_SECONDS) {
            return;
        }

        $l = new _tblSlowQueries();
        $l->environment = CONST_ENVIRONMENT;
        $l->remoteAddr = Server::RemoteADDR() ?? 'script';
        $l->query = json_encode(Database2::getQueryLog());
        $l->params = json_encode($_GET);
        $l->errorMsg = 'slow_page';
        $l->duration = $seconds;
        $l->guid = GUID;
        $l->recordDate = Dates::Timestamp();
        $l->page = substr($_SERVER['REQUEST_URI'] ?? $_SERVER['PHP_SELF'], 0, 255);
        $l->Save();
    });
}

function doNothing()
{
    // to silence the warnings
}

// DO NOT ADD ANYTHING TO THIS FILE

