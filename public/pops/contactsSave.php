<?php

use models\composite\oContacts\saveContactsDocs;
use models\composite\oContacts\saveContactsInfo;
use models\composite\oContacts\saveFileContacts;
use models\constants\gl\glMimeTypes;
use models\cypher;
use models\FileStorage;
use models\lendingwise\tblContactsType;
use models\lendingwise\tblFileContacts;
use models\PageVariables;
use models\Request;
use models\standard\Dates;
use models\standard\Strings;
use models\standard\UserAccess;
use models\UploadServer;

require 'popsConfig.php';

UserAccess::CheckAdminUse();


global $contacts;

$cRole = '';
//Upload the multiple images
$docArray = [];
$fileExtension = null;

$LMRId = Request::GetClean('LMRId') ? (int)cypher::myDecryption(Request::GetClean('LMRId')) : null;
$CID = Request::GetClean('CID') ? (int)cypher::myDecryption(Request::GetClean('CID')) : null;

$contactsInfoArray = [];
foreach ($contacts as $field) {
    if (Request::isset($field)) {
        $contactsInfoArray[$field] = Request::GetClean($field);
    }
}
$PCID = Request::GetClean('assignedToProcessingCompany');
$contactsInfoArray['contactPhone'] = Request::GetClean('phoneNumber');
$contactsInfoArray['contactFax'] = Request::GetClean('faxNumber');
$contactsInfoArray['contactCell'] = Request::GetClean('cellNumber');
$contactsInfoArray['processingCompanyId'] = $PCID;
$contactsInfoArray['PCID'] = $PCID;
$contactsInfoArray['LMRId'] = $LMRId;
$contactsInfoArray['CID'] = $CID;
$contactsInfoArray['UID'] = PageVariables::$userNumber;
$contactsInfoArray['UGroup'] = PageVariables::$userGroup;

$cRole = Request::GetClean('cRole');
$other = Request::GetClean('other');
$contactTypeonLoad = Request::GetClean('contactTypeonLoad');
$contactType = Request::GetClean('contactType');

if (!$cRole && $contactType) {
    if ($contactType == 10 || $contactType == 'Other') {
        $cRole = $other;
    } else if ($contactType == 18) {
        $cRole = 'Insurance Rep';
    } else {
        $role = tblContactsType::Get(['CTypeID' => $contactType]);
        $cRole = $role->TYPE;
    }
}
$CID = saveContactsInfo::getReport($contactsInfoArray);
if (!$CID) {
    echo json_encode(['code' => '101', 'msg' => 'Error While Updating.']);
    exit;
}
if ($LMRId) {
    if ($cRole == 'Other' && $other != '') {
        $cRole = $other;
    }
    saveFileContacts::getReport([
        'LMRId' => $LMRId,
        'CID'   => [['CID' => $CID, 'cRole' => $cRole]],
    ]);
}

if (($contactTypeonLoad != $contactType)) {
    //fetch all lmr files to which the contact is assigned and modify the crole in tblFileContact table if contact type is modified against the contact
    if ($contactType == 10 || $contactType == 'Other') {
        $contactTypeName = $other;
    } else if ($contactType == 18) {
        $contactTypeName = 'Insurance Rep';
    } else {
        $tblContactsType = tblContactsType::Get([
            'CTypeID' => $contactType,
        ]);
        $contactTypeName = $tblContactsType->TYPE;
    }
    $tblFileContacts = tblFileContacts::GetAll(['CID' => $CID]);
    foreach ($tblFileContacts as $item) {
        $item->cRole = $contactTypeName;
        $item->Save();
    }
}
$fileUploadCount = count($_FILES['contactFile']['name'] ?? []);
if ($fileUploadCount) {
    for ($f = 0; $f < $fileUploadCount; $f++) {
        // File Details
        $fileExtension = '';
        $docName = '';

        $file = $_FILES['contactFile'] ?? [];

        $file_name = trim($file['name'][$f] ?? '');
        $tmp_name = trim($file['tmp_name'][$f] ?? '');
        $file_type = trim($file['type'][$f] ?? '');
        $file_size = intval($file['size'][$f] ?? 0);

        if (!$file_size) {
            continue;
        }
        if ($file_name) {
            $docName = Strings::removeDisAllowedChars($file_name);
            $info = pathinfo($docName);
            if (count($info) > 0) {
                $fileExtension = $info['extension'];
            }
            $docName = str_ireplace('.' . $fileExtension, '', $file_name);
        }
        $docArray[] = [
            'fileSrc_name' => $file_name,
            'tmp_name'     => $tmp_name,
            'file_type'    => $file_type,
            'file_size'    => $file_size,
            'docName'      => $docName,
            'docCategory'  => 'Offer Docs',
        ];
    }

    foreach ($docArray as $m => $item) {
        if (!$item['fileSrc_name']) continue;
        if (!in_array($item['file_type'], glMimeTypes::$glMimeTypes) || $item['file_size'] > CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED) {
            $responseAjax = ['code' => '101', 'msg' => 'Unsupported File Format/File Size is too large.'];
            echo json_encode($responseAjax);
            exit();
        }
        $file_name = Strings::removeDisAllowedChars($item['fileSrc_name']);
        $info = pathinfo($file_name);
        if (count($info) > 0) $fileExtension = $info['extension'];

        $encFilePath = Strings::stripQuote($item['docName']) . '-' . $m . '-' . date('Y-m-d-H-i-s');
        $encContactFile = $encFilePath . '.' . $fileExtension;
        //FILENAME. DATE . EXT

        UploadServer::upload([
            'fileExtension'     => $fileExtension,
            'PCID'              => $PCID,
            'oldFPCID'          => $PCID,
            'uploadedBy'        => PageVariables::$userNumber,
            'userGroup'         => PageVariables::$userGroup,
            'uploadingUserType' => PageVariables::$userRole,
            'docName'           => Strings::stripQuote($item['docName']),
            'docCategory'       => $item['docCategory'],
            'recordDate'        => Dates::Datestamp(),
            'contactsDocs'      => 1,
            'contactID'         => $CID,
            'tmpFileContent'    => base64_encode(FileStorage::getFile(dirname($item['tmp_name']) . '/' . basename($item['tmp_name']))),
            'fileDocName'       => $encContactFile,
        ]);
        // save to contactsDocs
        $params['PCID'] = $PCID;
        $params['contactId'] = $CID;
        $params['contactFile'] = Strings::stripQuote($item['docName']);
        $params['encContactFile'] = $encContactFile;
        saveContactsDocs::getReport($params);
    }
}
echo json_encode(['code' => '100', 'msg' => 'Updated Successfully.']);
exit();
