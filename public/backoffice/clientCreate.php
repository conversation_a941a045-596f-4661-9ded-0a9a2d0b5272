<?php
global $userGroup, $userRole, $assignedPCID, $PCID, $fileTab;


use models\constants\gl\glUserRole;
use models\Controllers\loanForm;
use models\cypher;
use models\Request;
use models\standard\BaseHTML;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';
require 'initPageVariables.php';
require 'getPageVariables.php';

UserAccess::CheckAdminUse();

if(Request::GetClean('PCID')) {
    $assignedPCID = cypher::myDecryption(Request::GetClean('PCID'));
}

$allowEmpToViewAllFiles = 1;
$selClientId = 0;
$isClientProfile = 1;
$isClient = '';
$showGOG = 'NO'; //Hide the GOG section in the borrower profile assets tab.
if (isset($_SESSION['allowEmpToViewAllFiles'])) $allowEmpToViewAllFiles = $_SESSION['allowEmpToViewAllFiles'];

if ($userGroup == 'Employee' && $allowEmpToViewAllFiles == 0) {
    header('Location: adminLogout.php');
    exit();
}

if (isset($_REQUEST['cId'])) $selClientId = intval(cypher::myDecryption(Request::GetClean('cId')));
if (isset($_REQUEST['isClient'])) $isClient = intval(Request::GetClean('isClient'));

echo BaseHTML::openPage('Client Profile - ' . CONST_DOMAIN, 1, 1);
Strings::includeMyCSS([
    '/assets/styles/common.css',
    '/assets/styles/multi_select.css'
]);

loanForm::init($PCID, $fileTab);

if ($selClientId > 0) {
    if ($userRole == 'Client') {
        $title = 'My Profile';
    } else {
        $title = 'Update Borrower Profile';
    }
} else {
    $title = 'Borrower Registration';
}

/* Dynamic $Breadcrumb */
$Breadcrumb = [];
$Breadcrumb['icon'] = 'fas fa-users icon-md';
$Breadcrumb['breadcrumbList'] = [['href' => glUserRole::getUserBaseURL() . 'clients', 'title' => 'Borrower/Client List'], ['href' => '#', 'title' => $title]];
$Breadcrumb['toolbarHTML'] = '';

require('../includesNew/_page-body-loader.php');
require('../includesNew/_layoutOpen.php');
?>
<?php
$tabNumb = 1;
$selExecutiveId = 0;

if (isset($_REQUEST['encEId'])) $selExecutiveId = cypher::myDecryption(trim($_REQUEST['encEId']));
if (isset($_REQUEST['tabNumb'])) $tabNumb = trim($_REQUEST['tabNumb']);

?>
<script type="text/javascript">

    /**
     * This function validates 'Contact Info' form in 'Borrower Registration' tab.
     *
     * It checks if mandatory fields are empty then shows appropriate validation messages and returns false else returns true.
     *

     */
    function validateClientForm() {
        if (
            chkIsBlank('clientRegForm', 'clientFName', 'Please Enter the First Name')
            && chkIsBlank('clientRegForm', 'clientLName', 'Please Enter the Last Name')
            && isEmailOk('clientRegForm', 'clientEmail')
            && chkIsBlank('clientRegForm', 'referrerPLOCode', 'Please Select Branch')
            && chkIsBlank('clientRegForm', 'referrerAffCode', 'Please Select Loan Officer/Broker')
        ) {
            return true;
        } else {
            return false;
        }
    }

    $(document).ready(function () {
        $('#branchName').autocomplete({
            serviceUrl: '/JQFiles/getBranchesCodeForJQuery.php?PCID=<?php  echo cypher::myEncryption($assignedPCID)?>&userType=<?php  echo cypher::myEncryption('PLO')?>',
            minChars: 2,
            onSelect: function (value, data) {
                $('#branchName').val(replaceXMLProcess(value));
                document.clientRegForm.referrerPLOCode.value = data;
            }
        });

        $('#agentName').autocomplete({
            serviceUrl: '/JQFiles/getBrokerCode.php?PCID=<?php  echo cypher::myEncryption($assignedPCID)?>',
            minChars: 2,
            onSelect: function (value, data) {
                $('#agentName').val(replaceXMLProcess(value));
                document.clientRegForm.referrerAffCode.value = data;
            }
        });
    });
</script>
<div style="float:right;display:block;" id="divLoader"><img src="<?php echo IMG_PROGRESS_BAR; ?>" alt=""></div>

<?php require 'clientCreateForm.php'; ?>

<?php
require('../includesNew/_layoutClose.php');
require 'adminFooter.php';
echo BaseHTML::closePage();

Strings::includeMyScript([
    '/assets/js/ccValidation.js',
    '/assets/js/clientCreate.js',
    '/assets/js/clientListInfo.js',
    '/assets/js/models/Dates.js',
    '/assets/js/models/formValue.js',
    '/assets/js/fileCommon.js',
    '/assets/js/HMLOLoanInfo.js',
    '/assets/js/LMRequest.js',
    '/backoffice/api_v2/js/address_lookup.js',
    CONST_BO_URL . 'LMRequest/js/debugTools.js',
]);
if ($tabNumb == 6) {
    Strings::includeMyScript([
        '/assets/js/incExp.js',
        '/assets/js/LMRequest.js'
    ]);
}
require('../includesNew/_customPage.php');
?>
<input type="hidden" disabled id="client_info_form_original_data" value=""/>

<div class="pad5 right">
    <?php
    echo Strings::DisplayMessage(Strings::GetSess('msg'));
    Strings::SetSess('msg', '');
    ?>
</div>

<script>
    $(".mask_phone:enabled").inputmask("mask", {mask: "(999) 999 - 9999 Ext 9999"});
    $(".mask_cell:enabled").inputmask("mask", {mask: "999 - 999 - 9999"});
    $(".mask_ssn:enabled").inputmask("999 - 99 - 9999", {placeholder: "___ - __ - ____", clearMaskOnLostFocus: !0});
    $(".mask_date:enabled").inputmask("m/d/y", {autoUnmask: !0});
    $(".mask_home_phone:enabled").inputmask("mask", {mask: "999 - 999 - 9999"});
    var client_info_form_original_data = $(".clientRegForm").serialize();

    function submitAndNavigateTab(selClientId, selExecutiveId, tabOpt) {
        // Validates 'Contact Info' form when user switches over tabs in 'Borrower Registration' or 'Update Borrower Profile' pages.
        if ($("#tabOpt").val() == 1 || $("#tabOpt").val() == '') {
            if (!validateClientForm()) {
                return false;
            }
        }
        client_info_form_original_data = $('#client_info_form_original_data').val();
        console.log($(".clientRegForm").serialize());
        console.log(client_info_form_original_data);

        //return false;
        if ($(".clientRegForm").serialize() != client_info_form_original_data) {
            showDialogBox(selClientId, selExecutiveId, tabOpt);
            $("#tabOpt").val(tabOpt);
            $("#but_search").val("Save & Next");
        } else {
            showTab(selClientId, selExecutiveId, tabOpt);
        }
    }

</script>

<script type="text/javascript">
    $(document).ready(function () {
        $('#clientRegForm input[type="submit"]').attr('disabled', 'disabled');

        $('#clientRegForm').bind('keyup change', function (e) {
            client_info_form_original_data = $('#client_info_form_original_data').val();
            if ($("#clientRegForm").serialize() != client_info_form_original_data || (e.target.type == 'file' && e.type == 'change')) {
                e.preventDefault(); // <-- important
                /* var f = '';
                try { f = e.target.id; } catch (e){}
                if (f != 'LMRProcessorStatus') { */
                enableSaveButton();
                //}
            } else {
                $('#clientRegForm input[type="submit"]').attr('disabled', 'disabled');
            }
        });
        //this is for y/n toggle
        $('.switch-icon').click(function (e) {
            enableSaveButton();
        });
        $('#client_info_form_original_data').val($(".clientRegForm").serialize());
    });

    function enableSaveButton() {
        $('#clientRegForm input[type="submit"]').removeAttr('disabled');
    }

    function showDialogBox(selClientId, selExecutiveId, tabOpt) {
        $.confirm({
            icon: 'fa fa-warning',
            closeIcon: true,
            title: 'Confirm',
            content: "Would you like to save the data before leaving this tab?",
            type: 'red',
            backgroundDismiss: true,
            buttons: {
                yes: {
                    btnClass: 'btn-blue',
                    action: function () {
                        document.clientRegForm.submit();
                    }
                },
                No: {
                    btnClass: 'btn-red',
                    action: function () {
                        showTab(selClientId, selExecutiveId, tabOpt);
                    }
                },
                cancel: {
                    text: 'Cancel',
                    action: function () {
                        //  location.reload();
                    }
                },
            }
        });
    }

    function populateBranchID(value, targetField) {
        $('#' + targetField).val(value);
    }

    function checkPropInfo(val) {
        if (val == 47) {
            $('#propInfoDiv').show();
        } else {
            $('#propInfoDiv').hide();
        }
    }
</script>

<!-- clientCreate.php -->
