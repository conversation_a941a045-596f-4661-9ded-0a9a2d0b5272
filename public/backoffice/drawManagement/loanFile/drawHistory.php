
<?php
    $historyData = [];
    $historyRecords = $drawRequestManager->getDrawRequestHistory();
    foreach ($historyRecords as $historyRecord) {
        $historyData[] = $historyRecord->toArray();
    }
?>
<?php if (empty($historyData)) return; ?>
<div class="card card-custom card-stretch d-flex p-0 mt-4">
    <div class="card-header card-header-tabs-line bg-gray-100">
        <div class="card-title">
            <h3 class="card-label">
                Draw Request History
            </h3>
        </div>
        <div class="card-toolbar">
            <a href="javascript:void(0);"
                class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                data-card-tool="toggle" data-section="drawHistoryCard" data-toggle="tooltip" data-placement="top"
                title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
        </div>
    </div>

    <div class="card-body p-2">
        <div class="row">
            <div class="col-12">
                <div class="work-table">
                    <table class="table table-striped table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Submission Date</th>
                                <th>Amount Requested</th>
                                <th>Amount Approved</th>
                                <th>Wire Amount</th>
                                <th>Wire Sent</th>
                                <th style="width: 50px"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($historyData as $history): ?>
                            <tr>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    $statusText = ucfirst($history['status']);
                                    switch($history['status']) {
                                        case 'pending':
                                            $statusClass = 'badge-warning';
                                            $statusText = 'Submitted';
                                            break;
                                        case 'approved':
                                            $statusClass = 'badge-success';
                                            $statusText = 'Approved';
                                            break;
                                        case 'rejected':
                                            $statusClass = 'badge-danger';
                                            $statusText = 'Rejected';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass; ?>"><?= $statusText; ?></span>
                                </td>
                                <td><?= $history['submittedAt'] ? date('M j, Y g:i A', strtotime($history['submittedAt'])) : '-'; ?></td>
                                <td>$<?= number_format($history['amountRequested'], 2); ?></td>
                                <td>
                                    <?php if ($history['status'] === 'approved' && $history['amountApproved'] > 0): ?>
                                        $<?= number_format($history['amountApproved'], 2); ?>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($history['wireAmount']): ?>
                                        $<?= number_format($history['wireAmount'], 2); ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($history['wireSentDate']): ?>
                                        <?= date('M j, Y', strtotime($history['wireSentDate'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="javascript:void(0);"
                                       class="btn btn-icon btn-light-primary btn-sm text-primary view-details"
                                       title="View Details"
                                       data-history-id="<?= $history['id']; ?>"
                                       data-toggle="modal"
                                       data-target="#drawHistoryDetailsModal">
                                        <i class="fas fa-eye fa-lg"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Draw History Details Modal -->
<div class="modal fade" id="drawHistoryDetailsModal" tabindex="-1" role="dialog" aria-labelledby="drawHistoryDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style="max-width: 90vw; height: 90vh;">
        <div class="modal-content" style="height: 100%;">
            <div class="modal-header">
                <h5 class="modal-title" id="drawHistoryDetailsModalLabel">Draw Request Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="overflow-y: auto; padding: 20px;">
                <div id="drawHistoryDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">Loading draw request details...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('.view-details').on('click', function() {
        var historyId = $(this).data('history-id');

        // Reset modal content
        $('#drawHistoryDetailsContent').html(`
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Loading draw request details...</p>
            </div>
        `);

        // Load draw request details via AJAX
        $.ajax({
            url: '/backoffice/api/get_draw_history_details.php',
            type: 'GET',
            data: { historyId: historyId },
            success: function(response) {
                if (response.success) {
                    $('#drawHistoryDetailsContent').html(response.html);
                } else {
                    $('#drawHistoryDetailsContent').html(`
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            Error loading draw request details: ${response.message || 'Unknown error'}
                        </div>
                    `);
                }
            },
            error: function(xhr, status, error) {
                $('#drawHistoryDetailsContent').html(`
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        Error loading draw request details. Please try again.
                    </div>
                `);
            }
        });
    });
});
</script>
