<?php
global $fileTab, $fieldsInfo, $proInsFirstName, $publicUser, $allowToEdit,
       $PCID, $tabIndex, $fldArr, $stateArray, $op,
       $insuranceAgentInfoArray, $LMRId;

use models\Controllers\backoffice\LMRequest;
use models\Controllers\loanForm;
use models\CustomField;
use models\cypher;
use models\lendingwise\tblContacts;
use models\lendingwise\tblFile;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Strings;

if (!LMRequest::myFileInfo()) {
    LMRequest::setLMRId($LMRId);
}
$fileContacts = LMRequest::myFileInfo()->fileContacts()->multiContact;
if ($fileContacts['Insurance Rep']) {
    $insuranceAgentsInfo = $fileContacts['Insurance Rep'];
} else {
    $insuranceAgentsInfo = [
        new tblContacts(),
    ];
}

$secArr = BaseHTML::sectionAccess2(['sId' => 'ICI', 'opt' => $fileTab]);
loanForm::pushSectionID('ICI');

?>
<!-- insuranceInfo.php -->
<div class="card card-custom ICI HMLOLoanInfoSections insuranceAgentInfoSectionCard <?php if (count(Arrays::getValueFromArray('ICI', $fieldsInfo)) <= 0) {
    echo 'secHide';
} ?>" style="<?php echo $HMLOLoanInfoSectionsDisp; ?>">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">
                <?php echo BaseHTML::getSectionHeading('ICI'); ?>
            </h3>
            <?php if (trim(BaseHTML::getSectionTooltip('ICI')) != '') { ?>&nbsp;
                <i class="popoverClass fas fa-info-circle text-primary "
                   data-html="true"
                   data-content="<?php echo BaseHTML::getSectionTooltip('ICI'); ?>"></i>
            <?php } ?>
        </div>
        <div class="card-toolbar">
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="toggle"
                  data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
            <span class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none cursor-pointer"
                  data-card-tool="reload"
                  data-toggle="tooltip" data-placement="top" title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </span>
            <?php if ($allowToEdit) { ?>
                <span class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass cloneFormSection cursor-pointer"
                      onclick="SectionForm.cloneFormSection(this)"
                      data-clone-section="insuranceSection"
                      data-increment-section="incrementClassINS"
                      title="Click to add new <?php echo BaseHTML::getSectionHeading('ICI'); ?>.">
                    <i class=" icon-md fas fa-plus "></i>
                </span>
            <?php } ?>
            <span
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass cursor-pointer"
                    data-card-tool="toggle"
                    data-section="insuranceAgentInfoSectionCard"
                    data-toggle="tooltip" data-placement="top" title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </span>
        </div>
    </div>
    <div class="card-body insuranceAgentInfoSectionCard_body">
        <?php
        $ins = 1;

        foreach ($insuranceAgentsInfo as $insuranceAgentInfo) {
            ?>
            <div class="card card-custom mb-2 insuranceSection" id="insuranceSectionId_<?php echo $ins; ?>">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            <?php echo BaseHTML::getSectionHeading('ICI'); ?>
                            <span class="incrementClassINS"><?php echo $ins; ?> </span>
                            <?php //echo($insuranceAgentInfo->contactName ? ' (' . $insuranceAgentInfo->contactName . ' ' . $insuranceAgentInfo->contactLName . ' )' : '') ?>
                        </h3>
                    </div>
                    <div class="card-toolbar ">
                        <span class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                              data-card-tool="toggle"
                              data-section="refinanceSectionCard"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </span>
                        <span href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none"
                              data-card-tool="reload"
                              data-toggle="tooltip" data-placement="top" title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </span>
                        <span class="btn btn-icon btn-sm btn-hover-light-primary d-none"
                              data-card-tool="remove"
                              data-toggle="tooltip"
                              data-placement="top"
                              title="Remove Card">
                            <i class="ki ki-close icon-nm"></i>
                        </span>
                    </div>
                </div>
                <div class="card-body ">
                    <div class="row">
                        <input type="hidden"
                               name="insuranceAgentFields[<?php echo $ins; ?>][id]"
                               id="insuranceCompanyID_<?php echo $ins; ?>"
                               class="insuranceAgentIdClass"
                               value="<?php echo ($insuranceAgentInfo->CID); ?>"/>
                        <div class="col-md-6 proInsFirstName_disp <?php echo loanForm::showField('proInsFirstName'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proInsFirstName', 'col-md-5', '',
                                    loanForm::changeLog(
                                        $insuranceAgentInfo->CID,
                                        'contactName',
                                        \models\lendingwise\tblContacts::class,
                                        'Insurance Rep First Name'
                                    ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <div class="input-group">
                                            <input class="form-control input-sm insuranceAgentFirstName <?php if ($PCID != '3572' && $publicUser == 0) { ?> loadInsuranceAgents   <?php } ?> insPrimContactCls<?php echo $ins; ?>
                                            <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsFirstName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                   type="text"
                                                   name="insuranceAgentFields[<?php echo $ins; ?>][proInsFirstName]"
                                                   id="proInsFirstName_<?php echo $ins; ?>"
                                                   value="<?php echo htmlspecialchars($insuranceAgentInfo->contactName); ?>"
                                                   size="20" onblur="closeSuggestionDropdown();"
                                                   placeholder=" - Type Name Here - "
                                                   maxlength="68"
                                                   TABINDEX="<?php echo $tabIndex++; ?>"
                                                   autocomplete="off"
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsFirstName', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                            <div class="input-group-append">
                                                <div class="input-group-text">
                                                            <span class="icon-red fa fa-times valign-middle clearInsuranceAgent"
                                                                  title="Click to Clear Insurance info"></span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->contactName; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 proInsLastName_disp <?php echo loanForm::showField('proInsLastName'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proInsLastName', 'col-md-5', '',
                                    loanForm::changeLog(
                                        $insuranceAgentInfo->CID,
                                        'contactLName',
                                        \models\lendingwise\tblContacts::class,
                                        'Insurance Rep Last Name'
                                    ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input class="form-control input-sm insuranceAgentLastName insPrimContactCls<?php echo $ins; ?>         <?php if ($publicUser != 1 && $PCID != '3572') { ?> loadInsuranceAgents    <?php } ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsLastName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               type="text"
                                               name="insuranceAgentFields[<?php echo $ins; ?>][proInsLastName]"
                                               id="proInsLastName_<?php echo $ins; ?>"
                                               value="<?php echo htmlspecialchars($insuranceAgentInfo->contactLName); ?>"
                                               size="20" onblur="closeSuggestionDropdown();"
                                               placeholder=" - Type Name Here - "
                                               maxlength="68"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsLastName', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    <?php } else { ?>
                                        <h5><?php echo $insuranceAgentInfo->contactLName; ?></h5>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6 proInsName_disp <?php echo loanForm::showField('proInsName'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proInsName', 'col-md-5', '', loanForm::changeLog(
                                    $insuranceAgentInfo->CID,
                                    'companyName',
                                    \models\lendingwise\tblContacts::class,
                                    'Name Of Company'
                                ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text"
                                               class="form-control input-sm <?php if ($PCID != '3572' && $publicUser == 0) { ?> loadInsuranceAgents   <?php } ?>  insuranceAgentCompanyName insPrimContactCls<?php echo $ins; ?> <?php echo BaseHTML::checkMan($fldArr, $fileTab) ?>"
                                               name="insuranceAgentFields[<?php echo $ins; ?>][proInsName]"
                                               id="proInsName_<?php echo $ins; ?>"
                                               value="<?php echo htmlspecialchars($insuranceAgentInfo->companyName); ?>"
                                               size="20" onblur="closeSuggestionDropdown();"
                                               placeholder=" - Type Name Here - "
                                               maxlength="68"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsName', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->companyName; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 proIncEmail_disp <?php echo loanForm::showField('proIncEmail'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proIncEmail', 'col-md-5', '',
                                    loanForm::changeLog(
                                        $insuranceAgentInfo->CID,
                                        'email',
                                        \models\lendingwise\tblContacts::class,
                                        'E-mail'
                                    ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="email"
                                               class="form-control input-sm insContactCls<?php echo $ins; ?> insuranceAgentEmail <?php echo BaseHTML::fieldAccess(['fNm' => 'proIncEmail', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="insuranceAgentFields[<?php echo $ins; ?>][proIncEmail]"
                                               id="proIncEmail_<?php echo $ins; ?>"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               value="<?php echo $insuranceAgentInfo->email; ?>"
                                               size="20"
                                               onblur="fieldValidation(this.id,this.name);"
                                               maxlength="50"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'proIncEmail', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->email; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6 proIncPh_disp <?php echo loanForm::showField('proIncPh'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proIncPh', 'col-md-5', '',
                                    loanForm::changeLog(
                                        $insuranceAgentInfo->CID,
                                        'phone',
                                        \models\lendingwise\tblContacts::class,
                                        'Phone'
                                    ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input class="form-control input-sm mask_phone insuranceAgentPhone insContactCls<?php echo $ins; ?> <?php echo BaseHTML::fieldAccess(['fNm' => 'proIncPh', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="insuranceAgentFields[<?php echo $ins; ?>][proIncPh]"
                                               id="proIncPh_<?php echo $ins; ?>"
                                               tabindex="<?php echo $tabIndex++; ?>"
                                               value="<?php echo Strings::formatPhoneNumber($insuranceAgentInfo->phone); ?>"
                                               autocomplete="off"
                                               type="text"
                                               placeholder="(___) ___ - ____ Ext ____" <?php echo BaseHTML::fieldAccess(['fNm' => 'proIncPh', 'sArr' => $secArr, 'opt' => 'I']); ?>/>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->phone; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 proInsAddress_disp <?php echo loanForm::showField('proInsAddress'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proInsAddress', 'col-md-5', '',
                                    loanForm::changeLog(
                                        $insuranceAgentInfo->CID,
                                        'address',
                                        \models\lendingwise\tblContacts::class,
                                        'Address'
                                    ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text"
                                               class="form-control input-sm insContactCls<?php echo $ins; ?> insuranceAgentAddress <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsAddress', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="insuranceAgentFields[<?php echo $ins; ?>][proInsAddress]"
                                               id="proInsAddress_<?php echo $ins; ?>"
                                               value="<?php echo htmlspecialchars($insuranceAgentInfo->address); ?>"
                                               size="50"
                                               maxlength="75"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsAddress', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->address; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6 proInsCity_disp <?php echo loanForm::showField('proInsCity'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proInsCity', 'col-md-5', '',
                                    loanForm::changeLog(
                                        $insuranceAgentInfo->CID,
                                        'city',
                                        \models\lendingwise\tblContacts::class,
                                        'City'
                                    ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text"
                                               class="form-control input-sm insContactCls<?php echo $ins; ?>  insuranceAgentCity <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsCity', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="insuranceAgentFields[<?php echo $ins; ?>][proInsCity]"
                                               id="proInsCity_<?php echo $ins; ?>"
                                               value="<?php echo htmlspecialchars($insuranceAgentInfo->city); ?>"
                                               size="20"
                                               maxlength="30"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsCity', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->city; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6 proInsState_disp <?php echo loanForm::showField('proInsState'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proInsState', 'col-md-5', '',
                                    loanForm::changeLog(
                                        $insuranceAgentInfo->CID,
                                        'state',
                                        \models\lendingwise\tblContacts::class,
                                        'State'
                                    ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <select class="form-control input-sm insContactCls<?php echo $ins; ?> insuranceAgentState <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsState', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="insuranceAgentFields[<?php echo $ins; ?>][proInsState]"
                                                id="proInsState_<?php echo $ins; ?>"
                                                TABINDEX="<?php echo $tabIndex++; ?>">
                                            <option value=""> - Select -</option>
                                            <?php
                                            foreach ($stateArray as $eachState) {
                                                echo "<option value=\"" . trim($eachState['stateCode']) . "\" " . Arrays::isSelected(trim($eachState['stateCode']), $insuranceAgentInfo->state) . '>' . trim($eachState['stateName']) . '</option>';
                                            }
                                            ?>
                                        </select>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->state; ?></b>
                                        <?php
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 proInsZip_disp <?php echo loanForm::showField('proInsZip'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proInsZip', 'col-md-5', '',
                                    loanForm::changeLog(
                                        $insuranceAgentInfo->CID,
                                        'zip',
                                        \models\lendingwise\tblContacts::class,
                                        'Zip'
                                    ), $ins); ?>
                                <div class="col-md-7">
                                    <?php
                                    if ($allowToEdit) { ?>
                                        <input type="text"
                                               class="form-control zipCode insContactCls<?php echo $ins; ?> input-sm insuranceAgentZip <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsZip', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               name="insuranceAgentFields[<?php echo $ins; ?>][proInsZip]"
                                               id="proInsZip_<?php echo $ins; ?>"
                                               value="<?php echo htmlspecialchars($insuranceAgentInfo->zip); ?>"
                                               size="20"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsZip', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->zip; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 <?php echo loanForm::showField('proIncWebsite'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proIncWebsite', 'col-md-5', '', loanForm::changeLog(
                                    $insuranceAgentInfo->CID,
                                    'website',
                                    \models\lendingwise\tblContacts::class,
                                    'Website'
                                ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <input type="text"
                                               class="form-control input-sm insContactCls<?php echo $ins; ?>  insuranceAgentWebSite <?php echo BaseHTML::fieldAccess(['fNm' => 'proIncWebsite', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                               placeholder="https://"
                                               name="insuranceAgentFields[<?php echo $ins; ?>][proIncWebsite]"
                                               id="proIncWebsite_<?php echo $ins; ?>"
                                               value="<?php echo htmlspecialchars($insuranceAgentInfo->website); ?>"
                                               size="20"
                                               maxlength="60"
                                               TABINDEX="<?php echo $tabIndex++; ?>"
                                               autocomplete="off" <?php echo BaseHTML::fieldAccess(['fNm' => 'proIncWebsite', 'sArr' => $secArr, 'opt' => 'I']); ?>>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->website; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                        <div class=" col-md-6 <?php echo loanForm::showField('proIncRepNotes'); ?>">
                            <div class="form-group row">
                                <?php echo loanForm::label('proIncRepNotes', 'col-md-5', '', loanForm::changeLog(
                                    $insuranceAgentInfo->CID,
                                    'description',
                                    \models\lendingwise\tblContacts::class,
                                    'Insurance Rep Notes'
                                ), $ins); ?>
                                <div class="col-md-7">
                                    <?php if ($allowToEdit) { ?>
                                        <textarea
                                                class="form-control input-sm insuranceAgentNotes insContactCls<?php echo $ins; ?> proIncRepNotes validateMaxLength
                                                <?php echo BaseHTML::fieldAccess(['fNm' => 'proInsFirstName', 'sArr' => $secArr, 'opt' => 'M']); ?>"
                                                name="insuranceAgentFields[<?php echo $ins; ?>][proIncRepNotes]"
                                                id="proIncRepNotes_<?php echo $ins; ?>"
                                                maxlength="<?php echo loanForm::getFieldLength('description','tblContacts'); ?>"
                                                TABINDEX="<?php echo $tabIndex++; ?>"
                                        <?php echo BaseHTML::fieldAccess(['fNm' => 'proIncWebsite', 'sArr' => $secArr, 'opt' => 'I']); ?>><?php echo $insuranceAgentInfo->description; ?></textarea>
                                    <?php } else { ?>
                                        <b><?php echo $insuranceAgentInfo->description; ?></b>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>


                        <?php if ($allowToEdit) { ?>
                            <div class=" col-md-12">
                                <label class="font-weight-bold" style="color: #0462b3"> Add
                                    New <?php echo BaseHTML::getSectionHeading('ICI') ?> </label>
                                <a class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass <?php if ($ins < sizeof($insuranceAgentsInfo)) {
                                    echo 'd-none';
                                } ?> cloneFormSection addButton<?php echo $ins; ?>"
                                   onclick="SectionForm.cloneFormSection(this)"
                                   href="javascript:void(0)"
                                   data-clone-section="insuranceSection"
                                   data-increment-section="incrementClassINS"
                                   title="Click to add new <?php echo BaseHTML::getSectionHeading('ICI'); ?>.">
                                    <i class=" icon-md fas fa-plus "></i>
                                </a>
                                <a class="btn btn-sm btn-danger btn-text-primary  btn-icon ml-2 tooltipClass removeFromSection removeButton<?php echo $ins; ?>"
                                   href="javascript:void(0)"
                                   onclick="SectionForm.removeFromSection(this)"
                                   data-clone-section="insuranceSection"
                                   data-increment-section="incrementClassINS"
                                   data-page="deleteInsuranceAgent.php"
                                   data-id="<?php echo cypher::myEncryption($insuranceAgentInfo->CID); ?>"
                                   title="Click to Remove <?php echo BaseHTML::getSectionHeading('ICI'); ?>.">
                                    <i class=" icon-md fas fa-minus-circle "></i>
                                </a>
                            </div>
                        <?php } ?>

                    </div>
                </div>
            </div>

            <?php $ins++;
        } ?>

        <?php echo CustomField::RenderForTabSection(
            PageVariables::$PCID,
            tblFile::class,
            LMRequest::$LMRId,
            'ICI',
            $fileTab,
            $activeTab,
            LMRequest::myFileInfo()->getFileTypes(),
            LMRequest::myFileInfo()->getLoanPrograms()
        ); ?>
    </div>
</div>
<script src="/backoffice/LMRequest/js/insuranceAgentInfo.js?<?php echo CONST_JS_VERSION; ?>"></script>
<!-- insuranceInfo.php -->
