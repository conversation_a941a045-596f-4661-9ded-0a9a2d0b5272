<?php
session_start();
require __DIR__ . '/../../includes/util.php';
require __DIR__ . '/../initPageVariables.php';
require __DIR__ . '/../getPageVariables.php';

use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\DrawRequestsHistory;
use models\composite\oDrawManagement\DrawRequest;
use models\standard\UserAccess;

// Check user access
UserAccess::CheckAdminUse();

// Set JSON response header
header('Content-Type: application/json');

try {

    // Get history ID from request
    $historyId = $_GET['historyId'] ?? null;
    if (!$historyId || !is_numeric($historyId)) {
        throw new Exception('Invalid history ID');
    }

    // Get the history record
    $historyRecord = getDrawHistoryRecord((int)$historyId);
    if (!$historyRecord) {
        throw new Exception('History record not found');
    }

    // Get the original draw request data for categories and line item details
    $drawRequestManager = DrawRequestManager::forLoanFile($historyRecord['LMRId']);
    $drawRequest = $drawRequestManager->getDrawRequest();

    if (!$drawRequest) {
        throw new Exception('Draw request not found');
    }

    // Combine the data
    $combinedData = combineDrawRequestWithHistory($drawRequest, $historyRecord);

    // Generate the HTML
    $html = generateDrawHistoryDetailsHTML($combinedData, $historyRecord);

    echo json_encode([
        'success' => true,
        'html' => $html
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get draw history record by ID
 */
function getDrawHistoryRecord(int $historyId): ?array {
    $historyData = \models\lendingwise\tblDrawRequests_h::Get(['id' => $historyId]);
    if (!$historyData) {
        return null;
    }

    $history = new DrawRequestsHistory($historyData);
    $historyArray = $history->toArray();

    // Get LMRId from the original draw request
    $drawRequestData = \models\lendingwise\tblDrawRequests::Get(['id' => $historyArray['drawId']]);
    if ($drawRequestData) {
        $historyArray['LMRId'] = $drawRequestData->LMRId;
    }

    return $historyArray;
}

/**
 * Combine draw request data with history data
 */
function combineDrawRequestWithHistory(DrawRequest $drawRequest, array $historyRecord): array {
    $drawRequestData = $drawRequest->toArray();
    $historyLineItems = $historyRecord['lineItems'] ?? [];

    // Create a lookup array for history line items by lineItemId
    $historyLookup = [];
    foreach ($historyLineItems as $historyItem) {
        $historyLookup[$historyItem['lineItemId']] = $historyItem;
    }

    // Combine the data
    $combinedCategories = [];
    foreach ($drawRequestData['categories'] as $category) {
        $combinedCategory = $category;
        $combinedLineItems = [];

        foreach ($category['lineItems'] as $lineItem) {
            $combinedLineItem = $lineItem;

            // Override with history data if available
            if (isset($historyLookup[$lineItem['id']])) {
                $historyData = $historyLookup[$lineItem['id']];
                $combinedLineItem['completedAmount'] = $historyData['completedAmount'];
                $combinedLineItem['completedPercent'] = $historyData['completedPercent'];
                $combinedLineItem['requestedAmount'] = $historyData['requestedAmount'];
                $combinedLineItem['disbursedAmount'] = $historyData['disbursedAmount'];
            }

            $combinedLineItems[] = $combinedLineItem;
        }

        $combinedCategory['lineItems'] = $combinedLineItems;
        $combinedCategories[] = $combinedCategory;
    }

    return [
        'categories' => $combinedCategories,
        'status' => $historyRecord['status'],
        'submittedAt' => $historyRecord['submittedAt'],
        'amountRequested' => $historyRecord['amountRequested'],
        'amountApproved' => $historyRecord['amountApproved']
    ];
}

/**
 * Generate HTML for draw history details
 */
function generateDrawHistoryDetailsHTML(array $combinedData, array $historyRecord): string {
    $statusClass = '';
    $statusText = ucfirst($historyRecord['status']);
    switch($historyRecord['status']) {
        case 'pending':
            $statusClass = 'badge-warning';
            $statusText = 'Submitted';
            break;
        case 'approved':
            $statusClass = 'badge-success';
            $statusText = 'Approved';
            break;
        case 'rejected':
            $statusClass = 'badge-danger';
            $statusText = 'Rejected';
            break;
    }

    ob_start();
    ?>
    <div class="draw-history-details">
        <!-- Header Information -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Request Information</h6>
                        <p><strong>Status:</strong> <span class="badge <?= $statusClass; ?>"><?= $statusText; ?></span></p>
                        <p><strong>Submission Date:</strong> <?= $historyRecord['submittedAt'] ? date('M j, Y g:i A', strtotime($historyRecord['submittedAt'])) : '-'; ?></p>
                        <p><strong>Amount Requested:</strong> $<?= number_format($historyRecord['amountRequested'], 2); ?></p>
                        <?php if ($historyRecord['status'] === 'approved' && $historyRecord['amountApproved'] > 0): ?>
                            <p><strong>Amount Approved:</strong> $<?= number_format($historyRecord['amountApproved'], 2); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Line Items Table -->
        <?php if (!empty($combinedData['categories'])): ?>
            <div class="table-responsive">
                <table class="table table-bordered line-item-table">
                    <thead class="thead-light">
                        <colgroup>
                            <col style="width:25%">
                            <col style="width:15%">
                            <col style="width:15%">
                            <col style="width:15%">
                            <col style="width:10%">
                            <col style="width:20%">
                        </colgroup>
                        <tr>
                            <th scope="col">Line Item</th>
                            <th scope="col">Total Budget</th>
                            <th scope="col">Completed Renovations</th>
                            <th scope="col">Disbursed Amount</th>
                            <th scope="col">% Completed</th>
                            <th scope="col">Requested Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($combinedData['categories'] as $category): ?>
                            <?php if (!empty($category['lineItems'])): ?>
                                <!-- Category Header -->
                                <tr class="category-header">
                                    <td colspan="6" class="bg-light">
                                        <strong><?= htmlspecialchars(strtoupper($category['name'])) ?></strong>
                                        <?php if (!empty($category['description'])): ?>
                                            <i class="fa fa-info-circle text-primary ml-2" title="<?= htmlspecialchars($category['description']) ?>"></i>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <!-- Line Items -->
                                <?php foreach ($category['lineItems'] as $lineItem): ?>
                                    <tr class="line-item">
                                        <td>
                                            <?= htmlspecialchars($lineItem['name']) ?>
                                            <?php if (!empty($lineItem['description'])): ?>
                                                <i class="fa fa-info-circle text-primary ml-2" title="<?= htmlspecialchars($lineItem['description']) ?>"></i>
                                            <?php endif; ?>
                                        </td>
                                        <td>$<?= number_format($lineItem['cost'], 2) ?></td>
                                        <td>$<?= number_format($lineItem['completedAmount'], 2) ?></td>
                                        <td>$<?= number_format($lineItem['disbursedAmount'], 2) ?></td>
                                        <td>
                                            <span class="badge badge-info"><?= round($lineItem['completedPercent']) ?>%</span>
                                        </td>
                                        <td>$<?= number_format($lineItem['requestedAmount'], 2) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle"></i>
                No line items data available for this draw request.
            </div>
        <?php endif; ?>
    </div>

    <style>
    .draw-history-details .table {
        font-size: 0.9rem;
    }

    .draw-history-details .category-header td {
        background-color: #f8f9fa !important;
        font-weight: bold;
        border-top: 2px solid #dee2e6;
    }

    .draw-history-details .line-item td {
        vertical-align: middle;
    }

    .draw-history-details .card {
        border: 1px solid #e3e6f0;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .draw-history-details .card-title {
        color: #5a5c69;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    </style>
    <?php
    return ob_get_clean();
}
?>
