<?php
global $userGroup, $PCID, $userNumber, $userRole, $selClientId, $oBranch, $oBroker,
       $assignedPCID, $executiveId, $agentNo, $assignedProcCompId, $oLoanMod, $myTimeZone,
       $isClient, $promoCode, $clientFName, $clientLName, $borrowerDOB;

global $borrowerPOB, $ssnNumber, $clientPhone, $clientCell, $serviceProvider, $clientEmail,
       $clientSecondaryEmail, $clientAddress, $clientCity,
       $stateArray, $clientState, $clientZip, $tabIndex, $PCquickAppFieldsInfo;
global $methodContactArray, $allowToEdit, $driverLicenseState, $driverLicenseNumber,
       $sendMarketingEmail, $allowToViewMarketPlace, $allowToSubmitOffer,
       $userEmail, $PCModulesArray, $clientSelectBranch;

use models\composite\oBranch\getBranches;
use models\composite\oBranch\getBranchesForAgent;
use models\composite\oBranch\getPromoCodeForBranch;
use models\composite\oBroker\getPCAgents;
use models\composite\oBroker\getPromoCodeForAgent;
use models\composite\oEmployee\getEmployeeInfo;
use models\composite\oLoanMod\getPLOClientAgent;
use models\composite\oLoanMod\getPLONameAndReferralCode;
use models\constants\gl\glDate;
use models\constants\gl\glUserGroup;
use models\constants\gl\glUserRole;
use models\constants\methodOfContactArray;
use models\constants\SMSServiceProviderArray;
use models\constants\timeZoneArray;
use models\cypher;
use models\standard\Arrays;
use models\standard\BaseHTML;
use models\standard\Dates;
use models\standard\Strings;

$SMSServiceProviderArray = SMSServiceProviderArray::$SMSServiceProviderArray;
$methodOfContactArray = methodOfContactArray::$methodOfContactArray;
$timeZoneArray = timeZoneArray::$timeZoneArray;

if ($userGroup == 'Employee') {

    $ip = ['PCID' => $PCID, 'processorId' => $userNumber];


    $employeeInfo = getEmployeeInfo::getReport($ip);
    $LMRAEIDsArray = $employeeInfo['LMRAEIDInfoArray'];

    if ($userNumber > 0) {
        if (count($LMRAEIDsArray) > 0) {
            $tempArray = [];
            $LMRAEIDs = '';
            $tempArray = $LMRAEIDsArray[$userNumber] ?? [];

            for ($pl = 0; $pl < count($tempArray); $pl++) {
                $LMRAEId = 0;
                $LMRAEId = $tempArray[$pl]['LMRAEID'];
                if ($pl > 0) {
                    $LMRAEIDs .= ',';
                }
                $LMRAEIDs .= $LMRAEId;
            }
        }

        if (trim($LMRAEIDs) == '') {
            $LMRAEIDs = 0;
        }
    }

}

if ($userRole == 'Branch' && !($selClientId > 0)) {
    $LMRAEIDs = $userNumber;
}

/* Get Assigned Employee Branchs And Agent */
//if($LMRAEIDs > 0) {

$myOptNewArray = ['PCID' => $PCID, 'opt' => 'list'];

if ($LMRAEIDs > 0) {
    $myOptNewArray['execID'] = $LMRAEIDs;
} //https://www.pivotaltracker.com/story/show/*********/comments/*********

$LMRInfoArray = [];

$branchList = getBranches::getReport($myOptNewArray);
if (count($branchList) > 0) {
    $LMRInfoArray = $branchList['branchList'];
}

if ($userRole == 'Agent') {
    $tempArrBroker = [];
    $tempArrBroker['brokerNumber'] = $userNumber;
    $LMRInfoArray = getBranchesForAgent::getReport($tempArrBroker);
    /** Fetch Branches assigned to Agents **/
}


//if($LMRAEIDs > 0) {
$myOptNewAgentArray = [
        'PCID' => $PCID
];

if ($LMRAEIDs > 0) {
    $myOptNewAgentArray['executiveId'] = $LMRAEIDs;
} //https://www.pivotaltracker.com/story/show/*********/comments/*********


if ($assignedPCID > 0) {
} else {
    $assignedPCID = $PCID;
}
if ($userRole == glUserRole::USER_ROLE_BRANCH) {
    $agentInputArray = ['executiveID' => $executiveId];
} else if ($userRole == glUserRole::USER_ROLE_AGENT) {
    $agentInputArray = ['agentNo' => $agentNo];
} else if ($userGroup == glUserGroup::USER_GROUP_EMPLOYEE) {
    $agentInputArray = ['assignedProcCompanyId' => $assignedProcCompId];
} else {
    $agentInputArray = [];
}

if ($userGroup == 'Employee') {
    $mySearchArray = ['userType' => 'PLO', 'activeStatus' => 1, 'PCID' => $assignedPCID];
    if ($userRole != 'Manager') {
        $mySearchArray['executiveList'] = 'executive';
        $mySearchArray['executiveId'] = $LMRAEIDs;
    }
    $PLOSearchArray = getPLONameAndReferralCode::getReport($mySearchArray);

} else if ($userRole == 'Super') {
    $mySearchArray = ['userType' => 'PLO', 'activeStatus' => 1];
    $PLOSearchArray = getPLONameAndReferralCode::getReport($mySearchArray);

} else if ($userRole == 'Agent') {
    $mySearchArray = ['brokerNumber' => $agentNo, 'agentList' => 'agent'];
    $PLOSearchArray = getPLONameAndReferralCode::getReport($mySearchArray);
}

if (count($PLOSearchArray) > 0) {
    $PLOKeyArray = array_keys($PLOSearchArray);
}

$resultAgentInfoArray = getPLOClientAgent::getReport($agentInputArray);
if (count($resultAgentInfoArray) > 0) {
    $agentInfoArray = $resultAgentInfoArray['broker'];
    $agentEmails = $resultAgentInfoArray['email'];
    if ($agentEmails != '') {
        $ip['executiveEmails'] = $agentEmails;

        $agentPromoCodeArray = getPromoCodeForBranch::getReport($ip);
        if (count($agentPromoCodeArray) > 0) {
            $agentPromoCodeArray = array_change_key_case($agentPromoCodeArray, CASE_LOWER);
        }
    }
}

if (count($agentInfoArray) > 0) {
    $agentKeyArray = array_keys($agentInfoArray);
}

if (trim($myTimeZone) == '') {
    $myTimeZone = CONST_SERVER_TIME_ZONE;
}
?>
<input type="hidden" name="agentNo" id="agentNo" value="<?php echo cypher::myEncryption($agentNo) ?>">
<input type="hidden" name="executiveId" id="executiveId" value="<?php echo cypher::myEncryption($executiveId) ?>">
<input type="hidden" name="encClientId" value="<?php echo cypher::myEncryption($selClientId) ?>">
<input type="hidden" name="userRole" id="userRole" value="<?php echo cypher::myEncryption($userRole) ?>"/>
<input type="hidden" name="but_search" id="but_search" value=""/>
<input type="hidden" name="isClient" id="isClient" value="<?php echo $isClient ?>"/>
<input type="hidden" name="PCID" id="PCID" value="<?php echo cypher::myEncryption($PCID); ?>"/>


<div class="card card-custom mb-3">
    <div class="card-header card-header-tabs-line bg-gray-100  ">
        <div class="card-title">
            <h3 class="card-label">Contact Info</h3>
        </div>
        <div class="card-toolbar d-none">
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="toggle"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                <i class="ki ki-arrow-down icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1" data-card-tool="reload"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                <i class="ki ki-reload icon-nm"></i>
            </a>
            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary" data-card-tool="remove"
               data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                <i class="ki ki-close icon-nm"></i>
            </a>
        </div>
    </div>

    <div class="card-body">
        <div class="row">
            <?php
            if ($userRole == 'Branch' || $selClientId > 0) {
                ?>
                <div class="form-group row col-md-6 ">
                    <label class="col-md-5 font-weight-bold" for="referrerPLOCode">Selected Branch :</label>
                    <div class="col-md-7">
                        <input type="hidden" name="referrerPLOCode" id="referrerPLOCode"
                               value="<?php echo $promoCode ?>">
                        <input type="text" class="form-control input-sm " name="referrerPLOCodeName"
                               id="referrerPLOCodeName" value="<?php echo $clientSelectBranch ?>" disabled>
                    </div>
                </div>
                <?php
            } else { ?>
                <div class="form-group row col-md-6 ">
                    <label class="col-md-5 font-weight-bold" for="referrerPLOCode">Select the Branch</label>
                    <div class="col-md-7">
                        <select class="form-control input-sm mandatory" name="referrerPLOCode" id="referrerPLOCode">
                            <option value=''> - Select -</option>
                            <?php
                            for ($j = 0; $j < count($LMRInfoArray); $j++) {
                                $dispBranchName = '';
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($LMRInfoArray[$j]['executiveId']), $executiveId);

                                if (count($LMRInfoArray) == 1) $sOpt = 'Selected';

                                $dispBranchName = trim($LMRInfoArray[$j]['LMRExecutive']);
                                if ($userRole == 'Super') $dispBranchName .= ' - ' . trim($LMRInfoArray[$j]['company']);
                                echo "<option value=\"" . trim($LMRInfoArray[$j]['promoCode']) . "\" " . $sOpt . '>' . ucwords($dispBranchName) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>

                <div class="form-group row col-md-6 ">
                    <label class="col-md-5"></label>
                    <div class="col-md-7">
                        <select class="form-control input-sm " style="visibility: hidden;">
                        </select>
                    </div>
                </div>
                <?php } ?>


            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="clientFName">First name</label>
                <div class="col-md-7">
                    <input type="text" name="clientFName" id="clientFName"
                           value="<?php echo htmlentities($clientFName) ?>" size="30"
                           maxlength="30" autocomplete="off" class="form-control input-sm mandatory"/>
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="clientLName">Last name</label>
                <div class="col-md-7">
                    <input type="text" name="clientLName" id="clientLName"
                           value="<?php echo htmlentities($clientLName) ?>" size="30"
                           maxlength="30" autocomplete="off" class="form-control input-sm mandatory"/>
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="borrowerDOB">Date Of Birth</label>
                <div class="col-md-7">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">
                                <i class="fa fa-calendar text-primary"></i>
                            </span>
                        </div>
                        <input type="text" class="form-control dateNewClass" name="borrowerDOB" id="borrowerDOB"
                               placeholder="MM/DD/YYYY"
                               data-date-dob-start-date="<?php echo glDate::getMinRequirementDate(); ?>"
                               data-date-dob-end-date="<?php echo glDate::getMaxRequirementDate(); ?>"
                               value="<?php echo $borrowerDOB; ?>" size="30" maxlength="30"
                               autocomplete="off">
                    </div>
                </div>
            </div>


            <!-- Place Of Birth -->
            <div class="form-group row col-md-6">
                <label class="col-md-5" for="borrowerPOB">Place Of Birth</label>
                <div class="col-md-7">
                    <input type="text" name="borrowerPOB" id="borrowerPOB" class="form-control input-sm"
                           value="<?php echo htmlentities($borrowerPOB); ?>" autocomplete="off"/>
                </div>
            </div>
            <!-- //Place Of Birth// -->
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="ssnNumber">SSN Number</label>
                <div class="col-md-7">
                    <input type="text" name="ssnNumber" id="ssnNumber"
                           value="<?php echo Strings::formatSSNNumber($ssnNumber); ?>"
                           size="30" maxlength="30" autocomplete="off" class="form-control input-sm mask_ssn"
                           placeholder="___ - __ - ____"/>
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="clientPhone">Home Phone</label>
                <div class="col-md-7">
                    <input type="text" name="clientPhone" id="clientPhone"
                           value="<?php echo Strings::formatCellNumber($clientPhone); ?>" size="30" maxlength="30"
                           autocomplete="off"
                           class="form-control input-sm mask_cellnew" placeholder="(___) ___ - ____"/>
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="clientCell">Cell number</label>
                <div class="col-md-7">
                    <input type="text" name="clientCell" id="clientCell"
                           value="<?php echo Strings::formatCellNumber($clientCell) ?>"
                           size="30" maxlength="30" autocomplete="off" placeholder="(___) ___ - ____"
                           class="form-control input-sm mask_cellnew"/>
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="serviceProvider">Cell Carrier</label>
                <div class="col-md-6 col-xs-11">
                    <select name="serviceProvider" id="serviceProvider" class="form-control input-sm">
                        <option value="">- Select -</option>
                        <?php
                        $spKeyArray = array_keys($SMSServiceProviderArray);
                        for ($sp = 0; $sp < count($spKeyArray); $sp++) {
                            $spKey = $spKeyArray[$sp];
                            $myServiceProvider = $SMSServiceProviderArray[$spKey];
                            ?>
                            <option value="<?php echo $spKey ?>" <?php echo Arrays::isSelected($spKey, $serviceProvider); ?>><?php echo $myServiceProvider ?></option>
                            <?php
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-1 col-xs-1">
            <span><a class="fa fa-info-circle fa-2x tooltipClass" data-html="true" data-toggle="tooltip"
                     style="text-decoration:none;"
                     title="Please select service provider if you would like to receive task reminders via SMS"></a></i></span>
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="clientEmail">Email</label>
                <div class="col-md-7">
                    <?php
                    if ($selClientId > 0) {
                        ?>
                        <!-- Syncs with 'Contact Info' validation in 'Update Borrower Profile' page when user swithces tabs -->
                        <input type="hidden" name="clientEmail" id="clientEmail"
                               value="<?php echo htmlentities($clientEmail); ?>"/>
                        <?php
                        echo $clientEmail;
                    } else {
                        ?>
                        <input type="email" name="clientEmail" id="clientEmail"
                               value="<?php echo htmlentities($clientEmail); ?>"
                               size="40"
                               maxlength="75" onblur="checkClientEmailExist();" autocomplete="off"
                               class="form-control input-sm mandatory"/>
                        <?php
                    }
                    ?>
                </div>
            </div>
            <div class="form-group row col-md-6">
                <label class="col-md-5" for="clientSecondaryEmail">Secondary Email</label>
                <div class="col-md-7">
                    <input class="form-control input-sm" type="text" name="clientSecondaryEmail"
                           id="clientSecondaryEmail"
                           value="<?php echo htmlentities($clientSecondaryEmail); ?>">
                </div>
            </div>
            <div class="clearfix"></div>
            <div class="form-group row col-md-6 ">
                <script>
                    $(document).ready(function() {
                        $('#clientAddress').on('input', function() {
                            address_lookup.InitLegacy($(this));
                        });
                    });
                </script>
                <label class="col-md-5" for="clientAddress">Address</label>
                <div class="col-md-7">
                    <input type="text"
                           name="clientAddress"
                           id="clientAddress"
                           size="40"
                           maxlength="75"
                           data-address="clientAddress"
                           data-city="clientCity"
                           data-state="clientState"
                           data-zip="clientZip"
                           value="<?php echo htmlentities($clientAddress) ?>" autocomplete="off"
                           class="form-control input-sm">
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="clientCity">City</label>
                <div class="col-md-7">
                    <input type="text" name="clientCity" id="clientCity"
                           value="<?php echo htmlentities($clientCity); ?>" size="23"
                           maxlength="30" autocomplete="off" class="form-control input-sm">
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="clientState">State</label>
                <div class="col-md-7">
                    <select name="clientState" id="clientState"
                            onchange="populateStateTimeZone('clientRegForm', 'clientState', 'timeZone');"
                            class="form-control input-sm">
                        <option value=""> - Select -</option>
                        <?php
                        for ($s = 0; $s < count($stateArray); $s++) {
                            $sOpt = '';
                            $sOpt = Arrays::isSelected(trim($stateArray[$s]['stateCode']), $clientState);
                            echo "<option value=\"" . trim($stateArray[$s]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$s]['stateName']) . '</option>';
                        }
                        ?>            </select>
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="clientZip">Zip code</label>
                <div class="col-md-7">
                    <input type="text" name="clientZip" id="clientZip" size="10" maxlength="5"
                           value="<?php echo htmlentities($clientZip); ?>" autocomplete="off"
                           class="form-control input-sm zipCode">
                </div>
            </div>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="methodOfContact">Preferred Communication</label>
                <div class="col-md-7">
                    <select data-placeholder=" - Select - " name="methodOfContact[]" id="methodOfContact"
                            tabindex="<?php echo $tabIndex++; ?>"
                            class="chzn-select form-control form-controller-solid  <?php echo BaseHTML::chkMandField('methodOfContact', $PCquickAppFieldsInfo, ''); ?>"
                            multiple="">
                        <?php
                        $methodOfContactKeyArray = [];
                        $methodOfContactKeyArray = array_keys($methodOfContactArray);
                        for ($se = 0; $se < count($methodOfContactKeyArray); $se++) {
                            $sOpt = '';
                            $methodContact = '';
                            $methodContact = trim($methodOfContactKeyArray[$se]);
                            if (in_array($methodContact, $methodContactArray)) $sOpt = 'selected';

                            echo "<option value=\"" . trim($methodContact) . "\" " . $sOpt . '>' . $methodOfContactArray[$methodContact] . '</option>';
                        } ?>
                    </select>
                </div>
            </div>

            <div class="form-group row col-md-6">
                <label class="col-md-5" for="driverLicenseState">Driver License State</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <select class="form-control input-sm" name="driverLicenseState" id="driverLicenseState">
                            <option value=''> - Select -</option>
                            <?php
                            for ($j = 0; $j < count($stateArray); $j++) {
                                $sOpt = '';
                                $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $driverLicenseState);
                                echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
                            }
                            ?>
                        </select>
                    <?php } else { ?>
                        <h5><?php echo Strings::formatCellNumber($driverLicenseState); ?></h5>
                    <?php } ?>
                </div>
            </div>
            <div class="form-group row col-md-6">
                <label class="col-md-5" for="driverLicenseNumber">Driver License Number</label>
                <div class="col-md-7">
                    <?php if ($allowToEdit) { ?>
                        <input class="form-control input-sm" type="text" name="driverLicenseNumber"
                               id="driverLicenseNumber"
                               maxlength="20"
                               value="<?php echo htmlentities($driverLicenseNumber); ?>" autocomplete="new-password"/>
                    <?php } else { ?>
                        <h5><?php echo Strings::formatCellNumber($driverLicenseNumber); ?></h5>
                    <?php } ?>
                </div>
            </div>
            <?php
            if ($selClientId > 0) {
                if ($userRole == 'Super' || $userGroup == 'Super') {
                    ?>
                    <div class="form-group row col-md-6 ">
                        <label class="col-md-5"><a
                                    href="javascript:resetClientPwd('<?php echo cypher::myEncryption($selClientId) ?>','<?php echo cypher::myEncryption($executiveId) ?>');">Reset
                                Password</a></label>
                        <div class="col-md-7">
                        </div>
                    </div>
                    <?php
                }
            } else {
                ?>
                <div class="form-group row col-md-6 ">
                    <label class="col-md-5" for="clientPwd">Password</label>
                    <div class="col-md-7">
                        <input type="password" name="clientPwd" id="clientPwd" value="<?php echo $clientZip ?>"
                               maxlength="15"
                               size="30" autocomplete="new-password" class="form-control input-sm"/>
                    </div>
                </div>
                <div class="form-group row col-md-6 ">
                    <label class="col-md-5" for="confirmClientPwd">Confirm password</label>
                    <div class="col-md-7">
                        <input type="password" name="confirmClientPwd" id="confirmClientPwd" value="" maxlength="15"
                               size="30"
                               autocomplete="off" class="form-control input-sm"/>
                    </div>
                </div>
            <?php } ?>
            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="timeZone">Time Zone</label>
                <div class="col-md-7">
                    <select name="timeZone" id="timeZone" class="form-control input-sm">
                        <option value=''> - Select -</option>
                        <?php
                        $timeZoneKeyArray = [];
                        $timeZoneKeyArray = array_keys($timeZoneArray);
                        for ($tz = 0;
                             $tz < count($timeZoneKeyArray);
                             $tz++) {
                            $timeZone = trim($timeZoneKeyArray[$tz]);
                            ?>
                            <option value="<?php echo $timeZone ?>" <?php echo Arrays::isSelected($timeZone, $myTimeZone); ?>
                            ><?php echo $timeZoneArray[$timeZone] ?></option>
                            <?php
                        }
                        ?>
                    </select>
                </div>
            </div>

            <div class="form-group row col-md-6 ">
                <label class="col-md-5" for="sendMarketingEmail">Send email (about new features,<br> lenders and
                    marketing)</label>
                <div class="col-md-7">
                    <span class="switch switch-icon">
                        <label>
                            <input class="form-control" id="sendMarkettingEmail"
                                   type="checkbox" <?php if ($sendMarketingEmail == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $sendMarketingEmail ?>"
                                   onchange="toggleSwitch('sendMarkettingEmail', 'sendMarketingEmail','1','0' );"/>
                            <input type="hidden" name="sendMarketingEmail" id="sendMarketingEmail"
                                   value="<?php echo $sendMarketingEmail ?>">
                            <span></span>
                        </label>
                    </span>
                </div>
            </div>

            <?php //if ($userRole == 'Super' || $userGroup == 'Super') { ?>
            <!--      <div class="form-group row col-md-6 ">
          <label class="col-md-5" for="allowToLockLoanFileClient">Allow to Lock/unlock loan information?</label>
          <div class="col-md-7">
              <div <?php /*if ($allowToLockLoanFileClient == 1) { */ ?> class="switch-on" <?php /*} else { */ ?> class="switch-off" <?php /*} */ ?>   id="allowToLockLoanFileClientId" onclick="toggleSwitch('allowToLockLoanFileClientId', 'allowToLockLoanFileClient', '1', '0' );">
                  <br><input type="hidden" name="allowToLockLoanFileClient" id="allowToLockLoanFileClient" value="<?php /*echo $allowToLockLoanFileClient*/ ?>">
              </div>
          </div>
      </div>-->
            <?php //} ?>


            <?php if ($userGroup == 'Super' || $userRole == 'Manager') {
                ?>
                <div class="form-group row col-md-6 ">
                    <label class="col-md-5" for="allowToViewMarketPlace">Enable Marketplace Tab?</label>
                    <div class="col-md-7">
                    <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" id="allowToViewMarketPlaceForm"
                                               type="checkbox" <?php if ($allowToViewMarketPlace == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $allowToViewMarketPlace ?>"
                                               onchange="toggleSwitch('allowToViewMarketPlaceForm', 'allowToViewMarketPlace','1','0' );"/>
                                        <input type="hidden" name="allowToViewMarketPlace" id="allowToViewMarketPlace"
                                               value="<?php echo $allowToViewMarketPlace ?>">
                                        <span></span>
                                    </label>
                                </span>
                    </div>
                </div>
                <div class="clearfix"></div>
                <div class="form-group row col-md-6">
                    <label class="col-md-5" for="allowToSubmitOffer">Enable Offers Tab?</label>
                    <div class="col-md-7">
                        <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control" id="allowToSubmitOffer"
                                               type="checkbox" <?php if ($allowToSubmitOffer == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $allowToSubmitOffer ?>"
                                               onchange="toggleSwitch('allowToSubmitOffer', 'allowtosubmitoffer','1','0' );"/>
                                        <input type="hidden" name="allowToSubmitOffer" id="allowtosubmitoffer"
                                               value="<?php echo $allowToSubmitOffer ?>">
                                        <span></span>
                                    </label>
                                </span>

                    </div>
                </div>
            <?php } ?>

            <?php
            if ($selClientId == 0) {
                if ($userRole == 'Agent' || $selClientId > 0) {
                    $iptempArray = [];
                    $iptempArray = ['agentEmails' => "'" . $userEmail . "'"];
                    $agentPromoCodeInfoArray = getPromoCodeForAgent::getReport($iptempArray);
                    $agentCode = key($agentPromoCodeInfoArray);
                    ?>
                    <input type="hidden" name="referrerAffCode" id="referrerAffCode" value="<?php echo $agentCode ?>">
                    <?php
                } else {
                    $agentPromoCodeInfoArray = getPCAgents::getPromoCodes($myOptNewAgentArray);

                    ?>

                    <div class="form-group row col-md-6 ">
                        <label class="col-md-5" for="referrerPLOCode">
                            Which Loan Officer/Broker Referred?
                        </label>
                        <div class="col-md-7">
                            <select class="form-control input-sm mandatory" name="referrerAffCode" id="referrerAffCode">
                                <option value=''> - Select -</option>
                                <?php
                                foreach ($agentPromoCodeInfoArray as $pKey => $prValue) {
                                    $dispAgentName = '';
                                    $sOpt = '';
                                    $brokerName = '';
                                    $brokerName = trim($prValue['firstName']) . ' ' . trim($prValue['lastName']);
                                    $sOpt = Arrays::isSelected(trim($pKey), $agentNo);
                                    echo "<option value=\"" . trim($pKey) . "\" " . $sOpt . '>' . ucwords($brokerName) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>

                    <!--  <div class="form-group row col-md-6 ">
                       <label class="col-md-5" for="agentName">Which agent referred?</label>
                       <div class="col-md-7">
                         <input type="text" name="agentName" id="agentName" size="40" value="">
                         <input type="text" name="referrerAffCode" id="referrerAffCode" value="">
                       </div>
                     </div> -->
                    <?php
                }
            }
            /* if($selClientId > 0) {
             } else {
         ?>
                     <div class="form-group row col-md-6 ">
                       <label class="col-md-5" for="agreeTC">Which agent referred?</label>
                       <div class="col-md-7">
                         <input type="checkbox" id="agreeTC" name="agreeTC" value="1" class="mandatory"> By checking this box I agree to the terms and conditions.
                       </div>
                     </div>
         <?php
             } */
            ?>
        </div>
    </div>
</div>
<?php require 'clientGovtInfo.php'; ?>
<div class="row d-flex justify-content-center my-2">
    <?php
    if (in_array('HMLO', $PCModulesArray)) { ?>
        <button type="submit" name="butSubmit" id="butSubmit" value="Save"
                class="btn btn-primary font-weight-bold">Save
        </button>
        &nbsp;&nbsp;&nbsp;
        <button type="submit" name="but_search" id="but_sumbit" value="Save & Next"
                class="btn btn-primary font-weight-bold">Save & Next
        </button>
    <?php } else { ?>
        <button type="submit" name="butSubmit" id="butSubmit" value="Save"
                class="btn btn-primary font-weight-bold">Save
        </button>
    <?php } ?>
</div>
<?php
/*if($selClientId > 0) {
} else {
?>
<div class="left pad5" style="width:230px">
  <td colspan="4"><b>Note:</b> First name, Last name, Phone number, Cell number and email will be used in client <b>"Loss Mitigation Request"</b>. Please take care to enter the correct data.</td>
</div>
<?php } */ ?>
