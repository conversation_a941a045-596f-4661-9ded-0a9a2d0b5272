<?php

namespace tasks\migration\_868ebuu0x;

use models\APIHelper;
use models\Database2;
use models\lendingwise\db\tblFileContacts_db;
use models\lendingwise\tblFileContacts;
use models\types\strongType;

include __DIR__ . '/../../../public/includes/util.php';
class deleteDuplicateContactsForFile extends strongType
{
    public static function Run()
    {
        $query = APIHelper::getSQL(__DIR__ . '/sql/duplicateFileContacts.sql');
        $result = Database2::getInstance()->queryData($query);
        $deletedContactsIds = [];
        foreach ($result as $row) {
            $tblFileContacts = tblFileContacts::GetAll([tblFileContacts_db::COLUMN_CID => $row['CID']
                ,tblFileContacts_db::COLUMN_FILEID => $row['fileID']]);

           foreach ($tblFileContacts as $contactKey => $eachContact){
               if($contactKey != 0){
                   continue;
               }
               $deletedContactsIds[] = $eachContact->SID;

           }
        }
        echo 'Deleted File Contacts Is :';
        pr($deletedContactsIds);
        if(sizeof($deletedContactsIds)){
            $sql = " Delete From tblFileContacts where SID in (".implode(',',$deletedContactsIds).")";
            echo $sql;
        }
    }
}
deleteDuplicateContactsForFile::Run();