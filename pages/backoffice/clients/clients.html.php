<?php

namespace pages\backoffice\clients;

use models\ClientColumns;
use models\constants\defaultUserPreferencesBorrowerListArray;
use models\constants\gl\glUserRole;
use models\cypher;
use models\PageVariables;
use models\portals\Breadcrumb;
use models\portals\LoginPage;
use models\Request;
use models\standard\Currency;
use pages\backoffice\clients\classes\ClientRow;

$activeDeals = 0;
$allowUBAToUpAndDowngradeClient = 1;
$LMRId = 0;
$clsRow = '';
$clsPay = '';
$clsFileComplete = '';

?>

<div style="float: right">
    <div title="Advanced Search"
         class="tooltipClass btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1 "
         data-toggle="collapse"
         data-target="#borrowerSearchCollapse">
        <i class="flaticon-search text-primary"></i>
    </div>

    <div title="Select Columns"
         class="tooltipClass btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1 "
         data-toggle="collapse"
         data-target="#borrowerColumnsCollapse">
        <i class="flaticon-interface-6 text-primary"></i>
    </div>

    <?php if (PageVariables::$userRole == glUserRole::USER_ROLE_SUPER) { ?>
        <a href="<?php echo clients::$userURL; ?>/clientCreate.php?isClient=1&PCID=<?php echo cypher::myEncryption(Request::GetClean('processingCompanyId')); ?>"
           class="btn btn-primary btn-sm"
           data-card-tool="toggle">
            Create Borrower
        </a>
    <?php } else { ?>
        <a href="<?php echo clients::$userURL; ?>/clientCreate.php?isClient=1" class="btn btn-primary btn-sm"
           data-card-tool="toggle">
            Create Borrower
        </a>
    <?php } ?>
    <?php if (PageVariables::$userRole == glUserRole::USER_ROLE_MANAGER) { ?>
        <a title="Import Borrower"
           class="tooltipClass btn btn-sm btn-light-primary btn-text-primary btn-hover-primary btn-icon m-1 "
           href="/backoffice/clients/import"
        >
            <i class="fas fa-file-import text-primary"></i>
        </a>
    <?php } ?>
</div>

<h3 class="card-label"><?php echo Breadcrumb::$title; ?>
    <small><i class="manualPopover fas fa-info-circle text-primary"
              data-html="true"
              data-content='
                    <div class="row">
                        <div class="col-md-6"><i class="fas fa-user-check"></i> - denotes active Employee(s)</div>
                        <div class="col-md-6"><i class="fas fa-user-times"></i> - denotes Inactive Employee(s)</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6"><i class="fas fa-user-check"></i> - Click to send email campaign</div>
                        <div class="col-md-6"><i class="flaticon-refresh"></i> - Click to change</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6"><i class="flaticon2-check-mark"></i> - Allowed to login</div>
                        <div class="col-md-6"><i class="flaticon2-delete"></i> - Not allowed to login</div>
                    </div>'></i></small>
</h3>
<div style="clear: both;"></div>
<?php $hidden_values = [
    "userId"   => cypher::myEncryption(PageVariables::$userNumber),
    "userType" => cypher::myEncryption(PageVariables::$userGroup),
    "toUType"  => cypher::myEncryption('Borrower'),
    "encPCID"  => cypher::myEncryption(PageVariables::$PCID),
];
foreach ($hidden_values as $name => $value) { ?>
    <input type="hidden" name="<?php echo $name; ?>" id="<?php echo $name; ?>"
           value="<?php echo htmlentities($value); ?>"/>
<?php } ?>
<?php require __DIR__ . '/sections/clientSearch.php'; ?>

<?php require __DIR__ . '/sections/clientColumns.php'; ?>

<?php echo clients::$Pagination; ?>

<?php if (!clients::$sortable) { ?>
    <div class="alert alert-warning">Sorting is not available, result set too large. Please refine your search.</div>
<?php } ?>

<div style="width: 100%; overflow-x: scroll;">
    <table class="table table-striped" style="background-color: #fff;">
        <thead>
        <tr>
            <th class="text-center">#</th>
            <th class="text-nowrap">
                <?php
                echo ClientColumns::clientListHeaderSortHTML(
                    'name',
                    clients::$sortBy,
                    clients::$sortDir,
                    'Borrower/Co-Borrower Name',
                    PageVariables::$userGroup !== glUserRole::USER_ROLE_SUPER
                    || clients::$sortable
                ); ?>
            </th>
            <th class="text-nowrap">
                <?php
                echo ClientColumns::clientListHeaderSortHTML(
                    'entity',
                    clients::$sortBy,
                    clients::$sortDir,
                    'Entity',
                    PageVariables::$userGroup !== glUserRole::USER_ROLE_SUPER
                    || clients::$sortable
                ); ?>
            </th>
            <th class="text-nowrap text-left">Tags</th>
            <th class="text-nowrap">
                <?php echo ClientColumns::clientListHeaderSortHTML(
                    'creditLine',
                    clients::$sortBy,
                    clients::$sortDir,
                    'Credit Line',
                    PageVariables::$userGroup !== glUserRole::USER_ROLE_SUPER
                    || clients::$sortable
                ); ?>
            </th>
            <?php foreach (ClientColumns::Columns() as $column) {
                if (!$column->visible
                    || !in_array($column->column, clients::$selBorrowerHeader)
                ) {
                    continue;
                }
                ?>
                <th class="text-nowrap"
                    id="<?php echo $column->column; ?>"
                >
                    <?php if ($column->sortOpt) { ?>
                        <?php echo ClientColumns::clientListHeaderSortHTML(
                            $column->column,
                            clients::$sortBy,
                            clients::$sortDir,
                            $column->display,
                            PageVariables::$userGroup !== glUserRole::USER_ROLE_SUPER
                            || clients::$sortable
                        ); ?>

                    <?php } else { ?>
                        <?php echo $column->display; ?>
                    <?php } ?>
                </th>
            <?php } ?>

            <?php
            if (PageVariables::$userRole == glUserRole::USER_ROLE_SUPER || (PageVariables::$userRole == glUserRole::USER_ROLE_MANAGER) || (PageVariables::$userRole == glUserRole::USER_ROLE_AGENT) || ($allowUBAToUpAndDowngradeClient == 1)) {
                if (clients::$activeBorrower == 0) { ?>
                    <th class="text-nowrap" style="text-align: center">Actions</th>
                <?php } else {
                    ?>
                    <th class="text-nowrap"
                        style="text-align: center">Actions
                    </th>
                <?php } ?>
                <?php
            }

            if ((PageVariables::$userRole == glUserRole::USER_ROLE_SUPER || PageVariables::$userRole == glUserRole::USER_ROLE_MANAGER || PageVariables::$userRole == glUserRole::USER_ROLE_BRANCH || PageVariables::$userRole == glUserRole::USER_ROLE_AGENT) && clients::$activeBorrower == 1 && clients::$PCInfo->allowEmailCampaign) {
                ?>
                <th class="text-nowrap">
                    <div class="checkbox-inline" style="display: inline-block">
                        <label class="checkbox checkbox-outline checkbox-square">
                            <input class="selectBelowAll" type="checkbox" data-html="true"
                                   data-toggle="tooltip"
                                   title="Click to select all (<?php echo clients::$count; ?>) files below."
                                   onchange="clients.sendMarketingEmailForBelowClient(this)"><span></span></label>
                    </div>
                    <a class="btn1 d-none" id="clientEmailBtn"
                       href='#'
                       data-href="<?php echo CONST_URL_POPS; ?>sendEmailCampaign.php"
                       data-wsize='modal-xl'
                       data-name='Send Email Campaign'
                       data-toggle='modal' data-target='#exampleModal1'
                       data-id=''>
                        <i class="ki ki-solid-plus icon-nm"></i>
                    </a>
                    <span data-name="Send Email Campaign" data-id=""
                          onclick="clients.sendMarketingEmailForSelectedClient()"><span
                                class="svg-icon  svg-icon  svg-icon-2x"><svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24px"
                                    height="24px"
                                    viewBox="0 0 24 24">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path
                d="M5,6 L19,6 C20.1045695,6 21,6.8954305 21,8 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,8 C3,6.8954305 3.8954305,6 5,6 Z M18.1444251,7.83964668 L12,11.1481833 L5.85557487,7.83964668 C5.4908718,7.6432681 5.03602525,7.77972206 4.83964668,8.14442513 C4.6432681,8.5091282 4.77972206,8.96397475 5.14442513,9.16035332 L11.6444251,12.6603533 C11.8664074,12.7798822 12.1335926,12.7798822 12.3555749,12.6603533 L18.8555749,9.16035332 C19.2202779,8.96397475 19.3567319,8.5091282 19.1603533,8.14442513 C18.9639747,7.77972206 18.5091282,7.6432681 18.1444251,7.83964668 Z"
                fill="#000000"/>
    </g>
</svg></span>
                </span>
                </th>
            <?php } ?>
        </tr>
        </thead>
        <tbody>
        <?php foreach (clients::$ReportRows as $i => $row) {
            $serialNo = $i + 1 + clients::$page * clients::$perPage;
            ?>
            <tr <?php echo $clsRow ?>>
                <td class="text-center"><?php echo $serialNo ?></td>
                <td <?php echo $clsPay ?>>
                    <?php
                    if (PageVariables::$userRole == glUserRole::USER_ROLE_AGENT) { ?>
                    <a class="tooltipClass"
                       href="<?php echo $row->userUrl ?>clientCreate.php?cId=<?php echo cypher::myEncryption($row->CID); ?>&encEId=<?php echo $row->encLMRExecutiveID ?>"
                       data-html="true"
                       data-toggle="tooltip"
                       style="text-decoration:none" title="Click to view borrower profile"
                       target="_blank">
                        <?php } else { ?>
                        <a class="tooltipClass"
                           href="<?php echo $row->userUrl ?>clientCreate.php?cId=<?php echo cypher::myEncryption($row->CID); ?>&encEId=<?php echo $row->encLMRExecutiveID ?>"
                           style="text-decoration:none" title="Click to view borrower profile"
                           target="_blank">
                            <?php }
                            echo "<span class='font-weight-bold'>" . ($row->clientName ?: '<i>Client Name Not Set</i>') . '</span>'; ?></a>
                        <br>
                        <?php echo $row->clientEmail ?>
                </td>
                <td><?php echo $row->entityName; ?></td> <!-- Entity Info -->
                <td class="text-left">
                                        <span data-wsize='modal-default'
                                              data-toggle='modal'
                                              data-target='#exampleModal1'
                                              data-name="Add/Edit Tags"
                                              title="Add/Edit Tags"
                                              class="underline tooltipClass"
                                              data-id="id=<?php echo $row->CID; ?>&PCID=<?php echo cypher::myEncryption(PageVariables::$PCID); ?>&internalInfoTags=<?php echo $row->internalInfoTags; ?>"
                                              data-href="<?php echo CONST_URL_POPS; ?>tags.php">
                                            <i class="tooltipClass far fa-edit mr-2"></i><?php echo $row->internalInfoTags; ?></span>
                </td>
                <td><?php echo Currency::formatDollarAmountWithDecimal($row->internalInfoCreditLine); ?></td>

                <?php foreach (ClientColumns::Columns() as $column) { ?>
                    <?php if (!in_array($column->column, defaultUserPreferencesBorrowerListArray::$defaultUserPreferencesBorrowerListArray)) { ?>
                        <td><?php echo $column->column; ?></td>
                    <?php } else { ?>
                        <?php
                        if (!$column->visible
                            || !in_array($column->column, clients::$selBorrowerHeader)
                        ) {
                            continue;
                        }
                        ?>

                        <td id='<?php echo $column->column; ?>_<?php echo $serialNo; ?>'
                            class="<?php echo $column->class; ?>"
                        >
                            <?php if ($column->column == 'noOfDeals') { ?>
                                <a
                                        href="<?php echo $row->dealsUrl ?>"
                                        target="_blank"><?php echo $row->{$column->column}; ?>
                                </a>
                            <?php } else { ?>
                                <?php echo $row->{$column->column}; ?>
                            <?php } ?>
                        </td>

                    <?php } ?>
                <?php } ?>
                <td>
                    <?php
                    if (clients::$activeBorrower == 0) {
                        if ($activeDeals == 0) {
                            echo $row->clActiveStatusDisp;
                        }
                    } else {
                        if (PageVariables::$userRole == glUserRole::USER_ROLE_SUPER || PageVariables::$userRole == glUserRole::USER_ROLE_MANAGER) {
                            echo $row->clPwd;
                        }
                        ?>
                        <div class="d-flex justify-content-around">
                            <?php if (PageVariables::isSuper()) { ?>
                                <span class="change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1 ghost_user cursor-pointer"
                                      title="Get Ghost Link"
                                      data-user_number="<?php echo $row->CID; ?>"
                                      data-user_role="<?php echo glUserRole::USER_ROLE_CLIENT; ?>"
                                      data-user_group="<?php echo LoginPage::UR_CLIENT; ?>">
                                            <i class="fa fa-ghost"></i>
                                        </span>
                            <?php } ?>
                            <div style="display: inline-block" id="statusNotes_<?php echo $row->CID ?>"
                                 data-cid="<?php echo cypher::myEncryption($row->CID); ?>"
                                 onmouseover="clients.loadNotes(this);">
                                <?php
                                $processorCommentHeaderButton = '<a class="editable  clientNotes btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1" data-type="textarea" data-pk="' . cypher::myEncryption($row->CID . '-' . PageVariables::$PCID) . '"
                                                  data-value="" data-title="Notes"
                                                   data-original-title="Click to add Notes"
                                                    style="text-decoration:none!important; font-size: medium;"
                                                  id="brokerStatusNotes_' . $row->CID . '" href="#"
                                           ><i class="icon-md fas fa-comments "></i> </a>';

                                $processorCommentsNotesList = '<ul class="list-group p-1 font-size-sm" id="last_10_notes_' . $row->CID . '"></ul>';
                                $processorCommentsNotesFinal = '<div class="card card-custom">
 <div class="card-header">       
 <div class="card-title"><h3 class="card-label">Latest 10 notes are listed below</h3></div>
        <div class="card-toolbar">
            ' . $processorCommentHeaderButton . '
        </div>
 </div>
 <div class="card-body p-0 ' . $row->CID . '_cNotes">' . $processorCommentsNotesList . '</div>
</div>'

                                ?>
                                <span id="divListNotes<?php echo $LMRId ?>">
                <span id="status-bar">
                    <span class="status-infos">
                        <span class="lireplace">
                            <?php echo $processorCommentHeaderButton; ?>
                            <div class="result-block card-body p-0 "><span class="arrow"></span>
                                <?php echo $processorCommentsNotesFinal; ?>
                            </div>
                        </span>
                    </span>
                </span>
            </span>
                            </div>
                            <?php
                            if ($activeDeals == 0) {
                                echo $row->clActiveStatusDisp;
                            }
                            ?>
                            <?php echo $row->clientLoginRight; ?><?php echo $row->clientEmailRights; ?>
                            <?php echo $row->clientCreateNewFile; ?>
                        </div>

                    <?php } ?>
                </td>
                <?php
                if ((PageVariables::$userRole == glUserRole::USER_ROLE_SUPER
                        || PageVariables::$userRole == glUserRole::USER_ROLE_MANAGER
                        || PageVariables::$userRole == glUserRole::USER_ROLE_BRANCH
                        || PageVariables::$userRole == glUserRole::USER_ROLE_AGENT)
                    && clients::$activeBorrower == 1 && clients::$PCInfo->allowEmailCampaign) { ?>
                    <td><?php echo $row->clientEmailRights1; ?></td>
                <?php } ?>

            </tr>
        <?php } ?>
        </tbody>
    </table>
</div>
<?php echo clients::$Pagination; ?>

<script src="/backoffice/clients/js/clients.js?<?php echo CONST_JS_VERSION; ?>" type="application/javascript"></script>
<script src="/backoffice/api_v2/js/address_lookup.js?<?php echo CONST_JS_VERSION; ?>"
        type="application/javascript"></script>
<script type="text/javascript" src="/assets/js/ghostUser.js?<?php echo CONST_JS_VERSION; ?>"></script>
<script>
    clients.PCID = <?php echo PageVariables::$PCID ?: 0; ?>;
    clients.userURL = "<?php echo clients::$userURL; ?>";

    $(function () {
        clients.initAddNotes();

        $('#pcName').autocomplete({
            serviceUrl: '<?php echo CONST_SITE_URL; ?>JQFiles/getPCs.php',
            minChars: 2,
            onSelect: function (value, data) {
                $('#pcName').val(replaceXMLProcess(value));
                $('#processingCompanyId').val(data);
            }
        });

    });

</script>