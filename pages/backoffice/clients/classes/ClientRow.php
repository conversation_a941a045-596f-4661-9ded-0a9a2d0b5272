<?php

namespace pages\backoffice\clients\classes;

use models\composite\oClient\getMyClients\ClientAgentBranchId;
use models\composite\oClient\getMyClients\ClientAgentInfo;
use models\composite\oClient\getMyClients\ClientBranchInfo;
use models\constants\gl\glUserGroup;
use models\constants\gl\glUserRole;
use models\cypher;
use models\lendingwise\tblProcessingCompany;
use models\PageVariables;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

class ClientRow extends strongType
{
    public ?int $CID = null;
    public ?string $entityName = null;
    public ?string $clientName = null;
    public ?int $noOfDeals = null;
    public ?string $totalLoanAmount = null;
    public ?float $internalInfoCreditLine = null;
    public ?string $askPaymentBeforeLMR = null;
    public ?string $statusNotes = null;
    public ?string $userUrl = null;
    public ?string $dealsUrl = null;
    public ?string $createdDate = null;
    public ?string $lastLogin = null;
    public ?string $lastFileCreateDate = null;
    public ?string $clientEmail = null;
    public ?string $clientCity = null;
    public ?string $clientState = null;
    public ?string $clAddress = null;
    public ?string $branch = null;
    public ?string $encLMRExecutiveID = null;
    public ?string $broker = null;
    public ?string $loanOfficer = null;
    public ?string $contactInfo = null;
    public ?string $clPwd = null;
    public ?string $clientLoginRight = null;
    public ?string $clientEmailRights = null;
    public ?string $clActiveStatusDisp = null;
    public ?string $dispColor = null;
    public ?string $clientEmailRights1 = null;
    public ?string $clientCreateNewFile = null;
    public ?string $internalInfoTags = null;
    public ?string $PCName = null;

    /**
     * @param clientList $item
     * @param tblProcessingCompany|null $PCInfo
     * @param ClientBranchInfo[] $branchInfoArray
     * @param ClientAgentBranchId[] $uniqueAgentBranchArray
     * @param ClientAgentInfo[] $agentInfoArray
     * @return ClientRow
     */
    public static function fromClientList(
        clientList            $item,
        ?tblProcessingCompany $PCInfo,
        array                 $branchInfoArray,
        array                 $uniqueAgentBranchArray,
        array                 $agentInfoArray
    ): ClientRow
    {
        $branchIdsArray = [];
        $LMRExecutiveID = 0;
        $LMRUserType = '';
        $clPwd = '';
        $agentIdsArray = [];
        $clientEmailLink = '';
        $statusNotes = '';
        $askPaymentBeforeLMR = null;

        switch (PageVariables::$userRole) {
            case glUserRole::USER_ROLE_BRANCH:
                $LMRUserType = Strings::GetSess('AEUserType');
                $userUrl = '/branch/';
                break;
            case glUserRole::USER_ROLE_AGENT:
                $userUrl = '/agent/';
                break;
            default:
                $userUrl = '/backoffice/';
                break;
        }

        if (PageVariables::$userRole != glUserRole::USER_ROLE_BRANCH) {
            $askPaymentBeforeLMR = 1;
        }

        if ($item->clientEmail) {
            $clientEmailLink = $item->clientEmail;
        }

        if ($item->noOfDeals > 0) {
            $dealsUrl = $userUrl . 'myPipeline.php?searchTerm=' . $item->clientEmail . '&searchField=tl.borrowerEmail&activeFile=1&clientID=' . $item->CID;
        } else {
            $dealsUrl = $userUrl . 'myPipeline.php?searchTerm=' . $item->clientEmail . '&searchField=tl.borrowerEmail&activeFile=0&clientID=' . $item->CID;
        }

        if (array_key_exists($item->CID, $uniqueAgentBranchArray)) {
            $agentIdsArray = $uniqueAgentBranchArray[$item->CID]->agentIds;
            $branchIdsArray = $uniqueAgentBranchArray[$item->CID]->branchIds;
            $agentIdsArray = array_values(array_unique($agentIdsArray));
            $branchIdsArray = array_values(array_unique($branchIdsArray));
        }

        $clientName = htmlentities(ucwords(trim($item->clientFName . ' ' . $item->clientLName)), ENT_QUOTES, 'UTF-8');
        $clientPhone = Strings::formatPhoneNumber($item->clientPhone);
        $registerDate = Dates::formatDateWithRE($item->registerDate, 'YMD', 'M j, Y');
        $lastLogin = Dates::formatDateWithRE($item->lastLogin, 'YMD', 'M j, Y');
        $lastFileCreateDate = Dates::formatDateWithRE($item->lastFileCreateDate, 'YMD', 'M j, Y');
        if ($clientPhone) {
            $clientPhone = '<b>Ph:</b> ' . $clientPhone . '<br>';
        }

        $clientCell = '';
        if ($item->clientCell) {
            $clientCell = Strings::formatPhoneNumber($item->clientCell);
            $clientCell = '<b>Cell:</b> ' . $clientCell . '<br>';
        }
        $clientEmail = '';
        if ($item->clientEmail) {
            if (strlen($item->clientEmail) > 25) {
                $clientEmail = "<a href=\"mailto:" . $item->clientEmail . "\">" . $item->clientEmail . '</a>';
            } else {
                $clientEmail = "<a href=\"mailto:" . $item->clientEmail . "\">" . wordwrap($item->clientEmail, 25, '<br>', 1) . '</a>';
            }
        }
        $clientCity = '';
        if ($item->clientCity) {
            $clientCity = ',<br>' . $item->clientCity . ', ';
        }

        $clientState = '';
        if ($item->clientState) {
            $clientState = $item->clientState . ' - ' . $item->clientZip . '.';
        }
        $clAddress = ucfirst($item->clientAddress) . ucfirst($item->clientCity) . $item->clientState;

        $contactInfo = $clientPhone . $clientCell;

        if ($contactInfo) {
            $clAddress .= '<br>' . $contactInfo;
        }
        if ($clAddress == '') {
            $clAddress = '&nbsp;';
        }


        /** Branch Information Start **/
        $encCID = cypher::myEncryption($item->CID);
        $encLMRExecutiveID = '';
        $branchDetails = '';
        if (
            PageVariables::$userRole == glUserRole::USER_ROLE_SUPER
            || PageVariables::$userRole == glUserRole::USER_ROLE_AGENT
            || PageVariables::$userGroup == glUserGroup::USER_GROUP_EMPLOYEE
        ) {
            $hr = '';
            foreach ($branchIdsArray as $BRID) {
                if (array_key_exists($BRID, $branchInfoArray)) {
                    $executiveEmail = $branchInfoArray[$BRID]->executiveEmail;
                    $LMRExecutive = $branchInfoArray[$BRID]->LMRExecutive;
                    $LMRExecutiveID = $branchInfoArray[$BRID]->executiveId;
                    $LMRUserType = $branchInfoArray[$BRID]->userType;
                    $askPaymentBeforeLMR = $branchInfoArray[$BRID]->askPaymentBeforeLMR;
                    $branchInfo = $LMRExecutive . ",<br><a href=\"mailto:" . $executiveEmail . "\">";
                    if (strlen($executiveEmail) > 25) {
                        $branchInfo .= wordwrap($executiveEmail, 25, '<br>', 1);
                    } else {
                        $branchInfo .= $executiveEmail;
                    }
                    $branchInfo .= '</a>';
                    $branchInfo = "<div class=\"left\" style=\"width:100%\">" . $hr . $branchInfo . '</div>';
                    $branchDetails .= $branchInfo;
                    $hr = '<hr>';
                }
                $encLMRExecutiveID = trim(cypher::myEncryption($LMRExecutiveID));
            }
        } else if (PageVariables::$userGroup == 'Branch') {
            $encLMRExecutiveID = trim(cypher::myEncryption(PageVariables::$userNumber));
        }
        /** Branch Information End **/

        /** Agent Information Start **/
        $agentDetails = [];
        $loDetails = [];
        if (count($agentIdsArray) > 0) {
            foreach ($agentInfoArray as $AGID => $agent) {

                if ($item->BrokerIDs) {
                    $brokerIds = explode(',', $item->BrokerIDs);
                    if (in_array($AGID, $brokerIds) && strpos($agent->email, '@dummyAgentemail.com') === false) {
                        $brokerName = $agent->brokerName;
                        $email = $agent->email;
                        $brokerStatus = $agent->status;
                        $agentInfo = $brokerName . ",<br><a href=\"mailto:" . $email . "\">";
                        if (strlen($email) > 25) {
                            $agentInfo .= wordwrap($email, 25, '<br>', 1);
                        } else {
                            $agentInfo .= $email;
                        }
                        $agentInfo .= '</a>';

                        $agentInfo = "<div class=\"left\" style=\"width:100%;\">" . $agentInfo . '</div>';
                        if ($brokerStatus == 0) {
                            $agentInfo .= "<div class=\"right icons16\"><a class=\"reject\"></a></div>";
                        }
                        $agentDetails [] = $agentInfo;
                    }
                }
                if (!$item->LoanOfficerIDs) {
                    continue;
                }

                $loanOfficerIds = explode(',', $item->LoanOfficerIDs);
                if (in_array($AGID, $loanOfficerIds)) {
                    $loName = $agent->brokerName;
                    $email = $agent->email;
                    $brokerStatus = $agent->status;
                    $loInfo = $loName . ",<br><a href=\"mailto:" . $email . "\">";
                    if (strlen($email) > 25) {
                        $loInfo .= wordwrap($email, 25, '<br>', 1);
                    } else {
                        $loInfo .= $email;
                    }
                    $loInfo .= '</a>';

                    $loInfo = "<div class=\"left\" style=\"width:100%;\">" . $loInfo . '</div>';
                    if ($brokerStatus == 0) {
                        $loInfo .= "<div class=\"right icons16\"><a class=\"reject\"></a></div>";
                    }
                    $loDetails [] = $loInfo;
                }
            }
        }

        if ((PageVariables::$userRole == 'Super') || (PageVariables::$userRole == 'Manager')) {
            $clPwd = "<span class=\"cursor-pointer change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1\" 
            data-name=\"Change Password - " . ucwords($clientName) . "\"
             data-id=\"userId=" . cypher::myEncryption($item->CID) . '&userType=' . cypher::myEncryption('Client') . "\"
              data-href=\"" . CONST_URL_POPS . "passwordChangePopup.php\" 
              data-wsize = 'modal-default' 
              data-toggle='modal' 
              data-target='#exampleModal1'><i class='tooltipAjax flaticon-refresh' title=\"Click to Change Password\" ></i></a></span>";
        }
//        if ((PageVariables::$userRole == 'Super')) {
//            $clPwd .= "<span  id=\"divPwd_" . $item->CID . "\">" . wordwrap($item->clientPwd, 15, '<br>', 1) . '</span>';
//        }

        if ($item->activeStatus == 1) {
            $clActiveStatusDisp = "<span class=\"cursor-pointer change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1\" 
            onclick=\"clients.deletePLOClient('" . $encCID . "', '" . cypher::myEncryption(implode(', ', $branchIdsArray)) . "', '" . cypher::myEncryption(implode(', ', $agentIdsArray)) . "' ,'" . $clientName . "','0');\" 
            ><i class='tooltipAjax fas fa-user-check' title=\"" . ucfirst($clientName) . " is active. Click to deactivate this borrower and all connected loan files.  If you want to move the loan files, please do so via bulk updating in the pipeline.\"></i></span>";
            $dispColor = 'blue';
        } else {
            $clActiveStatusDisp = "<span class=\"cursor-pointer change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1\"
             onclick=\"clients.deletePLOClient('" . $encCID . "', '" . cypher::myEncryption(implode(', ', $branchIdsArray)) . "', '" . cypher::myEncryption(implode(', ', $agentIdsArray)) . "', '" . $clientName . "','1');\" 
             ><i class='tooltipAjax fas fa-user-times' title=\"" . ucfirst($clientName) . " is inactive. Click to activate\" ></i></span>";
            $dispColor = 'red';
        }

        $clientLoginRight = "<span class=\"left pad2\" id=\"cl_" . $item->CID . "\">";
        if ($item->allowToLogin == 1) {
            $clientLoginRight .= "<span class=\"cursor-pointer change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1\" 
            onclick=\"clients.updateClientLogin('" . $encCID . "', '0', 'cl_" . $item->CID . "');\"><i class='tooltipAjax flaticon2-check-mark' title=\"Click to stop login\"></i></a></span>";
        } else {
            $clientLoginRight .= "<span class=\"cursor-pointer change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1\" 
            onclick=\"clients.updateClientLogin('" . $encCID . "', '1', 'cl_" . $item->CID . "');\"><i class='tooltipAjax flaticon2-delete' title=\"Click to allow login\"></a></span>";
        }
        $clientLoginRight .= '</span>';

        $clientEmailRights = '';
        if ($PCInfo && $PCInfo->allowEmailCampaign) {
            if ($item->sendMarketingEmail == 1) {
                $clientEmailRights = "
                <span 
                    id=\"allowToSendMarketingEmail_" . $item->CID . "\"><span 
                    onclick=\"clients.allowToSendMarketingEmailForClient('" . $encCID . "','0', 'allowToSendMarketingEmail_" . $item->CID . "');\" 
                    class='tooltipClass' 
                    data-html=\"true\"
                    data-toggle=\"tooltip\"
                    title=\"Back Office Users will be able to send marketing E-mails to your borrowers.\"
                ><img src=\"/assets/images/email-send.png\" 
                    alt=\"Lender(s) will be able to send marketing E-mails to your borrowers.\" 
                    width=\"25\" 
                    height=\"25\" /></span></span>";
            } else {
                $clientEmailRights = "<span  
                    id=\"allowToSendMarketingEmail_" . $item->CID . "\"><span 
                    onclick=\"clients.allowToSendMarketingEmailForClient('" . $encCID . "','1', 'allowToSendMarketingEmail_" . $item->CID . "');\" 
                    data-html=\"true\"
                    class='tooltipClass' 
                    data-toggle=\"tooltip\" 
                    title=\"Back office users will NOT be able to send marketing E-mails to your borrower, which protects the broker relationship.\"
               ><img src=\"/assets/images/email-not-validated.png\" 
               alt=\"\" 
               width=\"25\" 
               height=\"25\" /></span></span>";
            }
        }

        $clientEmailRights1 = '';
        if ($PCInfo && $PCInfo->allowEmailCampaign && $item->sendMarketingEmail == 1) {
            $clientEmailRights1 = "<span style=\"margin-left: 10px\"><label for=\"clientID_" . $item->CID . "\"><input class=\"clientMailCheckbox\" type=\"checkbox\" name=\"clientID[]\" 
                id=\"clientID_" . $item->CID . "\" value=\"" . $item->CID . "\"><span>&nbsp;</span></label></span>";
        }

        if (PageVariables::$allowEmpToCreateFiles) {
            $clientCreateNewFile = '';
            $HMLOLink = CONST_SITE_URL . 'backoffice/LMRequest.php?eOpt=0&cliType=PC&tabOpt=QAPP&cemail=' . cypher::myEncryption($clientEmailLink);
            $clientCreateNewFile = "<a target=\"_blank\" href=\"" . $HMLOLink . "\"
         class=\"btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1\" 
          data-original-title=\"Create New File\">
                            <i class=\"tooltipAjax fa fa-file\" title=\"Create New File\"></i>
                        </a>";
        }

        if (PageVariables::$userRole == 'Branch') {
            if ($LMRUserType == 'PLO') {
                $userUrl = CONST_URL_BRSSL;
            } else {
                $userUrl = CONST_URL_BR;
            }
        } elseif (PageVariables::$userRole == 'Agent') {
            if ($LMRUserType == 'PLO') {
                $userUrl = CONST_URL_AG_SSL;
            } else {
                $userUrl = CONST_URL_AG;
            }
        } elseif (PageVariables::$userRole == 'Client') {
            if ($LMRUserType == 'PLO') {
                $userUrl = CONST_URL_CL_SSL;
            } else $userUrl = CONST_URL_CL;
        } else {
            if ($LMRUserType == 'PLO') {
                $userUrl = CONST_URL_BOSSL;
            } else {
                $userUrl = CONST_BO_URL;
            }
        }

        return new self([
            'askPaymentBeforeLMR'    => $askPaymentBeforeLMR,
            'statusNotes'            => $statusNotes,
            'userUrl'                => $userUrl,
            'dealsUrl'               => $dealsUrl,
            'createdDate'            => $registerDate,
            'lastLogin'              => $lastLogin,
            'lastFileCreateDate'     => $lastFileCreateDate,
            'clientEmail'            => $clientEmail,
            'clientCity'             => $clientCity,
            'clientState'            => $clientState,
            'clAddress'              => $clAddress,
            'branch'                 => $branchDetails,
            'encLMRExecutiveID'      => $encLMRExecutiveID,
            'broker'                 => $agentDetails ? '<div style="max-height: 5.0em; overflow-y: scroll;">' . implode('<hr/>', $agentDetails) . '</div>' : '',
            'loanOfficer'            => $loDetails ? '<div style="max-height: 5.0em; overflow-y: scroll;">' . implode('<hr/>', $loDetails) . '</div>' : '',
            'contactInfo'            => $contactInfo,
            'clPwd'                  => $clPwd,
            'clientLoginRight'       => $clientLoginRight,
            'clientEmailRights'      => $clientEmailRights,
            'clActiveStatusDisp'     => $clActiveStatusDisp,
            'dispColor'              => $dispColor,
            'clientEmailRights1'     => $clientEmailRights1,
            'clientCreateNewFile'    => $clientCreateNewFile,
            'CID'                    => $item->CID,
            'entityName'             => $item->entityName,
            'clientName'             => $clientName,
            'internalInfoTags'       => $item->internalInfoTags,
            'noOfDeals'              => $item->noOfDeals,
            'internalInfoCreditLine' => $item->internalInfoCreditLine,
            'totalLoanAmount'        => Currency::formatDollarAmountWithDecimal($item->totalLoanAmount),
            'PCName'                 => $item->processingCompanyName,
        ]);
    }
}