<?php

namespace pages\backoffice\api_v2\dashboard\announcement;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\PageVariables;
use models\portals\BackofficePage;
use models\standard\HTTP;
use pages\backoffice\api_v2\dashboard\announcement\classes\AnnouncementDTO;
use Twilio\Page;

class announcement extends BackofficePage
{
    public static function Get()
    {
        // CALL SP_GetAnnouncementContent(0, '', UID, '', '', '', '', '', '', UType, 'Y', URole);

        if(glCustomJobForProcessingCompany::hideAnnouncements(PageVariables::$PCID) || PageVariables::$userEmail === '<EMAIL>') {
            HTTP::ExitJSON(['announcement' => null]);
        }

        $note = AnnouncementDTO::getReport(
            PageVariables::$userNumber,
            PageVariables::$userGroup,
            PageVariables::$userRole
        );

        if($note && $note->showAnnouncement) {
            HTTP::ExitJSON(['announcement' => $note]);
        }

        HTTP::ExitJSON(['announcement' => null]);
    }
}