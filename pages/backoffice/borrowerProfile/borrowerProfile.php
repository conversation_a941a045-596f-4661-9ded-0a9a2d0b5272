<?php

namespace pages\backoffice\borrowerProfile;

use models\constants\borrowerBusinessEntity;
use models\constants\gblIntendedOccupancy;
use models\constants\gblLoanStatus;
use models\constants\gblType;
use models\constants\gl\glCountryArray;
use models\constants\gl\glDate;
use models\constants\gl\glInvestType;
use models\constants\gl\glScheduleStatus;
use models\constants\GpropertyTypeNumbArray;
use models\Controllers\backoffice\borrower\Docs;
use models\Controllers\loanForm;
use models\cypher;
use models\lendingwise\db\tblBorrowerEntityDocs_db;
use models\lendingwise\db\tblBorrowerEntityMemberDocs_db;
use models\lendingwise\db\tblBorrowerProfileUploadedDocs_db;
use models\lendingwise\db\tblPCClientEntityInfo_db;
use models\lendingwise\tblBorrowerEntityDocs;
use models\lendingwise\tblBorrowerEntityMemberDocs;
use models\lendingwise\tblBorrowerProfileUploadedDocs;
use models\lendingwise\tblMembersOfficers;
use models\lendingwise\tblPCClientEntityInfo;
use models\lendingwise\tblPCClientEntityMembers;
use models\PageVariables;
use models\portals\BackofficePage;
use models\Request;
use models\standard\Arrays;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use pages\backoffice\borrowerProfile\classes\borrowerProfileEntityInfo;


class borrowerProfile extends BackofficePage
{
    //public static ?array $entityMembers = null;

    public static ?string $html = null;
    public static ?string $closeHtml = '</div></div></div></div>';
    public static ?int $isBorrowerProfile = 0;
    public static ?int $number = 0;
    public static ?int $PCID = 0;
    public static ?int $isPageView = 1;
    public static function Init()
    {
        parent::Init();

    }

    public static function Get()
    {
        $MOID = cypher::myDecryption(Request::GetClean('MOID')) ?? 0;
        $CID = cypher::myDecryption(Request::GetClean('CID')) ?? 0;
        $CBEID = cypher::myDecryption(Request::GetClean('CBEID')) ?? 0;
        $PCID = cypher::myDecryption(Request::GetClean('encPCID')) ?? 0;
        self::$isBorrowerProfile = 1;
        self::$isPageView = 0;
        self::$number = intval(Request::GetClean('number')) ?? 0;
        self::$PCID = $PCID;
        $entityMembers = borrowerProfileEntityInfo::getReport(
            (int)$CID,
            (int)$CBEID,
            (int)$MOID
        );
        loanForm::init(PageVariables::$PCID, 'BO');
        loanForm::$fileTab = 'BO';
        loanForm::pushSectionID('BEN');
        if (!$entityMembers) { //New Data
            $number = Request::GetClean('number');
            for ($m = 1; $m <= $number; $m++) {
                $table = new tblMembersOfficers();
                $table->parent_id = intval($MOID); //new data
                self::$html .= self::getAccordion(0, $m, $table);
            }
        } else {
            self::getMembers($CID, $CBEID, null, 'Members');
        }
        loanForm::popSectionID();
        HTTP::ExitJSON(['html' => self::$html]);
    }

    public static function Post()
    {
        $rowId = cypher::myDecryption(Request::GetClean('rowId')) ?? 0;
        if(!$rowId) {
            HTTP::ExitJSON(['error' => 'Invalid Request'], HTTP::HTTP_STATUS_BAD_REQUEST);
        }
        $t = tblPCClientEntityMembers::Get(['id' => $rowId]);
        if(!$t) {
            HTTP::ExitJSON(['error' => 'Invalid Request'], HTTP::HTTP_STATUS_BAD_REQUEST);
        }
        $t->Delete();
        HTTP::ExitJSON([
            'success' => 'Deleted Successfully',
        ]);
    }

    public static function getMembers(int $CID, int $CBEID, ?int $parent_id = null, ?string $header = null): ?string
    {

        loanForm::init(PageVariables::$PCID, 'BO');
        loanForm::$fileTab = 'BO';
        loanForm::pushSectionID('BEN');
        $parentMembers = borrowerProfileEntityInfo::getRootMembers($CID, $CBEID, $parent_id);
        if (!sizeof($parentMembers)) {
            for ($m = 1; $m <= self::$number; $m++) {
                $table = new tblMembersOfficers();
                self::$html .= self::getAccordion(0, $m, $table);
            }
            return self::$html;
        }
        $i = 1;
        foreach ($parentMembers as $member) { //loop-1
            $title = self::getHeaderTitle($member, $i, $header);
            self::$html .= '
            <!--begin::Accordion-->
            <div class="accordion" id="accordionExample_' . cypher::myEncryption($member->MOID) . self::$isPageView . '">
                <div class="card">
                    <div class="card-header d-flex">
                        <div class="col-md-10 card-title" data-toggle="collapse" data-target="#collapseOne' . $member->MOID . self::$isPageView . '" >
                        ' . $title . '
                        </div>
                        <div class="col-md-2 text-right">
                            <span
                            class="btn btn-sm btn-danger btn-icon m-2 tooltipClass"
                            title=""
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Click to remove Individual/Entity info"
                            data-rem-tableid="' . cypher::myEncryption($member->MOID) . '"
                            data-rem-cid="' . cypher::myEncryption($member->CID) . '"
                            data-rem-cbeid="' . cypher::myEncryption($member->CBEID) . '"
                            data-rem-accordion="accordionExample_' . cypher::myEncryption($member->MOID) . self::$isPageView . '"
                            onclick="borrowerProfile.removeEntityMemberData(this)">
                                    <i class="icon-1x fas fa-minus-circle"></i>
                            </span>
                            <span
                            class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon m-2 toggleClass"
                            data-card-tool="toggle"
                            data-target="#collapseOne' . $member->MOID . self::$isPageView . '"
                            data-toggle="collapse"
                            title=""
                            data-original-title="Toggle Card">
                                <i class="icon-1x ki icon-nm ki-arrow-up"></i>
                            </span>
                        </div>
                    </div>
                    <div id="collapseOne' . $member->MOID . self::$isPageView . '"
                    class="collapse"
                    data-parent="#accordionExample_' . cypher::myEncryption($member->MOID) . self::$isPageView . '">
                    <div class="card-body">';
                    $childCountData = borrowerProfileEntityInfo::getRootMembers($CID, $CBEID, $member->MOID);
                    $childCount = count($childCountData);
                    self::getFormData($member, $childCount);
                    self::getMembers($CID, $CBEID, $member->MOID);
                    self::$html .= self::$closeHtml;
        $i++;
        }
        loanForm::popSectionID();
        return self::$html;
    }

    /**
     * @param tblMembersOfficers $item
     * @param int $childCount
     * @return void
     */
    public static function getFormData(tblMembersOfficers  $item, int $childCount = 0)
    {
        //To avoid conflict with pop up and page data
        $disabled = self::$isPageView ? 'disabled=disabled' : '';

        $parent_id = $item->parent_id ?: 0;
        if (!$item->MOID) $item->MOID = 0;
        if (!$item->memberType) {
            $item->memberType = 'Individual'; //Default
        }
        $individualChecked = $item->memberType == 'Individual' ? 'checked=checked' : '';
        $entityChecked = $item->memberType == 'Entity' ? 'checked=checked' : '';

        $memberPersonalGuaranteeYes = $item->memberPersonalGuarantee == 'Yes' ? 'checked=checked' : '';
        $memberPersonalGuaranteeNo = $item->memberPersonalGuarantee == 'No' ? 'checked=checked' : '';
        $memberAuthorizedSignerYes = $item->memberAuthorizedSigner == 'Yes' ? 'checked=checked' : '';
        $memberAuthorizedSignerNo = $item->memberAuthorizedSigner == 'No' ? 'checked=checked' : '';
        $memberCitizenshipUSCitizen = $item->memberCitizenship == 'U.S. Citizen' ? 'checked=checked' : '';
        $memberCitizenshipPermResident = $item->memberCitizenship == 'Perm Resident Alien' ? 'checked=checked' : '';
        $memberCitizenshipNonPermResident = $item->memberCitizenship == 'Non-Perm Resident Alien' ? 'checked=checked' : '';
        $memberCitizenshipForeignNational = $item->memberCitizenship == 'Foreign National' ? 'checked=checked' : '';


        //Hide or Show fields based on member type
        $memberTitleHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberAnnualSalaryHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberSSNHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberDOBHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberCreditScoreHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberEmailHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberDriversLicenseHideShow  = $item->memberType == 'Entity' ? 'hide' : '';
        $memberDriversLicenseStateHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberTinHideShow = $item->memberType != 'Entity' ? 'hide' : '';
        $memberPersonalGuaranteeHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberAuthorizedSignerHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberCitizenshipHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $howManyMemberOfficerHideShow = $item->memberType != 'Entity' ? 'hide' : ''; //Entity only
        $memberMaritalStatusHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberMarriageDateHideShow = ($item->memberType == 'Entity' || $item->memberMaritalStatus == 'Unmarried') ? 'hide' : '';
        $memberDivorceDateHideShow = ($item->memberType == 'Entity' || $item->memberMaritalStatus == 'Unmarried' || $item->memberMaritalStatus == 'Married') ? 'hide' : '';
        $memberMaidenNameHideShow = ($item->memberType == 'Entity' || $item->memberMaritalStatus == 'Unmarried') ? 'hide' : '';
        $memberSpouseNameHideShow = ($item->memberType == 'Entity' || $item->memberMaritalStatus == 'Unmarried') ? 'hide' : '';

        $memberCreditScoreDateHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberRentOrOwnHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberMonthlyRentOrMortgageHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberDateMovedAddressHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberRealEstateValueHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberRetirementAccountBalanceHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberCashSavingsStocksBalanceHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberCreditCardBalanceHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberMortgageBalanceHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberAutoLoanBalanceHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberTotalNetWorthHideShow = $item->memberType == 'Entity' ? 'hide' : '';

        //Rent or Own Selected
        $memberRentSelected = $item->memberRentOrOwn == 'Rent' ? 'selected=selected' : '';
        $memberOwnSelected = $item->memberRentOrOwn == 'Own' ? 'selected=selected' : '';

        self::$html .= '<div class="row">
        <input type="hidden" name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberId]" value="' . $item->MOID . '">        
        <input type="hidden" name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][parent_id]" value="' . $parent_id . '">
        <input type="hidden" name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][CID]" value="' . $item->CID . '">
        <input type="hidden" name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][CBEID]" value="' . $item->CBEID . '">
        <div class="col-md-3">
            <div class="radio-inline">
                <label class="radio radio-solid font-weight-bold">
                    <input
                    type="radio"
                    name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberType]"
                    value="Individual"
                    id="memberType' . $item->MOID . '_' . $parent_id . '_individual"
                    ' . $individualChecked . '
                    ' . $disabled . '
                    onchange="BusinessEntitySection.memberTypeShowHideFieldsCV3(' . "'Individual_$item->MOID" . "_" . "$parent_id'" . ',this);"
                    >
                    <span></span>Individual
                </label>
                <label class="radio radio-solid font-weight-bold">
                    <input
                    type="radio"
                    name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberType]"
                    value="Entity"
                    id="memberType' . $item->MOID . '_' . $parent_id . '_entity"
                    ' . $entityChecked . '
                    ' . $disabled . '
                    onchange="BusinessEntitySection.memberTypeShowHideFieldsCV3(' . "'Entity_$item->MOID" . "_" . "$parent_id'" . ',this);"
                    >
                    <span></span>Entity
                </label>
            </div>
        </div>

        <div class="col-md-3" id="">
            <div class="form-group">
                ' . loanForm::label('memberName') . '
                <div class="">
                    <input name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberName]"
                            class="form-control input-sm"
                            type="text"
                            value="' . $item->memberName . '"
                            ' . $disabled . ' 
                            id="memberName' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberTitleHideShow . ' " id="memberTitle_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberTitle') . '
                <div class="">
                    <input name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberTitle]"
                            class="form-control input-sm"
                            type="text"
                            value="' . $item->memberTitle . '"
                            ' . $disabled . '
                            id="memberTitle' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="form-group">
                ' . loanForm::label('memberCategory') . '
                <div class="">
                    <select class="form-control input-sm" ' . $disabled . '
                    name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCategory]"
                    id="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCategory]">
                        <option value="">-Select-</option>';
                            foreach (borrowerBusinessEntity::getEntityTypes() as $category) {
                                $memberCategory = $item->memberCategory == $category ? 'selected=selected' : '';
                                self::$html .= '<option value="' . $category . '" ' . $memberCategory . ' >' . $category . '</option>';
                            }
        self::$html .= '</select>
                </div>
            </div>
        </div>

        <div class="col-md-3" id="">
            <div class="form-group">
                <label class="font-weight-bold">What % Ownership of ' . $item->memberName . '? </label>
                <div class="">
                    <div class="input-group">
                        <input name="members[' . $item->MOID . '][entityMember][' . $parent_id. '][memberOwnership]"
                        class="form-control input-sm memberOwnership_' . $parent_id . '"
                        type="text"
                        value="' . htmlentities($item->memberOwnership) . '"
                        ' . $disabled . '
                        id="memberOwnership' . $item->MOID . '_' . $parent_id . '"
                        onblur="BusinessEntitySection.calculateOwnerShip(this)" >
                        <div class="input-group-append">
                            <span class="input-group-text">
                                %
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberAnnualSalaryHideShow . ' " id="memberAnnualSalary_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberAnnualSalary') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberAnnualSalary]"
                                class="form-control input-sm"
                                type="text" onblur="currencyConverter(this, this.value)"
                                value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberAnnualSalary)) . '"
                                ' . $disabled . '
                                id="memberAnnualSalary' . $item->MOID . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3" id="">
            <div class="form-group">
                ' . loanForm::label('memberAddress') . '
                <script>
                    $(document).ready(function() {
                        $("#memberAddress' . $item->MOID . '_' . $parent_id . '").on("input", function() {
                            address_lookup.InitLegacy($(this));
                        });
                    });
                </script>
                <div class="">
                    <input name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberAddress]"
                            class="form-control input-sm"
                            type="text"
                            value="' . htmlentities($item->memberAddress) . '"
                            ' . $disabled . '
                            id="memberAddress' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3" id="">
            <div class="form-group">
                ' . loanForm::label('memberPhone') . '
                <div class="">
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberPhone]"
                            class="form-control input-sm mask_phone"
                            type="text"
                            placeholder="(___) ___ - ____ Ext ____"
                            value="' . Strings::formatPhoneNumber($item->memberPhone) . '"
                            ' . $disabled . '
                            id="memberPhone' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3" id="">
            <div class="form-group">
                ' . loanForm::label('memberCell') . '
                <div class="">
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCell]"
                            class="form-control input-sm mask_cellnew"
                            type="text"
                            placeholder="(___) ___ - ____"
                            value="' . Strings::formatCellNumber($item->memberCell) . '"
                            ' . $disabled . '
                            id="memberCell' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberSSNHideShow . ' " id="memberSSN_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberSSN') . '
                <div class="">
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberSSN]"
                            class="form-control input-sm mask_ssn"
                            type="text"
                            placeholder="___ - __ - ____"
                            value="' . Strings::formatSSNNumber($item->memberSSN) . '"
                            ' . $disabled . '
                            id="memberSSN' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberDOBHideShow . ' " id="memberDOB_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDOB') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberDOB]"
                            class="form-control input-sm dateNewClass"
                            type="text" autocomplete="off"
                            placeholder="MM/DD/YYYY"
                            data-date-dob-start-date="' . glDate::getMinRequirementDate() . '"
                            data-date-dob-end-date="' . glDate::getMaxRequirementDate() . '"
                            value="' . Dates::formatDateWithRE($item->memberDOB, 'YMD', 'm/d/Y') . '"
                            ' . $disabled . '
                            id="memberDOB' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberEmailHideShow . ' " id="memberEmail_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberEmail') . '
                <div class="">
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberEmail]"
                            class="form-control input-sm"
                            type="text"
                            value="' . $item->memberEmail . '"
                            ' . $disabled . '
                            id="memberEmail' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberCreditScoreHideShow . ' " id="memberCreditScore_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCreditScore') . '
                <div class="">
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCreditScore]"
                            class="form-control input-sm memberCreditScore"
                            type="text"
                            value="' . $item->memberCreditScore . '"
                            ' . $disabled . '
                            id="memberCreditScore' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberCreditScoreDateHideShow . ' " id="memberCreditScoreDate_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCreditScoreDate') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCreditScoreDate]"
                            class="form-control input-sm dateNewClass"
                            type="text" autocomplete="off"
                            placeholder="MM/DD/YYYY"
                            value="' . Dates::formatDateWithRE($item->memberCreditScoreDate, 'YMD', 'm/d/Y') . '"
                            ' . $disabled . '
                            id="memberCreditScoreDate' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberRentOrOwnHideShow . ' " id="memberRentOrOwn_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberRentOrOwn') . '
                <div class="">
                    <select name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberRentOrOwn]" ' . $disabled . '
                    class="form-control input-sm" id="memberRentOrOwn' . $item->MOID . '_' . $parent_id . '">
                        <option value="">-Select-</option>
                        <option value="Rent" ' . $memberRentSelected . '>Rent</option>
                        <option value="Own" ' . $memberOwnSelected . '>Own</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberMonthlyRentOrMortgageHideShow . ' " id="memberMonthlyRentOrMortgage_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMonthlyRentOrMortgage') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberMonthlyRentOrMortgage]"
                                class="form-control input-sm"
                                type="text" onblur="currencyConverter(this, this.value)"
                                value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberMonthlyRentOrMortgage)) . '"
                                ' . $disabled . '
                                id="memberMonthlyRentOrMortgage' . $item->MOID . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberDateMovedAddressHideShow . ' " id="memberDateMovedAddress_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDateMovedAddress') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberDateMovedAddress]"
                            class="form-control input-sm dateNewClass"
                            type="text" autocomplete="off"
                            placeholder="MM/DD/YYYY"
                            value="' . Dates::formatDateWithRE($item->memberDateMovedAddress, 'YMD', 'm/d/Y') . '"
                            ' . $disabled . '
                            id="memberDateMovedAddress' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberRealEstateValueHideShow . ' " id="memberRealEstateValue_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberRealEstateValue') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberRealEstateValue]"
                                class="form-control input-sm"
                                type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberRealEstateValue)) . '"
                                ' . $disabled . '
                                data-index="' . $item->MOID . '_' . $parent_id . '"
                                id="memberRealEstateValue' . $item->MOID . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberRetirementAccountBalanceHideShow . ' " id="memberRetirementAccountBalance_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberRetirementAccountBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberRetirementAccountBalance]"
                                class="form-control input-sm"
                                type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberRetirementAccountBalance)) . '"
                                ' . $disabled . '
                                data-index="' . $item->MOID . '_' . $parent_id . '"
                                id="memberRetirementAccountBalance' . $item->MOID . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberCashSavingsStocksBalanceHideShow . ' " id="memberCashSavingsStocksBalance_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCashSavingsStocksBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCashSavingsStocksBalance]"
                                class="form-control input-sm"
                                type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberCashSavingsStocksBalance)) . '"
                                ' . $disabled . '
                                data-index="' . $item->MOID . '_' . $parent_id . '"
                                id="memberCashSavingsStocksBalance' . $item->MOID . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberCreditCardBalanceHideShow . ' " id="memberCreditCardBalance_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCreditCardBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCreditCardBalance]"
                                class="form-control input-sm"
                                type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberCreditCardBalance)) . '"
                                ' . $disabled . '
                                data-index="' . $item->MOID . '_' . $parent_id . '"
                                id="memberCreditCardBalance' . $item->MOID . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberMortgageBalanceHideShow . ' " id="memberMortgageBalance_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMortgageBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberMortgageBalance]"
                                class="form-control input-sm"
                                type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberMortgageBalance)) . '"
                                ' . $disabled . '
                                data-index="' . $item->MOID . '_' . $parent_id . '"
                                id="memberMortgageBalance' . $item->MOID . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberAutoLoanBalanceHideShow . ' " id="memberAutoLoanBalance_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberAutoLoanBalance') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberAutoLoanBalance]"
                                class="form-control input-sm"
                                type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberAutoLoanBalance)) . '"
                                ' . $disabled . '
                                data-index="' . $item->MOID . '_' . $parent_id . '"
                                id="memberAutoLoanBalance' . $item->MOID . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberTotalNetWorthHideShow . ' " id="memberTotalNetWorth_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberTotalNetWorth') . '
                <div class="">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">$</span>
                        </div>
                        <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberTotalNetWorth]"
                                class="form-control input-sm" readonly
                                type="text" onblur="currencyConverter(this, this.value)"
                                value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberTotalNetWorth)) . '"
                                ' . $disabled . '
                                id="memberTotalNetWorth' . $item->MOID . '_' . $parent_id . '">
                    </div>
                </div>
            </div>
        </div>       
        <div class="col-md-3 ' . $memberMaritalStatusHideShow . ' " id="memberMaritalStatus_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMaritalStatus') . '
                <div class="">
                    <div class="radio-inline">
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberMaritalStatus]"
                            id="memberMaritalStatusUnmarried_' . $item->MOID . '_' . $parent_id . '_Unmarried"
                            data-index="' . $item->MOID . '_' . $parent_id . '"
                            value="Unmarried" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                            ' . $disabled . '
                            ' . ($item->memberMaritalStatus == 'Unmarried' ? 'checked=checked' : '') . '
                            ><span></span>Unmarried
                        </label>
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberMaritalStatus]"
                            id="memberMaritalStatusMarried_' . $item->MOID . '_' . $parent_id . '_Married"
                            data-index="' . $item->MOID . '_' . $parent_id . '"
                            value="Married" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                            ' . $disabled . '
                            ' . ($item->memberMaritalStatus == 'Married' ? 'checked=checked' : '') . '
                            ><span></span>Married
                        </label>
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberMaritalStatus]"
                            id="memberMaritalStatusSeparated_' . $item->MOID . '_' . $parent_id . '_Separated"
                            data-index="' . $item->MOID . '_' . $parent_id . '"
                            value="Separated" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                            ' . $disabled . '
                            ' . ($item->memberMaritalStatus == 'Separated' ? 'checked=checked' : '') . '
                            ><span></span>Separated
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberMarriageDateHideShow . ' " id="memberMarriageDate_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMarriageDate') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberMarriageDate]"
                            class="form-control input-sm dateNewClass"
                            type="text"
                            placeholder="MM/DD/YYYY"
                            value="' . Dates::formatDateWithRE($item->memberMarriageDate, 'YMD', 'm/d/Y') . '"
                            ' . $disabled . '
                            id="memberMarriageDate' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberDivorceDateHideShow . ' " id="memberDivorceDate_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDivorceDate') . '
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">
                            <i class="fa fa-calendar text-primary"></i>
                        </span>
                    </div>
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberDivorceDate]"
                            class="form-control input-sm dateNewClass"
                            type="text"
                            placeholder="MM/DD/YYYY"
                            value="' . Dates::formatDateWithRE($item->memberDivorceDate, 'YMD', 'm/d/Y') . '"
                            ' . $disabled . '
                            id="memberDivorceDate' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberMaidenNameHideShow . ' " id="memberMaidenName_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberMaidenName') . '
                <div class="">
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberMaidenName]"
                            class="form-control input-sm"
                            type="text"
                            value="' . $item->memberMaidenName . '"
                            ' . $disabled . '
                            id="memberMaidenName' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberSpouseNameHideShow . ' " id="memberSpouseName_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberSpouseName') . '
                <div class="">
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberSpouseName]"
                            class="form-control input-sm"
                            type="text"
                            value="' . $item->memberSpouseName . '"
                            ' . $disabled . '
                            id="memberSpouseName' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>
        <div class="col-md-3 ' . $memberDriversLicenseStateHideShow . ' " id="memberDriversLicenseState_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDriversLicenseState') . '
                <div class="">
                    <select name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberDriversLicenseState]" ' . $disabled . '
                    class="form-control input-sm" id="memberDriversLicenseState' . $item->MOID . '_' . $parent_id . '">
                        <option value="">-Select-</option>';
                            foreach(Arrays::fetchStates() as $dlState) {
                                $licenseState = $item->memberDriversLicenseState == $dlState['stateCode'] ? 'selected=selected' : '';
                                self::$html .= '<option value="' . $dlState['stateCode'] . '" ' . $licenseState . ' >' . $dlState['stateName'] . '</option>';
                            }
            self::$html .= '</select>
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberDriversLicenseHideShow . ' " id="memberDriversLicense_' . $item->MOID  . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberDriversLicense') . '
                <div class="">
                    <input  name="members[' . $item->MOID  . '][entityMember][' . $parent_id . '][memberDriversLicense]"
                            class="form-control input-sm"
                            type="text"
                            value="' . htmlentities($item->memberDriversLicense) . '"
                            ' . $disabled . '
                            id="memberDriversLicense' . $item->MOID  . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberTinHideShow . ' " id="memberTin_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberTin') . '
                <div class="">
                    <input  name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberTin]"
                            class="form-control input-sm mask_ein"
                            type="text"
                            placeholder="__-_______"
                            value="' . $item->memberTin . '"
                            ' . $disabled . '
                            id="memberTin' . $item->MOID . '_' . $parent_id . '">
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberPersonalGuaranteeHideShow . ' " id="memberPersonalGuarantee_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberPersonalGuarantee') . '
                <div class="">
                    <div class="radio-inline">
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberPersonalGuarantee]"
                            id="memberPersonalGuaranteeYes_' . $item->MOID . '_' . $parent_id . '_Yes"
                            ' . $memberPersonalGuaranteeYes . '
                            ' . $disabled . '
                            value="Yes"><span></span>Yes
                        </label>
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberPersonalGuarantee]"
                            id="memberPersonalGuaranteeNo_' . $item->MOID . '_' . $parent_id . '_No"
                            ' . $memberPersonalGuaranteeNo . '
                            ' . $disabled . '
                            value="No"><span></span>No
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberAuthorizedSignerHideShow . ' " id="memberAuthorizedSigner_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberAuthorizedSigner') . '
                <div class="">
                    <div class="radio-inline">
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberAuthorizedSigner]"
                            id="memberAuthorizedSignerYes_' . $item->MOID . '_' . $parent_id . '_Yes"
                            ' . $memberAuthorizedSignerYes . '
                            ' . $disabled . '
                            value="Yes"><span></span>Yes
                        </label>
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberAuthorizedSigner]"
                            id="memberAuthorizedSignerNo_' . $item->MOID . '_' . $parent_id . '_No"
                            ' . $memberAuthorizedSignerNo . '
                            ' . $disabled . '
                            value="No"><span></span>No
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 ' . $memberCitizenshipHideShow . '  " id="memberCitizenship_' . $item->MOID  . '_' . $parent_id . '">
            <div class="form-group">
                ' . loanForm::label('memberCitizenship') . '
                <div class="">
                    <div class="radio-inline">
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCitizenship]"
                            id="memberCitizenship_us_' . $item->MOID  . '_' . $parent_id . '_US"
                            ' . $memberCitizenshipUSCitizen . '
                            ' . $disabled . '
                            value="U.S. Citizen"><span></span>U.S.
                            Citizen
                        </label>
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCitizenship]"
                            id="memberCitizenship_pra_' . $item->MOID  . '_' . $parent_id . '_PR"
                            ' . $memberCitizenshipPermResident . '
                            ' . $disabled . '
                            value="Perm Resident Alien"><span></span>Perm
                            Resident
                        </label>
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCitizenship]"
                            id="memberCitizenship_npra_' . $item->MOID  . '_' . $parent_id . '_NPR"
                            ' . $memberCitizenshipNonPermResident . '
                            ' . $disabled . '
                            value="Non-Perm Resident Alien"><span></span>Non-Perm
                            Resident
                        </label>
                        <label class="radio radio-solid font-weight-bold">
                            <input type="radio"
                            class=""
                            name="members[' . $item->MOID . '][entityMember][' . $parent_id . '][memberCitizenship]"
                            id="memberCitizenship_fn_' . $item->MOID  . '_' . $parent_id . '_FN"
                            ' . $memberCitizenshipForeignNational . '
                            ' . $disabled . '
                            value="Foreign National"><span></span>Foreign
                            National
                        </label>
                    </div>
                </div>
            </div>
        </div>';

        self::$html .= '<div class="col-md-12 p-0">'.
            self::getMembersDocsUploadHtml(
                self::$PCID,
                $item->CID,
                $item->CBEID,
                $parent_id,
                $item->memberType,
                $item->memberName,
                $item->MOID 
            )
            .'</div>';

        if (!borrowerProfile::$isPageView) {
            self::$html .='<div class="col-md-6 ' . $howManyMemberOfficerHideShow . ' " id="howManyMemberOfficer_' . $item->MOID . '_' . $parent_id . '">
            <div class="form-group row">
                <label class="col-md-10 font-weight-bold">
                    How many members/officers are there with 20%+ ownership?
                </label>
                <div class="col-md-2">
                    <input
                            class="form-control input-sm"
                            type="text"
                            name=""
                            value="' . $childCount . '"
                            id="' . $item->MOID . '_' . $parent_id . '"
                            maxlength="2"
                            data-moid="' . cypher::myEncryption($item->MOID) . '"
                            data-cid="' . cypher::myEncryption($item->CID) . '"
                            data-cbeid="' . cypher::myEncryption($item->CBEID) . '"
                            ' . $disabled . '
                            onchange="borrowerProfile.getEntityInfoMembers(this)">
                </div>
            </div>
        </div>
        <div class="col-md-12" id="formContainer_' . $item->MOID . '_' . $parent_id . '"></div>';
        }

        self::$html .='</div>';
    }

    /**
     * @param $item
     * @param $i
     * @param $header
     * @return string
     */
    public static function getHeaderTitle($item, $i, $header): string
    {
        $individualEntityMemberCount = borrowerProfileEntityInfo::getIndividualEntityMemberCount($item->MOID, $item->CBEID);
        if ($item->memberType === 'Entity') {
            $title = 'Member/Officer #' . $i . ' of ' . $item->memberName . ' > ' . $header;
        } else {
            $title = 'Member/Officer #' . $i . ' of ' . $header . ' > ' . $item->memberName;
        }
        $title .= $individualEntityMemberCount->individualCount ? ' |(' . $individualEntityMemberCount->individualCount . ')Individual' : '';
        $title .= $individualEntityMemberCount->entityCount ? ' |(' . $individualEntityMemberCount->entityCount . ')Entity' : '';
        return $title;
    }


    public static function getAccordion($inc, $i, $item): string
    {
        $ml = $inc * 5;
        $row = $inc = $inc + 1;
        if (!$item->memberType) {
            $item->memberType = 'Individual'; //Default
        }

        $individualChecked = $item->memberType == 'Individual' ? 'checked=checked' : '';
        $entityChecked = $item->memberType == 'Entity' ? 'checked=checked' : '';

        $memberPersonalGuaranteeYes = $item->memberPersonalGuarantee == 'Yes' ? 'checked=checked' : '';
        $memberPersonalGuaranteeNo = $item->memberPersonalGuarantee == 'No' ? 'checked=checked' : '';
        $memberAuthorizedSignerYes = $item->memberAuthorizedSigner == 'Yes' ? 'checked=checked' : '';
        $memberAuthorizedSignerNo = $item->memberAuthorizedSigner == 'No' ? 'checked=checked' : '';
        $memberCitizenshipUSCitizen = $item->memberCitizenship == 'U.S. Citizen' ? 'checked=checked' : '';
        $memberCitizenshipPermResident = $item->memberCitizenship == 'Perm Resident Alien' ? 'checked=checked' : '';
        $memberCitizenshipNonPermResident = $item->memberCitizenship == 'Non-Perm Resident Alien' ? 'checked=checked' : '';
        $memberCitizenshipForeignNational = $item->memberCitizenship == 'Foreign National' ? 'checked=checked' : '';


        //Hide or Show fields based on a member type
        $memberTitleHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberAnnualSalaryHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberSSNHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberDOBHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberCreditScoreHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberEmailHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberDriversLicenseHideShow  = $item->memberType == 'Entity' ? 'hide' : '';
        $memberDriversLicenseStateHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberTinHideShow = $item->memberType != 'Entity' ? 'hide' : '';
        $memberPersonalGuaranteeHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberAuthorizedSignerHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberCitizenshipHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberMaritalStatusHideShow = $item->memberType == 'Entity' ? 'hide' : '';
        $memberMarriageDateHideShow = ($item->memberType == 'Entity' || $item->memberMaritalStatus == 'Unmarried') ? 'hide' : '';
        $memberDivorceDateHideShow = ($item->memberType == 'Entity' || $item->memberMaritalStatus == 'Unmarried' || $item->memberMaritalStatus == 'Married') ? 'hide' : '';
        $memberMaidenNameHideShow = ($item->memberType == 'Entity' || $item->memberMaritalStatus == 'Unmarried') ? 'hide' : '';
        $memberSpouseNameHideShow = ($item->memberType == 'Entity' || $item->memberMaritalStatus == 'Unmarried') ? 'hide' : '';

        $memberCreditScoreDateHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberRentOrOwnHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberMonthlyRentOrMortgageHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberDateMovedAddressHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberRealEstateValueHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberRetirementAccountBalanceHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberCashSavingsStocksBalanceHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberCreditCardBalanceHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberMortgageBalanceHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberAutoLoanBalanceHideShow = ($item->memberType == 'Entity') ? 'hide' : '';
        $memberTotalNetWorthHideShow = ($item->memberType == 'Entity') ? 'hide' : '';

        $howManyMemberOfficerHideShow = $item->memberType != 'Entity' ? 'hide' : ''; //Entity only

        //Rent or Own Selected
        $memberRentSelected = $item->memberRentOrOwn == 'Rent' ? 'selected=selected' : '';
        $memberOwnSelected = $item->memberRentOrOwn == 'Own' ? 'selected=selected' : '';

        $html = '<div class="accordion ml-' . $ml . '" id="accordionExample_' . $inc . '_' . $i . '">
    <div class="card" id="accordion_' . $inc . '_' . $i . '">
        <div class="card-header d-flex">
            <div class="col-md-6 card-title" data-toggle="collapse" data-target="#collapseOne_' . $inc . '_' . $i . '">
                Member/Officer #' . $i . '
            </div>
            <div class="col-md-6 text-right">
                <span class="btn btn-sm btn-danger btn-icon m-2 tooltipClass"
                    title="" data-toggle="tooltip" data-placement="top"
                    data-original-title="Click to remove Individual/Entity info"
                    data-rem-accordion="accordionExample_' . $inc . '_' . $i . '"
                    data-rem-tableid="' . cypher::myEncryption($item->MOID) . '"
                    data-rem-cbeid="' . cypher::myEncryption($item->CBEID) . '"
                    data-rem-rowid="' . $inc . '_' . $i . '"
                    data-rem-level="parent"
                    onclick="BusinessEntitySection.removeEntityMemberFieldsCV3(this);"
                    >
                        <i class="icon-1x fas fa-minus-circle"></i>
                </span>
                <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon m-2 toggleClass"
                      data-card-tool="toggle"
                      data-target="#collapseOne_' . $inc . '_' . $i . '"
                      data-toggle="collapse"
                      title=""
                      data-original-title="Toggle Card">
                    <i class="icon-1x ki ki-arrow-down icon-nm"></i>
                </span>
            </div>
        </div>
        <div id="collapseOne_' . $inc . '_' . $i . '" class="collapse" data-parent="#accordionExample_' . $inc . '_' . $i . '">
            <div class="card-body">
                <div class="row">
                    <input type="hidden" 
                        name="members[' . $inc . '][entityMember][' . $i . '][MOID]" 
                        id="MOID' . $inc . '_' . $i . '"
                        value="' . $item->MOID . '">
                        <input type="hidden" 
                        name="members[' . $inc . '][entityMember][' . $i . '][parent_id]"
                        id="parent_id_' . $inc . '_' . $i . '"
                        value="' . $item->parent_id . '">
                    <div class="col-md-3">
                        <div class="radio-inline">
                            <label class="radio">
                                <input
                                type="radio"
                                name="members[' . $inc . '][entityMember][' . $i . '][memberType]"
                                value="Individual"
                                id="Individual_' . $inc . '_' . $i . '"
                                ' . $individualChecked . '
                                onchange="BusinessEntitySection.memberTypeShowHideFieldsCV3(' . "'Individual_$inc" . "_" . "$i'" . ',this);"
                                >
                                <span></span>Individual
                            </label>
                            <label class="radio">
                                <input
                                type="radio"
                                name="members[' . $inc . '][entityMember][' . $i . '][memberType]"
                                value="Entity"
                                id="Entity_' . $inc . '_' . $i . '"
                                ' . $entityChecked . '
                                onchange="BusinessEntitySection.memberTypeShowHideFieldsCV3(' . "'Entity_$inc" . "_" . "$i'" . ',this);"
                                >
                                <span></span>Entity
                            </label>
                            <span class="font-weight-bold text-danger hide" id="new_member_' . $inc . '_' . $i . '">
                                Please save to be able to add members to this entity.
                            </span>
                        </div>
                    </div>
                    <div class="col-md-3" id="">
                        <div class="form-group">
                            ' . loanForm::label('memberName') . '
                            <div class="">
                                <input name="members[' . $inc . '][entityMember][' . $i . '][memberName]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberName') . ' "
                                        type="text" autocomplete="off"
                                        value="' . $item->memberName . '"
                                        ' . loanForm::isEnabled('memberName') . '
                                        id="memberName' . $inc . '_' . $i . '">
                            </div>
                        </div>  
                    </div>
                    <div class="col-md-3 ' . $memberTitleHideShow . ' " id="memberTitle_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberTitle') . '
                            <div class="">
                                <input name="members[' . $inc . '][entityMember][' . $i . '][memberTitle]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberTitle') . ' "
                                        type="text"  autocomplete="off"
                                        value="' . $item->memberTitle . '"
                                        ' . loanForm::isEnabled('memberTitle') . '
                                        id="memberTitle' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3" id="memberCategory_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCategory') . '
                            <div class="">
                                <select name="members[' . $inc . '][entityMember][' . $i . '][memberCategory]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberCategory') . ' " 
                                        ' . loanForm::isEnabled('memberCategory') . '
                                        id="memberCategory' . $inc . '_' . $i . '">
                                    <option value="">-Select-</option>';
        foreach (borrowerBusinessEntity::getEntityTypes() as $entityMemberCategory) {
            $html .= '<option value="' . $entityMemberCategory . '">' . $entityMemberCategory . '</option>';
        }
        $html .=    '</select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3" id="">
                        <div class="form-group">
                            ' . loanForm::label('memberOwnership') . '
                            <div class="">
                                <div class="input-group">
                                    <input name="members[' . $inc . '][entityMember][' . $i . '][memberOwnership]"
                                    class="form-control input-sm memberOwnership_' . $inc . ' ' . loanForm::isMandatory('memberOwnership') . ' "
                                    type="text"  autocomplete="off"
                                    value="' . $item->memberOwnership . '"
                                    ' . loanForm::isEnabled('memberOwnership') . '
                                    id="memberOwnership' . $inc . '_' . $i . '"
                                    onblur="BusinessEntitySection.calculateOwnerShip(this)">
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            %
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberAnnualSalaryHideShow . ' " id="memberAnnualSalary_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberAnnualSalary') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberAnnualSalary]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberAnnualSalary') . ' "
                                        type="text" autocomplete="off" onblur="currencyConverter(this, this.value)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros($item->memberAnnualSalary) . '"
                                        ' . loanForm::isEnabled('memberAnnualSalary') . '
                                        id="memberAnnualSalary' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3" id="">
                        <div class="form-group">
                            ' . loanForm::label('memberAddress') . '
                            <script>
                                $(document).ready(function() {
                                    $("#memberAddress' . $inc . '_' . $i . '").on("input", function() {
                                        address_lookup.InitLegacy($(this));
                                    });
                                });
                            </script>
                            <div class="">
                                <input name="members[' . $inc . '][entityMember][' . $i . '][memberAddress]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberAddress') . ' "
                                        type="text"   autocomplete="off"
                                        value="' . $item->memberAddress . '"
                                        ' . loanForm::isEnabled('memberAddress') . '
                                        id="memberAddress' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3" id="">
                        <div class="form-group">
                            ' . loanForm::label('memberPhone') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberPhone]"
                                        class="form-control input-sm mask_phone ' . loanForm::isMandatory('memberPhone') . ' "
                                        type="text"   autocomplete="off"
                                        placeholder="(___) ___ - ____ Ext ____"
                                        value="' . Strings::formatPhoneNumber($item->memberPhone) . '"
                                        ' . loanForm::isEnabled('memberPhone') . '
                                        id="memberPhone' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3" id="">
                        <div class="form-group">
                            ' . loanForm::label('memberCell') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCell]"
                                        class="form-control input-sm mask_cellnew ' . loanForm::isMandatory('memberCell') . ' "
                                        type="text"  autocomplete="off"
                                        placeholder="(___) ___ - ____"
                                        value="' . Strings::formatCellNumber($item->memberCell) . '"
                                        ' . loanForm::isEnabled('memberCell') . '
                                        id="memberCell' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberSSNHideShow . ' " id="memberSSN_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberSSN') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberSSN]"
                                        class="form-control input-sm mask_ssn ' . loanForm::isMandatory('memberSSN') . ' "
                                        type="text" autocomplete="off"
                                        placeholder="___ - __ - ____"
                                        value="' . Strings::formatSSNNumber($item->memberSSN) . '"
                                        ' . loanForm::isEnabled('memberSSN') . '
                                        id="memberSSN' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDOBHideShow . ' " id="memberDOB_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDOB') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberDOB]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberDOB') . ' "
                                        type="text" autocomplete="off"
                                        placeholder="MM/DD/YYYY"
                                        data-date-dob-start-date="' . glDate::getMinRequirementDate() . '"
                                        data-date-dob-end-date="' . glDate::getMaxRequirementDate() . '"
                                        value="' . Dates::formatDateWithRE($item->memberDOB, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberDOB') . '
                                        id="memberDOB' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberEmailHideShow . ' " id="memberEmail_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberEmail') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberEmail]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberEmail') . ' "
                                        type="text"  autocomplete="off"
                                        value="' . $item->memberEmail . '"
                                        ' . loanForm::isEnabled('memberEmail') . '
                                        id="memberEmail' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCreditScoreHideShow . ' " id="memberCreditScore_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCreditScore') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCreditScore]"
                                        class="form-control input-sm memberCreditScore ' . loanForm::isMandatory('memberCreditScore') . ' "
                                        type="text"  autocomplete="off"
                                        value="' . $item->memberCreditScore . '"
                                        ' . loanForm::isEnabled('memberCreditScore') . '
                                        id="memberCreditScore' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCreditScoreDateHideShow . ' " id="memberCreditScoreDate_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCreditScoreDate') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCreditScoreDate]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberCreditScoreDate') . ' "
                                        type="text" autocomplete="off"
                                        placeholder="MM/DD/YYYY"
                                        value="' . Dates::formatDateWithRE($item->memberCreditScoreDate, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberCreditScoreDate') . '
                                        id="memberCreditScoreDate' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberRentOrOwnHideShow . ' " id="memberRentOrOwn_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberRentOrOwn') . '
                            <div class="">
                                <select name="members[' . $inc . '][entityMember][' . $i . '][memberRentOrOwn]"
                                        class="form-control input-sm  ' . loanForm::isMandatory('memberRentOrOwn') . ' "
                                        ' . loanForm::isEnabled('memberRentOrOwn') . '
                                        id="memberRentOrOwn' . $inc . '_' . $i . '">
                                    <option value="">-Select-</option>
                                    <option value="Rent" ' . $memberRentSelected . '>Rent</option>
                                    <option value="Own" ' . $memberOwnSelected . '>Own</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberMonthlyRentOrMortgageHideShow . ' " id="memberMonthlyRentOrMortgage_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMonthlyRentOrMortgage') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberMonthlyRentOrMortgage]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberMonthlyRentOrMortgage') . ' "
                                        type="text" onblur="currencyConverter(this, this.value)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberMonthlyRentOrMortgage)) . '"
                                        ' . loanForm::isEnabled('memberMonthlyRentOrMortgage') . '
                                        id="memberMonthlyRentOrMortgage' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDateMovedAddressHideShow . ' " id="memberDateMovedAddress_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDateMovedAddress') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberDateMovedAddress]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberDateMovedAddress') . ' "
                                        type="text"
                                        placeholder="MM/DD/YYYY"
                                        value="' . Dates::formatDateWithRE($item->memberDateMovedAddress, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberDateMovedAddress') . '
                                        id="memberDateMovedAddress' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberRealEstateValueHideShow . ' " id="memberRealEstateValue_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberRealEstateValue') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberRealEstateValue]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberRealEstateValue') . ' "
                                        type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberRealEstateValue)) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberRealEstateValue') . '
                                        id="memberRealEstateValue' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberRetirementAccountBalanceHideShow . ' " id="memberRetirementAccountBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberRetirementAccountBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberRetirementAccountBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberRetirementAccountBalance') . ' "
                                        type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberRetirementAccountBalance)) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberRetirementAccountBalance') . '
                                        id="memberRetirementAccountBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCashSavingsStocksBalanceHideShow . ' " id="memberCashSavingsStocksBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCashSavingsStocksBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCashSavingsStocksBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberCashSavingsStocksBalance') . ' "
                                        type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberCashSavingsStocksBalance)) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberCashSavingsStocksBalance') . '
                                        id="memberCashSavingsStocksBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCreditCardBalanceHideShow . ' " id="memberCreditCardBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCreditCardBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberCreditCardBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberCreditCardBalance') . ' "
                                        type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberCreditCardBalance)) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberCreditCardBalance') . '
                                        id="memberCreditCardBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberMortgageBalanceHideShow . ' " id="memberMortgageBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMortgageBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberMortgageBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberMortgageBalance') . ' "
                                        type="text" onblur="currencyConverter(this, this.value); BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberMortgageBalance)) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberMortgageBalance') . '
                                        id="memberMortgageBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberAutoLoanBalanceHideShow . ' " id="memberAutoLoanBalance_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberAutoLoanBalance') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input  name="members[' . $inc . '][entityMember][' . $i . '][memberAutoLoanBalance]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberAutoLoanBalance') . ' "
                                        type="text" onblur="currencyConverter(this, this.value);BusinessEntitySection.calculateTotalNetWorth(this)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberAutoLoanBalance)) . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberAutoLoanBalance') . '
                                        id="memberAutoLoanBalance' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberTotalNetWorthHideShow . ' " id="memberTotalNetWorth_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberTotalNetWorth') . '
                            <div class="">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input class="form-control input-sm" readonly
                                        type="text" onblur="currencyConverter(this, this.value)"
                                        value="' . Currency::formatDollarAmountWithDecimalZeros(htmlentities($item->memberTotalNetWorth)) . '"
                                        ' . loanForm::isEnabled('memberTotalNetWorth') . '
                                        id="memberTotalNetWorth' . $inc . '_' . $i . '">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberMaritalStatusHideShow . ' " id="memberMaritalStatus_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMaritalStatus') . '
                            <div class="">
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="memberMaritalStatusUnmarried_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class=" ' . loanForm::isMandatory('memberMaritalStatus') . ' "
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberMaritalStatus]"
                                        id="memberMaritalStatusUnmarried_' . $inc . '_' . $i . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberMaritalStatus') . '
                                        value="Unmarried" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                                        ' . ($item->memberMaritalStatus == 'Unmarried' ? 'checked=checked' : '') . '
                                        ><span></span>Unmarried
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberMaritalStatusMarried_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class=" ' . loanForm::isMandatory('memberMaritalStatus') . ' "
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberMaritalStatus]"
                                        id="memberMaritalStatusMarried_' . $inc . '_' . $i . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberMaritalStatus') . '
                                        value="Married" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                                        ' . ($item->memberMaritalStatus == 'Married' ? 'checked=checked' : '') . '
                                        ><span></span>Married
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberMaritalStatusSeparated_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class=" ' . loanForm::isMandatory('memberMaritalStatus') . ' "
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberMaritalStatus]"
                                        id="memberMaritalStatusSeparated_' . $inc . '_' . $i . '"
                                        data-index="' . $inc . '_' . $i . '"
                                        ' . loanForm::isEnabled('memberMaritalStatus') . '
                                        value="Separated" onchange="BusinessEntitySection.showHideMaritalFields(this);"
                                        ' . ($item->memberMaritalStatus == 'Separated' ? 'checked=checked' : '') . '
                                        ><span></span>Separated
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberMarriageDateHideShow . ' " id="memberMarriageDate_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMarriageDate') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberMarriageDate]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberMarriageDate') . ' "
                                        type="text"
                                        placeholder="MM/DD/YYYY"
                                        value="' . Dates::formatDateWithRE($item->memberMarriageDate, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberMarriageDate') . '
                                        id="memberMarriageDate' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDivorceDateHideShow . ' " id="memberDivorceDate_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDivorceDate') . '
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">
                                        <i class="fa fa-calendar text-primary"></i>
                                    </span>
                                </div>
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberDivorceDate]"
                                        class="form-control input-sm dateNewClass ' . loanForm::isMandatory('memberDivorceDate') . ' "
                                        type="text"
                                        placeholder="MM/DD/YYYY"
                                        value="' . Dates::formatDateWithRE($item->memberDivorceDate, 'YMD', 'm/d/Y') . '"
                                        ' . loanForm::isEnabled('memberDivorceDate') . '
                                        id="memberDivorceDate' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberMaidenNameHideShow . ' " id="memberMaidenName_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberMaidenName') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberMaidenName]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberMaidenName') . ' "
                                        type="text"
                                        value="' . $item->memberMaidenName . '"
                                        ' . loanForm::isEnabled('memberMaidenName') . '
                                        id="memberMaidenName' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberSpouseNameHideShow . ' " id="memberSpouseName_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberSpouseName') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberSpouseName]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberSpouseName') . ' "
                                        type="text"
                                        value="' . $item->memberSpouseName . '"
                                        ' . loanForm::isEnabled('memberSpouseName') . '
                                        id="memberSpouseName' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDriversLicenseStateHideShow . ' " id="memberDriversLicenseState_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDriversLicenseState') . '
                            <div class="">
                                <select name="members[' . $inc . '][entityMember][' . $i . '][memberDriversLicenseState]"
                                class="form-control input-sm ' . loanForm::isMandatory('memberDriversLicenseState') . ' "
                                ' . loanForm::isEnabled('memberDriversLicenseState') . '
                                id="memberDriversLicenseState' . $inc . '_' . $i . '">
                                    <option value="">-Select-</option>';
        foreach(Arrays::fetchStates() as $dlState) {
            $html .= '<option value="' . $dlState['stateCode'] . '">' . $dlState['stateName'] . '</option>';
        }
        $html .= '</select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberDriversLicenseHideShow . ' " id="memberDriversLicense_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberDriversLicense') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberDriversLicense]"
                                        class="form-control input-sm ' . loanForm::isMandatory('memberDriversLicense') . ' "
                                        type="text"   autocomplete="off"
                                        value="' . $item->memberDriversLicense . '"
                                        ' . loanForm::isEnabled('memberDriversLicense') . '
                                        id="memberDriversLicense' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberTinHideShow . ' " id="memberTin_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberTin') . '
                            <div class="">
                                <input  name="members[' . $inc . '][entityMember][' . $i . '][memberTin]"
                                        class="form-control input-sm mask_ein ' . loanForm::isMandatory('memberTin') . ' "
                                        type="text"
                                        autocomplete="off"
                                        placeholder="__-_______"
                                        value="' . $item->memberTin . '"
                                        ' . loanForm::isEnabled('memberTin') . '
                                        id="memberTin' . $inc . '_' . $i . '">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberPersonalGuaranteeHideShow . ' " id="memberPersonalGuarantee_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberPersonalGuarantee') . '
                            <div class="">
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="memberPersonalGuaranteeYes_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberPersonalGuarantee') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberPersonalGuarantee]"
                                        id="memberPersonalGuaranteeYes_' . $inc . '_' . $i . '"
                                        ' . $memberPersonalGuaranteeYes . '
                                        ' . loanForm::isEnabled('memberPersonalGuarantee') . '
                                        value="Yes"><span></span>Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberPersonalGuaranteeNo_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberPersonalGuarantee') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberPersonalGuarantee]"
                                        id="memberPersonalGuaranteeNo_' . $inc . '_' . $i . '"
                                        ' . $memberPersonalGuaranteeNo . '
                                        ' . loanForm::isEnabled('memberPersonalGuarantee') . '
                                        value="No"><span></span>No
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberAuthorizedSignerHideShow . ' " id="memberAuthorizedSigner_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberAuthorizedSigner') . '
                            <div class="">
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="memberAuthorizedSignerYes_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberAuthorizedSigner') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberAuthorizedSigner]"
                                        id="memberAuthorizedSignerYes_' . $inc . '_' . $i . '"
                                        ' . $memberAuthorizedSignerYes . '
                                        ' . loanForm::isEnabled('memberAuthorizedSigner') . '
                                        value="Yes"><span></span>Yes
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberAuthorizedSignerNo_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberAuthorizedSigner') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberAuthorizedSigner]"
                                        id="memberAuthorizedSignerNo_' . $inc . '_' . $i . '"
                                        ' . $memberAuthorizedSignerNo . '
                                        ' . loanForm::isEnabled('memberAuthorizedSigner') . '
                                        value="No"><span></span>No
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 ' . $memberCitizenshipHideShow . '  " id="memberCitizenship_' . $inc . '_' . $i . '">
                        <div class="form-group">
                            ' . loanForm::label('memberCitizenship') . '
                            <div class="">
                                <div class="radio-inline">
                                    <label class="radio radio-solid font-weight-bold" for="memberCitizenship_us_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberCitizenship') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberCitizenship]"
                                        id="memberCitizenship_us_' . $inc . '_' . $i . '"
                                        ' . $memberCitizenshipUSCitizen . '
                                        ' . loanForm::isEnabled('memberCitizenship') . '
                                        value="U.S. Citizen"><span></span>U.S.
                                        Citizen
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberCitizenship_pra_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberCitizenship') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberCitizenship]"
                                        id="memberCitizenship_pra_' . $inc . '_' . $i . '"
                                        ' . $memberCitizenshipPermResident . '
                                        ' . loanForm::isEnabled('memberCitizenship') . '
                                        value="Perm Resident Alien"><span></span>Perm
                                        Resident
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberCitizenship_npra_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberCitizenship') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberCitizenship]"
                                        id="memberCitizenship_npra_' . $inc . '_' . $i . '"
                                        ' . $memberCitizenshipNonPermResident . '
                                        ' . loanForm::isEnabled('memberCitizenship') . '
                                        value="Non-Perm Resident Alien"><span></span>Non-Perm
                                        Resident
                                    </label>
                                    <label class="radio radio-solid font-weight-bold" for="memberCitizenship_fn_' . $inc . '_' . $i . '">
                                        <input type="radio"
                                        class="' . loanForm::isMandatory('memberCitizenship') . '"
                                        name="members[' . $inc . '][entityMember][' . $i . '][memberCitizenship]"
                                        id="memberCitizenship_fn_' . $inc . '_' . $i . '"
                                        ' . $memberCitizenshipForeignNational . '
                                        ' . loanForm::isEnabled('memberCitizenship') . '
                                        value="Foreign National"><span></span>Foreign
                                        National
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>';

        $html .= '<div class="col-md-12 p-0">'.
            self::getNewMembersDocsUploadHtml(self::$PCID,$item->CID, $inc, $i, $item->memberType).
            '</div>';


        $html.='<div class="row d-none">
                    <div class="col-md-6 ' . $howManyMemberOfficerHideShow . ' " id="howManyMemberOfficer_' . $inc . '_' . $i . '">
                        <div class="form-group row">
                            <label class="col-md-10 font-weight-bold">
                                How many members/officers are there with 20%+ ownership?
                            </label>
                            <div class="col-md-2">
                                <input
                                        class="form-control input-sm"
                                        type="text"
                                        name=""
                                        value=""
                                        id="formContainer_' . $row . '_' . $i . '"
                                        data-row = "' . $row . '"
                                        data-inc="' . $i . '"
                                        data-moid="' . cypher::myEncryption($item->MOID) . '"
                                        data-cbeid="' . cypher::myEncryption($item->CBEID) . '"
                                        onchange="borrowerProfile.getEntityInfoMembers(this)"
                                        >
                            </div>
                        </div>
                    </div>
                </div>
                <div id="formContainer_' . $row . '_' . $i . '"></div>';
        $html.='</div>
        </div>
    </div>
</div>';
        return $html;
    }


    public static function getEntityDocsUploadHtml(?int $PCID,
                                                   ?int $CID,
                                                   ?int $CBEID): string
    {

        //pr(func_get_args());
        Docs::init($PCID);
        Docs::$borrowerId = $CID;
        Docs::$CBEID = $CBEID;
        ob_start();
        ?>
        <div class="card card-custom mb-3">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">Entity Documents</h3>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php
                    foreach (Docs::$entityDocs as $eachDoc) { ?>
                        <div class="col-md-6">
                            <div class="card card-custom gutter-b">
                                <div class="card-header card-header-tabs-line bg-gray-100  ">
                                    <div class="card-title p-2">
                                        <h3 class="card-label"
                                            data-id="<?php echo $eachDoc->id; ?>"><?php echo $eachDoc->docName; ?></h3>
                                    </div>
                                </div>
                                <div class="card-body p-1">
                                    <?php
                                    $docSno = 1;
                                    $tblBorrowerEntityDocs = tblBorrowerEntityDocs::getAll([
                                        tblBorrowerEntityDocs_db::COLUMN_CBEID         => Docs::$CBEID,
                                        tblBorrowerEntityDocs_db::COLUMN_REQUIREDDOCID => $eachDoc->id,
                                        tblBorrowerEntityDocs_db::COLUMN_STATUS        => 1,
                                    ]);
                                    $tblBorrowerEntityDocs = sizeof($tblBorrowerEntityDocs) ? $tblBorrowerEntityDocs : [new tblBorrowerEntityDocs()];

                                    foreach ($tblBorrowerEntityDocs as $eachUploadedDoc) { ?>
                                        <div class=" border p-4 mb-2 borrowerDoc<?php echo $eachDoc->id; ?>"
                                             id="borrowerDocDiv<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>">
                                           <input type="hidden"
                                                   class="form-control"
                                                   id="docsId_borrower_<?php echo $eachDoc->id; ?>"
                                                   value="<?php echo $eachUploadedDoc->id; ?>"
                                                   name="docs[entity][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][id]"/>
                                            <div class="form-group row">
                                                <label class="col-md-2 font-weight-bold"
                                                       for="docs_borrowerFile_<?php echo $eachDoc->id; ?>">File Upload
                                                    :</label>
                                                <div class="col-md-8">
                                                    <input type="file"
                                                           class="form-control entityDocsChooser"
                                                           id="docs_borrowerFile_<?php echo $eachDoc->id; ?>"
                                                           name="docs[entity][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][file]"/>
                                                </div>
                                                <div class="col-md-2">
                                                    <?php
                                                    if ($eachUploadedDoc->getTblFileStorage_by_id()) { ?>
                                                        <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass ignoreClone"
                                                           href="/backoffice/api_v2/load_file?file_path=<?php echo cypher::myEncryption($eachUploadedDoc->getTblFileStorage_by_id()->givenPath); ?>"
                                                           target="_blank"
                                                           data-toggle="popover"
                                                           data-html="true"
                                                           data-content="Click to view"><i class="fa fa-eye"></i></a>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-md-2 font-weight-bold"
                                                       for="docs_borrowerExpiryDate_<?php echo $eachDoc->id; ?>">Expiry
                                                    Date:</label>
                                                <div class="col-md-10">
                                                    <input type="text"
                                                           class="form-control dateNewClass"
                                                           placeholder="Expiry Date"
                                                           id="docs_borrowerExpiryDate_<?php echo $eachDoc->id; ?>"
                                                           value="<?php echo Dates::formatDateWithRE($eachUploadedDoc->expiryDate, 'YMD', 'm/d/Y') ?>"
                                                           name="docs[entity][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][expiryDate]"/>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-md-2 font-weight-bold"
                                                       for="docs_borrowerDisplayName_<?php echo $eachDoc->id; ?>">Display
                                                    Name:</label>
                                                <div class="col-md-10">
                                                    <input type="text"
                                                           class="form-control "
                                                           placeholder="Display Name"
                                                           id="docs_borrowerDisplayName_<?php echo $eachDoc->id; ?>"
                                                           value="<?php echo $eachUploadedDoc->displayName; ?>"
                                                           name="docs[entity][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][displayName]"/>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12 text-right">
                                            <span class="btn btn-sm btn-success btn-text-primary  btn-icon ml-2 tooltipClass <?php if ($docSno < sizeof($tblBorrowerEntityDocs)) {
                                                echo 'd-none';
                                            }
                                            ?> cloneFormSection addButton<?php echo $eachDoc->id; ?>"
                                                  onclick="SectionForm.cloneFormSection(this)"
                                                  data-clone-section="borrowerDoc<?php echo $eachDoc->id; ?>"
                                                  data-increment-section="incrementClassPS"
                                                  data-max-section=""
                                                  title="Click to add new Row.">
                                                <i class=" icon-md fas fa-plus "></i>
                                            </span>
                                                    <span class="btn btn-sm btn-danger btn-text-primary  btn-icon ml-2 tooltipClass removeFromSection removeButton<?php echo $docSno;
                                                    ?>"
                                                          href="javascript:void(0)"
                                                          onclick="SectionForm.removeFromSection(this)"
                                                          data-clone-section="borrowerDoc<?php echo $eachDoc->id; ?>"
                                                          data-increment-section="incrementClassPS"
                                                          data-page="../backoffice/api_v2/borrower/Docs/delete?type=<?php echo cypher::myEncryption('entity'); ?>"
                                                          data-id="<?php echo cypher::myEncryption($eachUploadedDoc->id); ?>"
                                                          title="Click to Remove Row.">
                                                <i class=" icon-md fas fa-minus-circle "></i>
                                            </span>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                        $docSno++;
                                    } ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    public static function getMembersDocsUploadHtml(
        ?int $PCID,
        ?int $CID,
        ?int $CBEID,
        ?int $parent_id,
        ?string $memberType,
        ?string $memberName,
        ?int $memberId = null  
    ): string
    {
        Docs::init($PCID);
        Docs::$borrowerId = $CID;
        Docs::$CBEID = $CBEID;
        Docs::$memberId = $memberId;
        ob_start();
        $isDisabled = self::$isPageView ? 'disabled' : '';
        ?>
        <div class="card card-custom mb-3">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label"
                        data-member-id="<?php echo $memberId; ?>">
                        <?php echo $memberName ? '('.$memberName.')': '' ?>
                        Documents</h3>
                </div>
                <div class="card-toolbar">
                    <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                          data-card-tool="toggle"
                          data-toggle="tooltip"
                          data-placement="top"
                          title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                     </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php
                   // pr(self::$isPageView);
                    //pr(Docs::$memberDocs);
                    foreach (Docs::$memberDocs as $eachDoc) { ?>
                        <div class="col-md-6 memberDocs <?php echo $eachDoc->memberType.'ClassDoc'; ?>"
                             style="<?php echo $eachDoc->memberType == $memberType ? '' : 'display:none;' ; ?>">
                            <div class="card card-custom gutter-b">
                                <div class="card-header card-header-tabs-line bg-gray-100  ">
                                    <div class="card-title p-2">
                                        <h3 class="card-label"
                                            data-type="<?php echo $eachDoc->docType; ?>"><?php echo $eachDoc->docName; ?></h3>
                                    </div>
                                </div>
                                <div class="card-body bg-gray-100">
                                    <?php
                                    $docSno = 1;
                                    $tblBorrowerEntityMemberDocs = tblBorrowerEntityMemberDocs::getAll([
                                        tblBorrowerEntityMemberDocs_db::COLUMN_MEMBERID      => Docs::$memberId,
                                        tblBorrowerEntityMemberDocs_db::COLUMN_REQUIREDDOCID => $eachDoc->id,
                                        tblBorrowerEntityMemberDocs_db::COLUMN_STATUS        => 1,
                                    ]);
                                    $tblBorrowerEntityMemberDocs = sizeof($tblBorrowerEntityMemberDocs) ? $tblBorrowerEntityMemberDocs : [new tblBorrowerEntityMemberDocs()];

                                    foreach ($tblBorrowerEntityMemberDocs as $eachUploadedDoc) { ?>
                                        <div class=" border p-4 mb-2 borrowerDoc<?php echo $memberId;?>_<?php echo $eachDoc->id; ?>"
                                             id="borrowerDocDiv<?php echo $memberId;?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>">
                                            <input type="hidden"
                                                   id="docsId_borrower_<?php echo $memberId; ?>_<?php echo $parent_id; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>"
                                                   name="docs[member][<?php echo $memberId; ?>][<?php echo $parent_id; ?>][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][id]"
                                                   value="<?php echo $eachUploadedDoc->id; ?>"/>
                                            <br>
                                            <div class="form-group row">
                                                <label class="col-md-2 font-weight-bold"
                                                       for="docs_borrowerFile_<?php echo $eachDoc->id; ?>">File Upload
                                                    :</label>
                                                <div class="col-md-8" style="<?php echo self::$isPageView ? 'display:none;' : ''; ?>">
                                                    <input type="file"
                                                           class="form-control memberDocsChooser"
                                            id="docs_borrowerFile_<?php echo $memberId; ?>_<?php echo $parent_id; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>"
                                            name="docs[member][<?php echo $memberId; ?>][<?php echo $parent_id; ?>][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][file]"
                                                    />
                                                </div>
                                                <div class="col-md-2">
                                                    <?php
                                                    if ($eachUploadedDoc->getTblFileStorage_by_id()) { ?>
                                                        <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass ignoreClone"
                                                           href="/backoffice/api_v2/load_file?file_path=<?php echo cypher::myEncryption($eachUploadedDoc->getTblFileStorage_by_id()->givenPath); ?>"
                                                           target="_blank"
                                                           data-toggle="popover"
                                                           data-html="true"
                                                           data-content="Click to view"><i class="fa fa-eye"></i></a>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-md-2 font-weight-bold"
                                                       for="docs_borrowerExpiryDate_<?php echo $memberId; ?>_<?php echo $parent_id; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>">Expiry
                                                    Date:</label>
                                                <div class="col-md-10">
                                                    <input type="text"
                                                           class="form-control dateNewClass"
                                                           placeholder="Expiry Date"
                                                           id="docs_borrowerExpiryDate_<?php echo $memberId; ?>_<?php echo $parent_id; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>"
                                                           name="docs[member][<?php echo $memberId; ?>][<?php echo $parent_id; ?>][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][expiryDate]"
                                                           value="<?php echo Dates::formatDateWithRE($eachUploadedDoc->expiryDate, 'YMD', 'm/d/Y') ?>"
                                                           <?php echo $isDisabled;?>
                                                           />
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-md-2 font-weight-bold"
                                                       for="docs_borrowerDisplayName_<?php echo $memberId; ?>_<?php echo $parent_id; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>">Display
                                                    Name:</label>
                                                <div class="col-md-10">
                                                    <input type="text"
                                                           class="form-control "
                                                           placeholder="Display Name"
                                                           id="docs_borrowerDisplayName_<?php echo $memberId; ?>_<?php echo $parent_id; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>"
                                                           name="docs[member][<?php echo $memberId; ?>][<?php echo $parent_id; ?>][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][displayName]"
                                                           value="<?php echo $eachUploadedDoc->displayName; ?>"
                                                            <?php echo $isDisabled;?>
                                                    />
                                                </div>
                                            </div>
                                            <div class="row" style="<?php echo self::$isPageView ? 'display:none;' : ''; ?>">
                                                <div class="col-md-12 text-right">
                                                    <div class="btn btn-sm btn-success btn-text-primary btn-icon ml-2 tooltipClass <?php if ($docSno < sizeof($tblBorrowerEntityMemberDocs)) {
                                                        echo 'd-none';
                                                    }
                                                    ?> cloneFormSection addButton<?php echo $eachDoc->id; ?>"
                                                          onclick="SectionForm.cloneFormSection(this)"
                                                          data-clone-section="borrowerDoc<?php echo $memberId;?>_<?php echo $eachDoc->id; ?>"
                                                          data-increment-section="incrementClassPS"
                                                          data-section-name="memberDoc"
                                                          data-max-section=""
                                                          title="Click to add new Row.">
                                                        <i class=" icon-md fas fa-plus "></i>
                                                    </div>
                                                    <div class="btn btn-sm btn-danger btn-text-primary  btn-icon ml-2 tooltipClass removeFromSection removeButton<?php echo $docSno;
                                                    ?>"
                                                          href="javascript:void(0)"
                                                          onclick="SectionForm.removeFromSection(this)"
                                                          data-clone-section="borrowerDoc<?php echo $memberId;?>_<?php echo $eachDoc->id; ?>"
                                                          data-increment-section="incrementClassPS"
                                                          data-page="../backoffice/api_v2/borrower/Docs/delete?type=<?php echo cypher::myEncryption('member'); ?>"
                                                          data-id="<?php echo cypher::myEncryption($eachUploadedDoc->id); ?>"
                                                          title="Click to Remove Row.">
                                                             <i class=" icon-md fas fa-minus-circle "></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                        $docSno++;
                                    } ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
//  backoffice/borrower/entityDocs.php
    }

    public static function getNewMembersDocsUploadHtml(?int $PCID,
                                                    ?int $CID,
                                                    ?int $entityIncrementId,
                                                    ?int $memberIncrementNumber,
                                                    ?string $memberType
    ): string
    {
        Docs::init($PCID);
        Docs::$borrowerId = $CID;
        ob_start();
        ?>
        <div class="card card-custom mb-3">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">Documents</h3>
                </div>
                <div class="card-toolbar">
                    <span class="cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                          data-card-tool="toggle"
                          data-toggle="tooltip"
                          data-placement="top"
                          title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                     </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php
                    foreach (Docs::$memberDocs as $eachDoc) { ?>
                        <div class="col-md-6 memberDocs <?php echo $eachDoc->memberType.'ClassDoc'; ?>"
                             style="<?php echo $eachDoc->memberType == $memberType ? '' : 'display:none;' ; ?>">
                            <div class="card card-custom gutter-b">
                                <div class="card-header card-header-tabs-line bg-gray-100  ">
                                    <div class="card-title p-2">
                                        <h3 class="card-label"
                                            data-id="<?php echo $eachDoc->id; ?>"><?php echo $eachDoc->docName; ?></h3>
                                    </div>
                                </div>
                                <div class="card-body bg-gray-100">
                                    <?php
                                    $docSno = 1;
                                    $tblBorrowerEntityMemberDocs = tblBorrowerEntityMemberDocs::getAll([
                                        tblBorrowerEntityMemberDocs_db::COLUMN_MEMBERID      => Docs::$memberId,
                                        tblBorrowerEntityMemberDocs_db::COLUMN_REQUIREDDOCID => $eachDoc->id,
                                        tblBorrowerEntityMemberDocs_db::COLUMN_STATUS        => 1,
                                    ]);
                                    $tblBorrowerEntityMemberDocs = sizeof($tblBorrowerEntityMemberDocs) ? $tblBorrowerEntityMemberDocs : [new tblBorrowerEntityMemberDocs()];

                                    foreach ($tblBorrowerEntityMemberDocs as $eachUploadedDoc) { ?>
                                        <div class=" border p-4 mb-2 borrowerDoc<?php echo $memberIncrementNumber;?>_<?php echo $eachDoc->id; ?>"
                                             id="borrowerDocDiv<?php echo $memberIncrementNumber;?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>">
                                            <input type="hidden"
                                                   id="docsId_borrower_<?php echo $entityIncrementId; ?>_<?php echo $memberIncrementNumber; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>"
                                                   name="docs[member][<?php echo $entityIncrementId; ?>][<?php echo $memberIncrementNumber; ?>][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][id]"
                                                   value="<?php echo $eachUploadedDoc->id; ?>"/>
                                            <div class="form-group row">
                                                <label class="col-md-2 font-weight-bold"
                                                       for="docs_borrowerFile_<?php echo $eachDoc->id; ?>">File Upload
                                                    :</label>
                                                <div class="col-md-8">
                                                    <input
                        type="file"
                        class="form-control"
                        id="docs_borrowerFile_<?php echo $entityIncrementId; ?>_<?php echo $memberIncrementNumber; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>"
                        name="docs[member][<?php echo $entityIncrementId; ?>][<?php echo $memberIncrementNumber; ?>][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][file]"
                                                    />
                                                </div>
                                                <div class="col-md-2">
                                                    <?php
                                                    if ($eachUploadedDoc->getTblFileStorage_by_id()) { ?>
                                                        <a class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 tooltipClass ignoreClone"
                                                           href="/backoffice/api_v2/load_file?file_path=<?php echo cypher::myEncryption($eachUploadedDoc->getTblFileStorage_by_id()->givenPath); ?>"
                                                           target="_blank"
                                                           data-toggle="popover"
                                                           data-html="true"
                                                           data-content="Click to view"><i class="fa fa-eye"></i></a>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-md-2 font-weight-bold"
                                                       for="docs_borrowerExpiryDate_<?php echo $entityIncrementId; ?>_<?php echo $memberIncrementNumber; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>">Expiry
                                                    Date:</label>
                                                <div class="col-md-10">
                                                    <input type="text"
                                                           class="form-control dateNewClass"
                                                           placeholder="Expiry Date"
                                                           id="docs_borrowerExpiryDate_<?php echo $entityIncrementId; ?>_<?php echo $memberIncrementNumber; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>"
                                                           name="docs[member][<?php echo $entityIncrementId; ?>][<?php echo $memberIncrementNumber; ?>][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][expiryDate]"
                                                           value="<?php echo Dates::formatDateWithRE($eachUploadedDoc->expiryDate, 'YMD', 'm/d/Y') ?>"
                                                           />
                                                </div>
                                            </div>
                                            <div class="form-group row">
                                                <label class="col-md-2 font-weight-bold"
                                                       for="docs_borrowerDisplayName_<?php echo $entityIncrementId; ?>_<?php echo $memberIncrementNumber; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>">Display
                                                    Name:</label>
                                                <div class="col-md-10">
                                                    <input type="text"
                                                           class="form-control "
                                                           placeholder="Display Name"
                                                           id="docs_borrowerDisplayName_<?php echo $entityIncrementId; ?>_<?php echo $memberIncrementNumber; ?>_<?php echo $eachDoc->id; ?>_<?php echo $docSno; ?>"
                                                           name="docs[member][<?php echo $entityIncrementId; ?>][<?php echo $memberIncrementNumber; ?>][<?php echo $eachDoc->id ?>][<?php echo $docSno; ?>][displayName]"
                                                           value="<?php echo $eachUploadedDoc->displayName; ?>"
                                                           />
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12 text-right">
                                                    <div class="btn btn-sm btn-success btn-text-primary btn-icon ml-2 tooltipClass <?php if ($docSno < sizeof($tblBorrowerEntityMemberDocs)) {
                                                        echo 'd-none';
                                                    }
                                                    ?> cloneFormSection addButton<?php echo $eachDoc->id; ?>"
                                                          onclick="SectionForm.cloneFormSection(this)"
                                                          data-clone-section="borrowerDoc<?php echo $memberIncrementNumber;?>_<?php echo $eachDoc->id; ?>"
                                                          data-increment-section="incrementClassPS"
                                                          data-section-name="memberDoc"
                                                          data-max-section=""
                                                          title="Click to add new Row.">
                                                        <i class=" icon-md fas fa-plus "></i>
                                                    </div>
                                                    <div class="btn btn-sm btn-danger btn-text-primary  btn-icon ml-2 tooltipClass removeFromSection removeButton<?php echo $docSno;
                                                        ?>"
                                                              href="javascript:void(0)"
                                                              onclick="SectionForm.removeFromSection(this)"
                                                              data-clone-section="borrowerDoc<?php echo $memberIncrementNumber;?>_<?php echo $eachDoc->id; ?>"
                                                              data-increment-section="incrementClassPS"
                                                              data-section-name="memberDoc"
                                                              data-page="../backoffice/api_v2/borrower/Docs/delete?type=<?php echo cypher::myEncryption('member'); ?>"
                                                              data-id="<?php echo cypher::myEncryption($eachUploadedDoc->id); ?>"
                                                              title="Click to Remove Row.">
                                                        <i class=" icon-md fas fa-minus-circle "></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                        $docSno++;
                                    } ?>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
//  backoffice/borrower/entityDocs.php
    }


    public static function getBorrowerDocsTree(
        int $CID,
        ?int $CBEID = null,
        ?int $parent_id = null
    ): array {
        // Generate Borrower Docs
        $borrowerData = self::generateBorrowerDocs($CID);

        // Generate Entity Docs and Members
        $entityData = [];
        $membersData = [];

        // Prepare parameters for entity filtering
        $params = [
            tblPCClientEntityInfo_db::COLUMN_CID          => $CID,
            tblPCClientEntityInfo_db::COLUMN_ACTIVESTATUS => 1,
            tblPCClientEntityInfo_db::COLUMN_BORROWERTYPE => 'Entity',
        ];
        if ($CBEID) {
            $params[tblPCClientEntityInfo_db::COLUMN_CBEID] = $CBEID;
        }

        // Fetch entities using filtering parameters
        $entities = tblPCClientEntityInfo::getAll($params);

        foreach ($entities as $eachEntity) {
            // Generate entity documents
            $entityDocs = self::generateEntityDocs($eachEntity);
            $entityData[] = $entityDocs;

            // Generate member tree
            $members = [
                'text'     => '<span class="h3">' . $eachEntity->entityName . '</span>',
                'icon'     => 'fas fa-hotel',
                'id'       => 'entity_id_' . $eachEntity->CBEID,
                'children' => borrowerProfile::getBorrowerMembersTree($eachEntity->CID, $eachEntity->CBEID),
                'state'    => ['opened' => true],
            ];
            $membersData[] = $members;
        }

        return [
            'borrowerDocs' => $borrowerData,
            'entityDocs'   => $entityData,
            'membersDocs'  => $membersData,
        ];
    }


    private static function generateBorrowerDocs(int $CID): array
    {
        $borrowerData = [];
        foreach (Docs::$borrowerDocs as $item) {
            $docsSub = [];
            $uploadedDocs = tblBorrowerProfileUploadedDocs::getAll([
                tblBorrowerProfileUploadedDocs_db::COLUMN_BORROWERID    => $CID,
                tblBorrowerProfileUploadedDocs_db::COLUMN_REQUIREDDOCID => $item->id,
                tblBorrowerProfileUploadedDocs_db::COLUMN_STATUS        => 1,
            ]);


            foreach ($uploadedDocs as $subItem) {
                $attach = '';
                if (Docs::$LMRId) {
                                    $docName = htmlentities($item->docName, ENT_QUOTES, 'UTF-8');
                                    $docDisplayName = $subItem->displayName;
                                    $LMRId = cypher::myEncryption(Docs::$LMRId);
                                    $borrowerDocID = cypher::myEncryption($subItem->id);
                                    $borrowerDocType = cypher::myEncryption('profile');

                    $attach         = sprintf(
                                        '<a class="btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon tooltipClass ml-4"
                            title="Attach To File"
                            data-toggle="modal"
                            data-target="#exampleModal1"
                            data-wsize="modal-lg"
                            href="javascript:void(0);"
                            data-href="%sattachBorrowerDocToFile.php"
                            data-id="LMRId=%s&borrowerDocID=%s&borrowerDocType=%s"
                            data-name="File: %s > %s">
                            <i class="fa fa-paperclip"></i>
                        </a>',
                        CONST_URL_POPS, // URL base
                        $LMRId,         // Encrypted LMRId
                        $borrowerDocID, // Encrypted borrowerDocID
                        $borrowerDocType, // Encrypted borrowerDocType
                        $docName,       // Escaped required doc name
                        $docDisplayName // Display name
                    );
                }

                $docsSub[] = [
                    'text' => '<span class="tooltipClass" title="click to view">'
                        . $subItem->displayName
                        . '</span>'
                        . '<a class="ml-4 btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon tooltipClas openAttachLink" data-stop-attach="true" title="Click To View"
                            href="/backoffice/api_v2/load_file?file_path=' . cypher::myEncryption($subItem->getTblFileStorage_by_id()->givenPath) . '" target="_blank">'
                        . '<i class="fa fa-eye icon-sm ml-2 text-dark"></i>'
                        . '</a>'
                        . $attach,
                    'icon'   => 'flaticon2-document',
                    'id'     => 'profile_' . $subItem->id,
                ];
            }

            $borrowerData[] = [
                'text'     => '<span class="h4">' . $item->docName . '</span>',
                'state'    => ['opened' => true],
                'children' => $docsSub,
            ];
        }
        return $borrowerData;
    }

    private static function generateEntityDocs($entity): array
    {
        $entityDocs = [
            'text'     => '<span class="h3">' . $entity->entityName . '</span>',
            'icon'     => 'fas fa-hotel',
            'id'       => 'entity_id_' . $entity->CBEID,
            'children' => [],
            'state'    => ['opened' => true],
        ];

        foreach (Docs::$entityDocs as $item) {
            $docChildren = [];
            foreach ($entity->getTblBorrowerEntityDocs_by_CBEID() as $doc) {
                if($doc->status != 1) continue;
                if ($item->id == $doc->getTblBorrowerProfileRequiredDocs_by_id()->id) {

                    $attach = '';
                    if (Docs::$LMRId) {
                        $docName = htmlentities($item->docName, ENT_QUOTES, 'UTF-8');
                        $docDisplayName = $doc->displayName;
                        $LMRId = cypher::myEncryption(Docs::$LMRId);
                        $borrowerDocID = cypher::myEncryption($doc->id);
                        $borrowerDocType = cypher::myEncryption('entity');

                        $attach         = sprintf(
                            '<a class="btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon tooltipClass ml-4"
                            title="Attach To File"
                            data-toggle="modal"
                            data-target="#exampleModal1"
                            data-wsize="modal-lg"
                            href="javascript:void(0);"
                            data-href="%sattachBorrowerDocToFile.php"
                            data-id="LMRId=%s&borrowerDocID=%s&borrowerDocType=%s"
                            data-name="File: %s > %s">
                            <i class="fa fa-paperclip"></i>
                        </a>',
                            CONST_URL_POPS, // URL base
                            $LMRId,         // Encrypted LMRId
                            $borrowerDocID, // Encrypted borrowerDocID
                            $borrowerDocType, // Encrypted borrowerDocType
                            $docName,       // Escaped required doc name
                            $docDisplayName // Display name
                        );
                    }

                    $docChildren[] = [
                        'text' => '<span class="tooltipClass" title="click to view">'
                            . $doc->displayName
                            . '</span>'
                            . '<a class="ml-4 btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon tooltipClas openAttachLink" data-stop-attach="true" title="Click To View"
                            href="/backoffice/api_v2/load_file?file_path=' . cypher::myEncryption($doc->getTblFileStorage_by_id()->givenPath) . '" target="_blank">'
                            . '<i class="fa fa-eye icon-sm ml-2 text-dark"></i>'
                            . '</a>'
                            . $attach,
                        'icon'   => 'flaticon2-document',
                        'id'     => 'fileStorage_id_' . $doc->getTblFileStorage_by_id()->id,
                    ];
                }
            }
            $entityDocs['children'][] = [
                'text'     => '<span class="h5">' . $item->docName . '</span>',
                'id'       => 'entity_doc_id_' . $entity->CBEID . '_' . $item->id,
                'children' => $docChildren,
                'state'    => ['opened' => true],
            ];
        }
        return $entityDocs;
    }

    public static  ?array $memberData = [];

    public static function getBorrowerMembersTree(int $CID, int $CBEID, ?int $parent_id = null): ?array
    {
        $parentMembers = borrowerProfileEntityInfo::getRootMembers($CID, $CBEID, $parent_id);
        self::$memberData = []; // Initialize or reset member data
        foreach ($parentMembers as $member) {
            self::$memberData[] = self::getMemberTree($CID, $CBEID, $member);
        }
        return self::$memberData;
    }


    public static function getMemberTree($CID, $CBEID, tblMembersOfficers $member): array
    {
        $memberInfo = [
            'text' => '<span class="tooltipClass h5" title="' . ($member->memberType == 'Individual' ? 'Individual' : 'Entity') . '">' . ucfirst($member->memberName) . '</span><span class="h7 tooltipClass" title="Ownership"> ('.$member->memberOwnership.'%)</span>',
            'icon' => $member->memberType == 'Individual' ? 'flaticon2-user' : 'fas fa-hotel',
            'children' => [],
            'state' => ['opened' => true],
            'a_attr' => [
                'href' => "",
                'target' => "_blank",
            ],
        ];
        $docTypes = $member->memberType == 'Individual' ? Docs::$memberIndividualDocs : Docs::$memberEntityDocs;
        foreach ($docTypes as $item) {
            $docTypeJson = [
                'text' => '<span class="tooltipClass h6" title="Document Folder">' . ucfirst($item->docName) . '</span>',
                'id' => $CID.'_'.$CBEID.'_'.$member->memberType.'_doc_id_' . $member->MOID . '_' . $item->id,
                'children' => [],
                'state' => ['opened' => true],
                'a_attr' => [
                    'href' => "",
                    'target' => "_blank",
                ],
            ];
            $tblBorrowerEntityMemberDocs = tblBorrowerEntityMemberDocs::GetAll([
               'memberId' => $member->MOID,
            ]);
            foreach ($tblBorrowerEntityMemberDocs as $eachIndividualDoc) {
                if($eachIndividualDoc->status != 1) continue;
                if ($item->id == $eachIndividualDoc->requiredDocId) {
                    $attach = '';
                    if (Docs::$LMRId) {
                        $docName = htmlentities($item->docName, ENT_QUOTES, 'UTF-8');
                        $docDisplayName = $eachIndividualDoc->displayName;
                        $LMRId = cypher::myEncryption(Docs::$LMRId);
                        $borrowerDocID = cypher::myEncryption($eachIndividualDoc->id);
                        $borrowerDocType = cypher::myEncryption('member');

                        $attach         = sprintf(
                            '<a class="btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon tooltipClass ml-4"
                            title="Attach To File"
                            data-toggle="modal"
                            data-target="#exampleModal1"
                            data-wsize="modal-lg"
                            href="javascript:void(0);"
                            data-href="%sattachBorrowerDocToFile.php"
                            data-id="LMRId=%s&borrowerDocID=%s&borrowerDocType=%s"
                            data-name="File: %s > %s">
                            <i class="fa fa-paperclip"></i>
                        </a>',
                            CONST_URL_POPS, // URL base
                            $LMRId,         // Encrypted LMRId
                            $borrowerDocID, // Encrypted borrowerDocID
                            $borrowerDocType, // Encrypted borrowerDocType
                            $docName,       // Escaped required doc name
                            $docDisplayName // Display name
                        );
                    }

                    $docTypeJson['children'][] = [
                        'text' => '<span class="tooltipClass" title="click to view">'
                            . $eachIndividualDoc->displayName
                            . '</span>'
                            . '<a class="ml-4 btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon tooltipClas openAttachLink" data-stop-attach="true" title="Click To View"
                            href="/backoffice/api_v2/load_file?file_path=' . cypher::myEncryption($eachIndividualDoc->getTblFileStorage_by_id()->givenPath) . '" target="_blank">'
                            . '<i class="fa fa-eye icon-sm ml-2 text-dark"></i>'
                            . '</a>'
                            . $attach,
                        'icon'   => 'flaticon2-document',
                        'id' => 'fileStorage_id_' . $eachIndividualDoc->getTblFileStorage_by_id()->id,
                    ];

                }
            }
            $memberInfo['children'][] = $docTypeJson;
        }
        // Recursively process child members
        $childMembers = borrowerProfileEntityInfo::getRootMembers($CID, $CBEID, $member->MOID);
        foreach ($childMembers as $childMember) {
            $memberInfo['children'][] = self::getMemberTree($CID, $CBEID, $childMember);
        }
        return $memberInfo;
    }

    public static function getScheduleOfRealEstate($params): string
    {
        $html = '';
        $loopId = 1;
        $activeTab = cypher::myDecryption(Request::GetClean('activeTab')) ?? '';
        switch ($activeTab) {
            case 'QAPP':
                $activeTab = 'QA';
                break;
            case 'LI':
                $activeTab = 'FA';
                break;
            default:
                $activeTab = 'BO';
                break;
        }
        loanForm::init(PageVariables::$PCID, $activeTab);
        loanForm::$fileTab = $activeTab;
        loanForm::pushSectionID('SORE');
        foreach ($params as $soreId => $sore) {
            $sore = json_decode(json_encode($sore));
            $salesDisplay = 'display:none';
            $mortgageDisplay = '';
            $incomeValueDisplay = '';
            if (in_array($sore->scheduleStatus, ['', 'Sold'])) {
                $salesDisplay = '';
                $mortgageDisplay = 'display:none';
                $incomeValueDisplay = 'display:none';
            }

            $isPaidAtOrBeforeCloseChecked = $sore->paidAtOrBeforeClose == '1' ? 'checked="checked"' : '';
            $isPaidAtOrBeforeCloseAnotherChecked = $sore->paidAtOrBeforeCloseAnother == '1' ? 'checked="checked"' : '';

            $html .= "<div id='scheduleRealEstate_" . $loopId . "' class='card card-custom mb-2 scheduleRealEstate'>
                        <div class='card-header bg-primary-o-40'>
                            <div class='card-title'>
                                <h3 class='card-label' id='scheduleHeader_" . $loopId . "'>Schedule of Real Estate Info :</h3>
                            </div>
                            <div class='card-toolbar'>
                                <span class='cursor-pointer tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass'
                                      data-card-tool='toggle'
                                      data-toggle='tooltip'
                                      data-placement='top'
                                      title='Toggle Card'>
                                    <i class='ki ki-arrow-down icon-nm'></i>
                                 </span>
                            </div>
                        </div>
                        <div class='card-body' id='body_" . $loopId . "'>
                            <div class='row'>
                                
                                <div class='col-md-6 " . loanForm::showField('schedulePropAddr') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('schedulePropAddr', 'col-md-5 schedulePropAddr_' . $loopId) . "
                                        <div class='col-md-7'>                                        
                                            <input type='text' 
                                            class='form-control " . loanForm::isMandatory('schedulePropAddr') . " ' 
                                            id='schedulePropAddr_" . $loopId . "' 
                                            name='schedulePropAddr[]' 
                                            value='" . $sore->schedulePropAddr . "'
                                            " . loanForm::isEnabled('schedulePropAddr') . "
                                            >
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('schedulePropUnit') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('schedulePropUnit', 'col-md-5 schedulePropUnit_' . $loopId) . "
                                        <div class='col-md-7'>
                                            <input type='text'
                                            class='form-control " . loanForm::isMandatory('schedulePropUnit') . " '
                                            id='schedulePropUnit_" . $loopId . "'
                                            name='schedulePropUnit[]'
                                            value='" . $sore->schedulePropUnit . "'
                                            " . loanForm::isEnabled('schedulePropUnit') . "
                                            >
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('schedulePropCity') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('schedulePropCity', 'col-md-5 schedulePropCity_' . $loopId) . "
                                        <div class='col-md-7'>
                                            <input type='text' 
                                            class='form-control " . loanForm::isMandatory('schedulePropCity') . " ' 
                                            id='schedulePropCity_" . $loopId . "' 
                                            name='schedulePropCity[]' 
                                            value='" . $sore->schedulePropCity . "'
                                            " . loanForm::isEnabled('schedulePropCity') . "
                                            >
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('schedulePropState') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('schedulePropState', 'col-md-5 schedulePropState_' . $loopId) . "
                                        <div class='col-md-7'>                                            
                                            <select class='form-control " . loanForm::isMandatory('schedulePropState') . " '
                                                id='schedulePropState_" . $loopId . "' 
                                                name='schedulePropState[]'
                                                " . loanForm::isEnabled('schedulePropState') . " 
                                                >
                                                    <option value=''>Select</option>";
                                                    foreach(Arrays::fetchStates() as $state) {
                                                        $html .= "<option value='" . $state['stateCode'] . "' " . ($sore->schedulePropState == $state['stateCode'] ? 'selected' : '') . ">" . $state['stateName'] . "</option>";
                                                    }
                                            $html .= "</select>    
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('schedulePropZip') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('schedulePropZip', 'col-md-5 schedulePropZip_' . $loopId) . "
                                        <div class='col-md-7'>
                                            <input type='text' 
                                            class='form-control " . loanForm::isMandatory('schedulePropZip') . " ' 
                                            id='schedulePropZip_" . $loopId . "' 
                                            name='schedulePropZip[]' 
                                            value='" . $sore->schedulePropZip . "'
                                            " . loanForm::isEnabled('schedulePropZip') . "
                                            >
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('schedulePropCountry') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('schedulePropCountry', 'col-md-5 schedulePropCountry_' . $loopId) . "
                                        <div class='col-md-7'>
                                            <select class='form-control " . loanForm::isMandatory('schedulePropCountry') . " '
                                                id='schedulePropCountry_" . $loopId . "' 
                                                name='schedulePropCountry[]'
                                                " . loanForm::isEnabled('schedulePropCountry') . " 
                                                >
                                                <option value=''>Select</option>";
                                                    foreach (glCountryArray::$glCountryArray as $schCountryName => $schCountryCode) {
                                                        $html .= "<option value='" . $schCountryCode . "' " . ($sore->schedulePropCountry == $schCountryCode ? 'selected' : '') . ">" . $schCountryName . "</option>";
                                                    }
                                    $html .= "</select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('scheduleStatus') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('scheduleStatus', 'col-md-5 scheduleStatus_' . $loopId) . "
                                        <div class='col-md-7'>
                                            <select class='form-control chzn-select " . loanForm::isMandatory('scheduleStatus') . " '
                                                id='scheduleStatus_" . $loopId . "' 
                                                name='scheduleStatus[]'
                                                onchange='enableSaleFields(this.id, this.value)'
                                                " . loanForm::isEnabled('scheduleStatus') . " 
                                                >
                                                <option value=''>Select</option>";
                                                foreach (glScheduleStatus::$glScheduleStatus as $scheduleStatus) {
                                                    $html .= "<option value='" . $scheduleStatus . "' " . ($sore->scheduleStatus == $scheduleStatus ? 'selected' : '') . ">" . $scheduleStatus . "</option>";
                                                }
                                                $html .= "</select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('ownership') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('ownership', 'col-md-5 ownership_' . $loopId) . "
                                        <div class='col-md-7'>
                                            <input type='text' 
                                            class='form-control " . loanForm::isMandatory('ownership') . " ' 
                                            id='ownership_" . $loopId . "' 
                                            name='ownership[]' 
                                            value='" . $sore->ownership . "'
                                            " . loanForm::isEnabled('ownership') . "
                                            >
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('propType') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('propType', 'col-md-5 propType_' . $loopId) . "
                                        <div class='col-md-7'>
                                            <select name='propType[]' 
                                                id='propType_" . $loopId . "' 
                                                class='form-control " . loanForm::isMandatory('propType') . "'
                                                " . loanForm::isEnabled('propType') . " >
                                                <option value=''>Select</option>";
                                                $schedulePropertyTypeKeyArray = [];
                                                if (count(GpropertyTypeNumbArray::$GpropertyTypeNumbArray) > 0) {
                                                    $schedulePropertyTypeKeyArray = array_keys(GpropertyTypeNumbArray::$GpropertyTypeNumbArray);
                                                }
                                                foreach ($schedulePropertyTypeKeyArray as $schedulePropertyTypeKey) {
                                                    if($schedulePropertyTypeKey == '1000') {
                                                        $html .= "<option disabled style='color: white;background-color: rgb(0, 130, 187)'>---Residential---</option>";
                                                    } elseif($schedulePropertyTypeKey == '1001') {
                                                        $html .= "<option disabled style='color: white;background-color: rgb(0, 130, 187)'>---Commercial---</option>";
                                                    } else {
                                                        $html .= "<option value='" . $schedulePropertyTypeKey . "' " . ($sore->propType == $schedulePropertyTypeKey ? 'selected' : '') . ">" . GpropertyTypeNumbArray::$GpropertyTypeNumbArray[$schedulePropertyTypeKey] . "</option>";
                                                    }
                                                }
                                                $html .= "</select>
                                            
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('scheduleInvestType') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('scheduleInvestType', 'col-md-5 scheduleInvestType_' . $loopId) . "
                                        <div class='col-md-7'>
                                            <select class='form-control " . loanForm::isMandatory('scheduleInvestType') . " '
                                                id='scheduleInvestType_" . $loopId . "' 
                                                name='scheduleInvestType[]'
                                                " . loanForm::isEnabled('scheduleInvestType') . " 
                                                >
                                                <option value=''>Select</option>";
                                                foreach (glInvestType::$glInvestType as $schInvestTypeName => $schInvestTypeCode) {
                                                    $html .= "<option value='" . $schInvestTypeCode . "' " . ($sore->scheduleInvestType == $schInvestTypeCode ? 'selected' : '') . ">" . $schInvestTypeName . "</option>";
                                                }
                                                $html .= "</select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-6 " . loanForm::showField('presentMarketValue') . " '>
                                    <div class='form-group row'>
                                    " . loanForm::label('presentMarketValue', 'col-md-5 presentMarketValue_' . $loopId) . "
                                        <div class='col-md-7'>
                                            <input type='text' 
                                            class='form-control " . loanForm::isMandatory('presentMarketValue') . " ' 
                                            id='presentMarketValue_" . $loopId . "' 
                                            name='presentMarketValue[]' 
                                            value='" . Currency::formatDollarAmountWithDecimal($sore->presentMarketValue) . "'
                                            " . loanForm::isEnabled('presentMarketValue') . "
                                            >
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-12 incomeValuesInfo_" . $loopId . "' id='incomeValuesInfo_" . $loopId . "' style='" . $incomeValueDisplay . "'>
                                    <div class='row'>
                                        <div class='col-md-6 " . loanForm::showField('amountOfMortgages') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('amountOfMortgages', 'col-md-5 amountOfMortgages_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('amountOfMortgages') . " ' 
                                                    id='amountOfMortgages_" . $loopId . "' 
                                                    name='amountOfMortgages[]' 
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->amountOfMortgages) . "'
                                                    onblur='currencyConverter(this, this.value)'
                                                    " . loanForm::isEnabled('amountOfMortgages') . "
                                                    >
                                                </div>                                     
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('grossRentalIncome') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('grossRentalIncome', 'col-md-5 grossRentalIncome_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('grossRentalIncome') . " ' 
                                                    id='grossRentalIncome_" . $loopId . "' 
                                                    name='grossRentalIncome[]' 
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->grossRentalIncome) . "'
                                                    onblur='currencyConverter(this, this.value)'
                                                    " . loanForm::isEnabled('grossRentalIncome') . "
                                                    >
                                                </div>                                     
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('mortgagePayments') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('mortgagePayments', 'col-md-5 mortgagePayments_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('mortgagePayments') . " ' 
                                                    id='mortgagePayments_" . $loopId . "' 
                                                    name='mortgagePayments[]' 
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->mortgagePayments) . "'
                                                    onblur='currencyConverter(this, this.value)'
                                                    " . loanForm::isEnabled('mortgagePayments') . "
                                                    >
                                                </div>                                     
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('insMaintTaxMisc') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('insMaintTaxMisc', 'col-md-5 insMaintTaxMisc_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('insMaintTaxMisc') . " ' 
                                                    id='insMaintTaxMisc_" . $loopId . "' 
                                                    name='insMaintTaxMisc[]' 
                                                    value='" . $sore->insMaintTaxMisc . "'
                                                    onblur='currencyConverter(this, this.value)'
                                                    " . loanForm::isEnabled('insMaintTaxMisc') . "
                                                    >
                                                </div>                                     
                                            </div>
                                        </div>                                         
                                    </div>
                                </div>
                                
                                <div class='col-md-12'>
                                    <div class='row'>
                                        <div class='col-md-6 " . loanForm::showField('netRentalIncome') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('netRentalIncome', 'col-md-5 netRentalIncome_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('netRentalIncome') . " ' 
                                                    id='netRentalIncome_" . $loopId . "' 
                                                    name='netRentalIncome[]' 
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->netRentalIncome) . "'
                                                    onblur='currencyConverter(this, this.value)'
                                                    " . loanForm::isEnabled('netRentalIncome') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('titledUnder') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('titledUnder', 'col-md-5 titledUnder_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('titledUnder') . " ' 
                                                    id='titledUnder_" . $loopId . "' 
                                                    name='titledUnder[]' 
                                                    value='" . $sore->titledUnder . "'                                                    
                                                    " . loanForm::isEnabled('titledUnder') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('datePurchased') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('datePurchased', 'col-md-5 datePurchased_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control dateNewClass " . loanForm::isMandatory('datePurchased') . " ' 
                                                    id='datePurchased_" . $loopId . "' 
                                                    name='datePurchased[]' 
                                                    value='" . Dates::formatDateWithRE($sore->datePurchased, 'YMD', 'm/d/Y') . "'                                                    
                                                    " . loanForm::isEnabled('datePurchased') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('purchasePrice') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('purchasePrice', 'col-md-5 purchasePrice_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('purchasePrice') . " ' 
                                                    id='purchasePrice_" . $loopId . "' 
                                                    name='purchasePrice[]' 
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->purchasePrice) . "'
                                                    " . loanForm::isEnabled('purchasePrice') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('valueofImprovementsMade') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('valueofImprovementsMade', 'col-md-5 valueofImprovementsMade_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('valueofImprovementsMade') . " ' 
                                                    id='valueofImprovementsMade_" . $loopId . "' 
                                                    name='valueofImprovementsMade[]' 
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->valueofImprovementsMade) . "'
                                                    " . loanForm::isEnabled('valueofImprovementsMade') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('intendedOccupancy') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('intendedOccupancy', 'col-md-5 intendedOccupancy_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <select name='intendedOccupancy[]' id='intendedOccupancy_" . $loopId . "' class='form-control " . loanForm::isMandatory('intendedOccupancy') . "' " . loanForm::isEnabled('intendedOccupancy') . " >
                                                        <option value=''>Select</option>";
                                                        foreach (gblIntendedOccupancy::$gblIntendedOccupancy as $intendedOccupancy) {
                                                            $html .= "<option value='" . $intendedOccupancy . "' " . ($sore->intendedOccupancy == $intendedOccupancy ? 'selected' : '') . ">" . $intendedOccupancy . "</option>";
                                                        }
                                        $html .= "</select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>            
                                </div>
                                
                                <div class='col-md-12 salesDispOpt_" . $loopId . "' id='salesDispOpt_" . $loopId . "' style='" . $salesDisplay . "'>
                                    <div class='row'>
                                        <div class='col-md-6 " . loanForm::showField('salesDate') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('salesDate', 'col-md-5 salesDate_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('salesDate') . " ' 
                                                    id='salesDate_" . $loopId . "' 
                                                    name='salesDate[]' 
                                                    value='" . Dates::formatDateWithRE($sore->salesDate, 'YMD', 'm/d/Y') . "'                                                    
                                                    " . loanForm::isEnabled('salesDate') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('salesPrice') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('salesPrice', 'col-md-5 salesPrice_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text' 
                                                    class='form-control " . loanForm::isMandatory('salesPrice') . " ' 
                                                    id='salesPrice_" . $loopId . "' 
                                                    name='salesPrice[]' 
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->salesPrice) . "'                                                    
                                                    " . loanForm::isEnabled('salesPrice') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='col-md-12 mortgageInfo_" . $loopId . "' id='mortgageInfo_" . $loopId . "' style='" . $mortgageDisplay . "'>
                                    <div class='row'>
                                        <div class='col-md-6 " . loanForm::showField('anyMortgagesLiens') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('anyMortgagesLiens', 'col-md-5 anyMortgagesLiens_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <div class='radio-inline'>
                                                        <label class='radio radio-solid font-weight-bold' for='anyMortgagesLiensYes_" . $loopId . "'>
                                                            <input type='radio'
                                                            class='" . loanForm::isMandatory('anyMortgagesLiens') . "' id='anyMortgagesLiensYes_" . $loopId . "'
                                                            name='anyMortgagesLiens_" . $loopId . "[]' value='Yes' " . ($sore->anyMortgagesLiens == 'Yes' ? 'checked' : '') . "
                                                            " . loanForm::isEnabled('anyMortgagesLiens') . "  >
                                                            <span></span>Yes
                                                        </label>
                                                        <label class='radio radio-solid font-weight-bold' for='anyMortgagesLiensNo_" . $loopId . "'>
                                                            <input type='radio'
                                                            class='" . loanForm::isMandatory('anyMortgagesLiens') . "' id='anyMortgagesLiensNo_" . $loopId . "'
                                                            name='anyMortgagesLiens_" . $loopId . "[]' value='No' " . ($sore->anyMortgagesLiens == 'No' ? 'checked' : '') . "
                                                            " . loanForm::isEnabled('anyMortgagesLiens') . "  >
                                                            <span></span>No
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-12'></div>
                                        <div class='col-md-6 " . loanForm::showField('creditorName') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('creditorName', 'col-md-5 creditorName_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('creditorName') . " '
                                                    id='creditorName_" . $loopId . "'
                                                    name='creditorName[]'
                                                    value='" . $sore->creditorName . "'
                                                    " . loanForm::isEnabled('creditorName') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('accountNumber') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('accountNumber', 'col-md-5 accountNumber_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('accountNumber') . " '
                                                    id='accountNumber_" . $loopId . "'
                                                    name='accountNumber[]'
                                                    value='" . $sore->accountNumber . "'
                                                    " . loanForm::isEnabled('accountNumber') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('loanStatus') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('loanStatus', 'col-md-5 loanStatus_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <select class='form-control " . loanForm::isMandatory('loanStatus') . " '
                                                    id='loanStatus_" . $loopId . "'
                                                    name='loanStatus[]'
                                                    " . loanForm::isEnabled('loanStatus') . ">
                                                    <option value=''>Select</option>";
                                                    foreach (gblLoanStatus::$gblLoanStatus as $loanStatus) {
                                                        $html .= "<option value='" . $loanStatus . "' " . ($sore->loanStatus == $loanStatus ? 'selected' : '') . ">" . $loanStatus . "</option>";
                                                    }
                                        $html .= "</select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('monthlyPayment') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('monthlyPayment', 'col-md-5 monthlyPayment_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('monthlyPayment') . " '
                                                    id='monthlyPayment_" . $loopId . "'
                                                    name='monthlyPayment[]'
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->monthlyPayment) . "'
                                                    " . loanForm::isEnabled('monthlyPayment') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('scheduleInterestRate') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('scheduleInterestRate', 'col-md-5 scheduleInterestRate_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('scheduleInterestRate') . " '
                                                    id='scheduleInterestRate_" . $loopId . "'
                                                    name='scheduleInterestRate[]'
                                                    value='" . $sore->scheduleInterestRate . "'
                                                    " . loanForm::isEnabled('scheduleInterestRate') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('unpaidBalance') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('unpaidBalance', 'col-md-5 unpaidBalance_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('unpaidBalance') . " '
                                                    id='unpaidBalance_" . $loopId . "'
                                                    name='unpaidBalance[]'
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->unpaidBalance) . "'
                                                    " . loanForm::isEnabled('unpaidBalance') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('type') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('type', 'col-md-5 type_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <select class='form-control " . loanForm::isMandatory('type') . " '
                                                    id='type_" . $loopId . "'
                                                    name='type[]'
                                                    " . loanForm::isEnabled('type') . ">
                                                    <option value=''>Select</option>";
                                                    foreach (gblType::$gblType as $type) {
                                                        $html .= "<option value='" . $type . "' " . ($sore->mortgageType == $type ? 'selected' : '') . ">" . $type . "</option>";
                                                    }
                                            $html .= "</select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('creditLimit') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('creditLimit', 'col-md-5 creditLimit_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('creditLimit') . " '
                                                    id='creditLimit_" . $loopId . "'
                                                    name='creditLimit[]' onblur='currencyConverter(this, this.value)'
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->creditLimit) . "'
                                                    " . loanForm::isEnabled('creditLimit') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('paidAtOrBeforeClose') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('paidAtOrBeforeClose', 'col-md-5 paidAtOrBeforeClose_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <span class='switch switch-icon'>
                                                        <label>
                                                            <input type='checkbox'
                                                            class='switch' id='paidAtOrBeforeClose_" . $loopId . "'
                                                            name='paidAtOrBeforeClose[]' value='1' " . $isPaidAtOrBeforeCloseChecked . "
                                                            " . loanForm::isEnabled('paidAtOrBeforeClose') . " >
                                                            <span></span>
                                                        </label>                                                    
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('maturityDateSchedule') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('maturityDateSchedule', 'col-md-5 maturityDateSchedule_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <div class='input-group maturityDateSchedule'>
                                                        <div class='input-group-prepend'>
                                                            <span class='input-group-text maturityDateSchedule_" . $loopId . "'>
                                                                <i class='fa fa-calendar text-primary'></i>
                                                            </span>
                                                        </div>
                                                        <input type='text'
                                                        class='form-control dateNewClass" . loanForm::isMandatory('maturityDateSchedule') . " '
                                                        id='maturityDateSchedule_" . $loopId . "'
                                                        name='maturityDateSchedule[]'
                                                        value='" . Dates::formatDateWithRE($sore->maturityDateSchedule, 'YMD', 'm/d/Y') . "'
                                                        " . loanForm::isEnabled('maturityDateSchedule') . "
                                                        >
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class='col-md-12 mortgageInfo_" . $loopId . "' style='" . $mortgageDisplay . "'>
                                    <div class='row'>
                                        <div class='col-md-6 " . loanForm::showField('anyOtherMortgagesLiens') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('anyOtherMortgagesLiens', 'col-md-5 anyOtherMortgagesLiens_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <div class='radio-inline'>
                                                        <label class='radio radio-solid font-weight-bold' for='anyOtherMortgagesLiensYes_" . $loopId . "'>
                                                            <input type='radio'
                                                            class='" . loanForm::isMandatory('anyOtherMortgagesLiens') . "'
                                                            id='anyOtherMortgagesLiensYes_" . $loopId . "'
                                                            name='anyOtherMortgagesLiens_" . $loopId . "[]' value='Yes' " . ($sore->anyOtherMortgagesLiens == 'Yes' ? 'checked' : '') . "
                                                            " . loanForm::isEnabled('anyOtherMortgagesLiens') . "  >
                                                            <span></span>Yes
                                                        </label>
                                                        <label class='radio radio-solid font-weight-bold' for='anyOtherMortgagesLiensNo_" . $loopId . "'>
                                                            <input type='radio'
                                                            class='" . loanForm::isMandatory('anyOtherMortgagesLiens') . "'
                                                            id='anyOtherMortgagesLiensNo_" . $loopId . "'
                                                            name='anyOtherMortgagesLiens_" . $loopId . "[]' value='No' " . ($sore->anyOtherMortgagesLiens == 'No' ? 'checked' : '') . "
                                                            " . loanForm::isEnabled('anyOtherMortgagesLiens') . "  >
                                                            <span></span>No
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-12'></div>
                                        <div class='col-md-6 " . loanForm::showField('creditorNameAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('creditorNameAnother', 'col-md-5 creditorNameAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('creditorNameAnother') . " '
                                                    id='creditorNameAnother_" . $loopId . "'
                                                    name='creditorNameAnother[]'
                                                    value='" . $sore->creditorNameAnother . "'
                                                    " . loanForm::isEnabled('creditorNameAnother') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('accountNumberAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('accountNumberAnother', 'col-md-5 accountNumberAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('accountNumberAnother') . " '
                                                    id='accountNumberAnother_" . $loopId . "'
                                                    name='accountNumberAnother[]'
                                                    value='" . $sore->accountNumberAnother . "'
                                                    " . loanForm::isEnabled('accountNumberAnother') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('loanStatusAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('loanStatusAnother', 'col-md-5 loanStatusAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <select class='form-control " . loanForm::isMandatory('loanStatusAnother') . " '
                                                    id='loanStatusAnother_" . $loopId . "'
                                                    name='loanStatusAnother[]'
                                                    " . loanForm::isEnabled('loanStatusAnother') . ">
                                                    <option value=''>Select</option>";
                                                    foreach (gblLoanStatus::$gblLoanStatus as $loanStatus) {
                                                        $html .= "<option value='" . $loanStatus . "' " . ($sore->loanStatusAnother == $loanStatus ? 'selected' : '') . ">" . $loanStatus . "</option>";
                                                    }
                                                    $html .= "</select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('monthlyPaymentAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('monthlyPaymentAnother', 'col-md-5 monthlyPaymentAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('monthlyPaymentAnother') . " '
                                                    id='monthlyPaymentAnother_" . $loopId . "'
                                                    name='monthlyPaymentAnother[]'
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->monthlyPaymentAnother) . "'
                                                    " . loanForm::isEnabled('monthlyPaymentAnother') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('scheduleInterestRateAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('scheduleInterestRateAnother', 'col-md-5 scheduleInterestRateAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('scheduleInterestRateAnother') . " '
                                                    id='scheduleInterestRateAnother_" . $loopId . "'
                                                    name='scheduleInterestRateAnother[]'
                                                    value='" . $sore->scheduleInterestRateAnother . "'
                                                    " . loanForm::isEnabled('scheduleInterestRateAnother') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('unpaidBalanceAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('unpaidBalanceAnother', 'col-md-5 unpaidBalanceAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('unpaidBalanceAnother') . " '
                                                    id='unpaidBalanceAnother_" . $loopId . "'
                                                    name='unpaidBalanceAnother[]'
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->unpaidBalanceAnother) . "'
                                                    " . loanForm::isEnabled('unpaidBalanceAnother') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('typeAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('typeAnother', 'col-md-5 typeAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <select class='form-control " . loanForm::isMandatory('typeAnother') . " '
                                                    id='typeAnother_" . $loopId . "'
                                                    name='typeAnother[]'
                                                    " . loanForm::isEnabled('typeAnother') . ">
                                                    <option value=''>Select</option>";
                                                    foreach (gblType::$gblType as $type) {
                                                        $html .= "<option value='" . $type . "' " . ($sore->typeAnother == $type ? 'selected' : '') . ">" . $type . "</option>";
                                                    }
                                                    $html .= "</select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('creditLimitAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('creditLimitAnother', 'col-md-5 creditLimitAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <input type='text'
                                                    class='form-control " . loanForm::isMandatory('creditLimitAnother') . " '
                                                    id='creditLimitAnother_" . $loopId . "'
                                                    name='creditLimitAnother[]' onblur='currencyConverter(this, this.value)'
                                                    value='" . Currency::formatDollarAmountWithDecimal($sore->creditLimitAnother) . "'
                                                    " . loanForm::isEnabled('creditLimitAnother') . "
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('paidAtOrBeforeCloseAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('paidAtOrBeforeCloseAnother', 'col-md-5 paidAtOrBeforeCloseAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <span class='switch switch-icon'>
                                                        <label>
                                                            <input type='checkbox'
                                                            class='switch' id='paidAtOrBeforeCloseAnother_" . $loopId . "'
                                                            name='paidAtOrBeforeCloseAnother[]' value='1' " . $isPaidAtOrBeforeCloseAnotherChecked . "
                                                            " . loanForm::isEnabled('paidAtOrBeforeCloseAnother') . " >
                                                            <span></span>
                                                        </label>                                                    
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='col-md-6 " . loanForm::showField('maturityDateAnother') . " '>
                                            <div class='form-group row'>
                                            " . loanForm::label('maturityDateAnother', 'col-md-5 maturityDateAnother_' . $loopId) . "
                                                <div class='col-md-7'>
                                                    <div class='input-group maturityDateAnother'>
                                                        <div class='input-group-prepend'>
                                                            <span class='input-group-text maturityDateAnother_" . $loopId . "'>
                                                                <i class='fa fa-calendar text-primary'></i>
                                                            </span>
                                                        </div>
                                                        <input type='text'
                                                        class='form-control dateNewClass" . loanForm::isMandatory('maturityDateAnother') . " '
                                                        id='maturityDateAnother_" . $loopId . "'
                                                        name='maturityDateAnother[]'
                                                        value='" . Dates::formatDateWithRE($sore->maturityDateAnother, 'YMD', 'm/d/Y') . "'
                                                        " . loanForm::isEnabled('maturityDateAnother') . "
                                                        >
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>    
                        </div>            
                        
</div>";
            $loopId++;
        }

        return $html;
    }

}
